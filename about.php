<?php
/**
 * GNG Makine - Hakkımızda Sayfası
 */

// Konfigürasyonu dahil et
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

// Sayfa meta bilgileri
$page_title = "Hakkımızda - " . SITE_NAME;
$page_description = "GNG Makine olarak 15+ yıllık tecrübemizle endüstriyel makine çözümleri sunuyoruz. Misyonumuz, vizyonumuz ve değerlerimizi keşfedin.";
$page_keywords = "GNG Makine, hakkımızda, endüstriyel makine, tecrübe, kalite, misyon, vizyon";
$current_page = "about";

// Breadcrumb
$breadcrumb = [
    ['title' => 'Ana Sayfa', 'url' => BASE_URL],
    ['title' => 'Hakkımızda', 'url' => '']
];

// İstatistikler
$stats = [
    ['number' => '15+', 'label' => 'Yıllık Tecrübe'],
    ['number' => '500+', 'label' => 'Mutlu Müşteri'],
    ['number' => '1000+', 'label' => 'Tamamlanan Proje'],
    ['number' => '24/7', 'label' => 'Teknik Destek']
];

// Değerlerimiz
$values = [
    [
        'icon' => 'fas fa-award',
        'title' => 'Kalite',
        'description' => 'En yüksek kalite standartlarında ürün ve hizmet sunuyoruz.'
    ],
    [
        'icon' => 'fas fa-handshake',
        'title' => 'Güvenilirlik',
        'description' => 'Müşterilerimizle uzun vadeli güven ilişkileri kuruyoruz.'
    ],
    [
        'icon' => 'fas fa-lightbulb',
        'title' => 'İnovasyon',
        'description' => 'Sürekli gelişim ve yenilikçi çözümlerle sektöre öncülük ediyoruz.'
    ],
    [
        'icon' => 'fas fa-users',
        'title' => 'Müşteri Odaklılık',
        'description' => 'Müşteri memnuniyeti bizim için en önemli önceliktir.'
    ],
    [
        'icon' => 'fas fa-shield-alt',
        'title' => 'Güvenlik',
        'description' => 'İş güvenliği ve çevre koruma konularında hassasiyetle çalışıyoruz.'
    ],
    [
        'icon' => 'fas fa-clock',
        'title' => 'Zamanında Teslimat',
        'description' => 'Projelerimizi belirlenen sürede ve kalitede teslim ediyoruz.'
    ]
];

include 'includes/header.php';
?>

<style>
/* Hakkımızda Sayfası Özel Stilleri */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.about-hero {
    background: linear-gradient(135deg, var(--text-dark) 0%, #34495e 100%);
    padding: 120px 0 80px;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero-content h1 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 50px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    margin-top: 60px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    display: block;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 1rem;
    color: #95a5a6;
    margin-top: 5px;
}

/* Story Section */
.story-section {
    padding: 100px 0;
    background: var(--white);
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.story-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 30px;
    line-height: 1.2;
}

.story-text p {
    font-size: 1.1rem;
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 25px;
}

.story-visual {
    position: relative;
}

.story-image {
    width: 100%;
    height: 500px;
    background: var(--bg-light);
    border-radius: 20px;
    overflow: hidden;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: var(--primary-color);
}

.story-badge {
    position: absolute;
    bottom: -20px;
    right: -20px;
    background: var(--primary-color);
    color: var(--white);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(243, 156, 18, 0.3);
}

.story-badge .badge-number {
    font-size: 2rem;
    font-weight: 800;
    display: block;
}

.story-badge .badge-text {
    font-size: 0.9rem;
    font-weight: 600;
}

/* Values Section */
.values-section {
    padding: 100px 0;
    background: var(--bg-light);
}

.section-title {
    text-align: center;
    margin-bottom: 80px;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.section-title p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.value-card {
    background: var(--white);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.value-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.value-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: 0 20px 40px rgba(243, 156, 18, 0.2);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    color: var(--white);
    font-size: 2rem;
}

.value-card h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.value-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Mission Vision Section */
.mission-vision {
    padding: 100px 0;
    background: var(--white);
}

.mv-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.mv-card {
    background: linear-gradient(135deg, var(--text-dark), #34495e);
    padding: 50px 40px;
    border-radius: 20px;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.mv-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0.1;
    z-index: 1;
}

.mv-content {
    position: relative;
    z-index: 2;
}

.mv-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 25px;
}

.mv-card h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.mv-card p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #bdc3c7;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.btn-primary {
    background: var(--white);
    color: var(--primary-color);
    border: 2px solid var(--white);
}

.btn-primary:hover {
    background: transparent;
    color: var(--white);
}

.btn-outline {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-outline:hover {
    background: var(--white);
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .story-content {
        grid-template-columns: 1fr;
        gap: 50px;
    }

    .story-content .story-visual {
        order: -1;
    }

    .story-badge {
        bottom: 20px;
        right: 20px;
    }

    .mv-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}
</style>

<?php include 'includes/navbar.php'; ?>

<!-- Hero Section -->
<section class="about-hero">
    <div class="container">
        <div class="hero-content">
            <h1>Hakkımızda</h1>
            <p>15 yılı aşkın tecrübemizle endüstriyel makine sektöründe güvenilir çözüm ortağınızız. Kalite, güvenilirlik ve müşteri memnuniyeti odaklı hizmet anlayışımızla sektörde öncü konumdayız.</p>

            <div class="hero-stats">
                <?php foreach ($stats as $stat): ?>
                <div class="stat-item">
                    <span class="stat-number"><?php echo $stat['number']; ?></span>
                    <div class="stat-label"><?php echo $stat['label']; ?></div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<!-- Story Section -->
<section class="story-section">
    <div class="container">
        <div class="story-content">
            <div class="story-text">
                <h2>Hikayemiz</h2>
                <p>GNG Makine, 2008 yılında endüstriyel makine sektöründe kaliteli ve güvenilir çözümler sunmak amacıyla kuruldu. Kuruluşumuzdan bu yana, müşterilerimizin üretim süreçlerini optimize etmek ve verimliliği artırmak için çalışıyoruz.</p>

                <p>Sektördeki derin tecrübemiz ve sürekli gelişim anlayışımız sayesinde, bugün Türkiye'nin önde gelen makine tedarikçilerinden biri haline geldik. Modern teknoloji ile geleneksel kalite anlayışını birleştirerek, müşterilerimize en iyi hizmeti sunmaya devam ediyoruz.</p>

                <p>Uzman ekibimiz, geniş ürün yelpazemiz ve güçlü satış sonrası destek hizmetlerimizle, her zaman yanınızdayız.</p>
            </div>

            <div class="story-visual">
                <div class="story-image">
                    <i class="fas fa-industry"></i>
                </div>
                <div class="story-badge">
                    <span class="badge-number">15+</span>
                    <span class="badge-text">Yıllık Tecrübe</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Values Section -->
<section class="values-section">
    <div class="container">
        <div class="section-title">
            <h2>Değerlerimiz</h2>
            <p>İş yapış şeklimizi belirleyen temel değerlerimiz, müşterilerimize sunduğumuz hizmetin kalitesini garanti eder.</p>
        </div>

        <div class="values-grid">
            <?php foreach ($values as $value): ?>
            <div class="value-card">
                <div class="value-icon">
                    <i class="<?php echo $value['icon']; ?>"></i>
                </div>
                <h3><?php echo $value['title']; ?></h3>
                <p><?php echo $value['description']; ?></p>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Mission Vision Section -->
<section class="mission-vision">
    <div class="container">
        <div class="mv-grid">
            <div class="mv-card">
                <div class="mv-content">
                    <div class="mv-icon">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <h3>Misyonumuz</h3>
                    <p>Endüstriyel makine sektöründe en kaliteli ürünleri ve hizmetleri sunarak, müşterilerimizin üretim verimliliğini artırmak ve rekabet gücünü güçlendirmek. Sürekli gelişim ve yenilikçi yaklaşımlarla sektöre değer katmak.</p>
                </div>
            </div>

            <div class="mv-card">
                <div class="mv-content">
                    <div class="mv-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3>Vizyonumuz</h3>
                    <p>Türkiye'nin ve bölgenin önde gelen endüstriyel makine tedarikçisi olmak. Teknoloji ve kalite odaklı hizmet anlayışımızla, müşterilerimizin tercih ettiği güvenilir iş ortağı konumunda bulunmak.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <h2>Bizimle İletişime Geçin</h2>
            <p>Endüstriyel makine ihtiyaçlarınız için uzman ekibimizden destek alın. Size en uygun çözümleri sunmaya hazırız.</p>

            <div class="cta-buttons">
                <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-primary">
                    <i class="fas fa-envelope"></i>
                    İletişime Geç
                </a>
                <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-outline">
                    <i class="fas fa-search"></i>
                    Ürünleri İncele
                </a>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
