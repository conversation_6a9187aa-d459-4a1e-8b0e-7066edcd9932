<?php
/**
 * GNG Makine - Hakkımızda Sayfası
 */

// Konfigürasyonu dahil et
require_once __DIR__ . '/config/config.php';

// Sayfa meta bilgileri
$page_title = "Hakkımızda - " . SITE_NAME;
$page_description = "GNG Makine olarak 15+ yıllık tecrübemizle endüstriyel makine çözümleri sunuyoruz. Misyonumuz, vizyonumuz ve değerlerimizi keşfedin.";
$page_keywords = "GNG Makine, hakkımızda, endüstriyel makine, tecrübe, kalite, misyon, vizyon";
$current_page = "about";

// Breadcrumb
$breadcrumb = [
    ['title' => 'Hakkımızda']
];

// İstatistikler
$stats = [
    'experience' => '15+',
    'projects' => '500+',
    'clients' => '150+',
    'team' => '25+'
];

// Ekip üyeleri (örnek veriler)
$team_members = [
    [
        'name' => 'Ahmet Kaya',
        'position' => 'Genel Müdür',
        'description' => '15 yıllık endüstri tecrübesi ile şirketimizi yönetiyor.',
        'image' => ASSETS_URL . '/images/team/member1.jpg',
        'linkedin' => '#',
        'email' => '<EMAIL>'
    ],
    [
        'name' => 'Mehmet Demir',
        'position' => 'Teknik Müdür',
        'description' => 'Makine mühendisliği alanında uzman, Ar-Ge departmanı lideri.',
        'image' => ASSETS_URL . '/images/team/member2.jpg',
        'linkedin' => '#',
        'email' => '<EMAIL>'
    ],
    [
        'name' => 'Fatma Yılmaz',
        'position' => 'Satış Müdürü',
        'description' => 'Müşteri ilişkileri ve satış stratejileri konusunda uzman.',
        'image' => ASSETS_URL . '/images/team/member3.jpg',
        'linkedin' => '#',
        'email' => '<EMAIL>'
    ]
];
// Header'ı dahil et
include 'includes/header.php';
?>

<style>
/* Hakkımızda Sayfası Özel Stilleri */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.about-hero {
    background: linear-gradient(135deg, var(--text-dark) 0%, #34495e 100%);
    padding: 120px 0 80px;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.about-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('<?php echo ASSETS_URL; ?>/images/backgrounds/about-pattern.svg') no-repeat center;
    background-size: cover;
    opacity: 0.1;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-content .highlight {
    color: var(--primary-color);
}

.hero-content p {
    font-size: 1.25rem;
    color: #bdc3c7;
    margin-bottom: 40px;
    line-height: 1.6;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-color);
    display: block;
}

.stat-label {
    font-size: 1rem;
    color: #95a5a6;
    margin-top: 5px;
}

/* Story Section */
.story-section {
    padding: 100px 0;
    background: var(--white);
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.story-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 30px;
    line-height: 1.2;
}

.story-text p {
    font-size: 1.1rem;
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 25px;
}

.story-visual {
    position: relative;
}

.story-image {
    width: 100%;
    height: 500px;
    background: var(--bg-light);
    border-radius: 20px;
    overflow: hidden;
    position: relative;
}

.story-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.story-badge {
    position: absolute;
    bottom: -20px;
    right: -20px;
    background: var(--primary-color);
    color: var(--white);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(243, 156, 18, 0.3);
}

.story-badge .badge-number {
    font-size: 2rem;
    font-weight: 800;
    display: block;
}

.story-badge .badge-text {
    font-size: 0.9rem;
    font-weight: 600;
}

/* Values Section */
.values-section {
    padding: 100px 0;
    background: var(--bg-light);
}

.section-title {
    text-align: center;
    margin-bottom: 80px;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.section-title p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.value-card {
    background: var(--white);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.value-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.value-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: 0 20px 40px rgba(243, 156, 18, 0.2);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    color: var(--white);
    font-size: 2rem;
}

.value-card h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.value-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Mission Vision Section */
.mission-vision {
    padding: 100px 0;
    background: var(--white);
}

.mv-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.mv-card {
    background: linear-gradient(135deg, var(--text-dark), #34495e);
    padding: 50px 40px;
    border-radius: 20px;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.mv-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0.1;
    z-index: 1;
}

.mv-content {
    position: relative;
    z-index: 2;
}

.mv-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 25px;
}

.mv-card h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.mv-card p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #bdc3c7;
}

/* Team Section */
.team-section {
    padding: 100px 0;
    background: var(--bg-light);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.team-card {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.team-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.team-image {
    height: 300px;
    background: var(--bg-light);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.team-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.team-placeholder {
    width: 100px;
    height: 100px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 3rem;
    font-weight: 700;
}

.team-info {
    padding: 30px;
    text-align: center;
}

.team-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 5px;
}

.team-position {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 15px;
}

.team-description {
    color: var(--text-light);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 20px;
}

.team-social {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-btn {
    width: 40px;
    height: 40px;
    background: var(--bg-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: var(--text-light);
    transition: all 0.3s ease;
}

.social-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.btn-primary {
    background: var(--white);
    color: var(--primary-color);
    border: 2px solid var(--white);
}

.btn-primary:hover {
    background: transparent;
    color: var(--white);
}

.btn-outline {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-outline:hover {
    background: var(--white);
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .story-content {
        grid-template-columns: 1fr;
        gap: 50px;
    }

    .story-content .story-visual {
        order: -1;
    }

    .story-badge {
        bottom: 20px;
        right: 20px;
    }

    .mv-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}

        /* Story Section */
        .story-section {
            padding: 100px 0;
            background: var(--white);
        }

        .story-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
        }

        .story-text h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--secondary-color);
            margin-bottom: 30px;
            line-height: 1.2;
        }

        .story-text p {
            font-size: 1.1rem;
            color: var(--text-light);
            line-height: 1.8;
            margin-bottom: 25px;
        }

        .story-text p:last-child {
            margin-bottom: 0;
        }

        .story-visual {
            position: relative;
        }

        .story-image {
            width: 100%;
            height: 500px;
            background: var(--bg-light);
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }

        .story-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .story-badge {
            position: absolute;
            bottom: -20px;
            right: -20px;
            background: var(--primary-color);
            color: var(--white);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(243, 156, 18, 0.3);
        }

        .story-badge .badge-number {
            font-size: 2rem;
            font-weight: 800;
            display: block;
        }

        .story-badge .badge-text {
            font-size: 0.9rem;
            font-weight: 600;
        }

        /* Values Section */
        .values-section {
            padding: 100px 0;
            background: var(--bg-light);
        }

        .section-title {
            text-align: center;
            margin-bottom: 80px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .section-title p {
            font-size: 1.1rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        .values-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .value-card {
            background: var(--white);
            padding: 40px 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .value-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        }

        .value-card:hover {
            transform: translateY(-10px);
            border-color: var(--primary-color);
            box-shadow: 0 20px 40px rgba(243, 156, 18, 0.2);
        }

        .value-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            color: var(--white);
            font-size: 2rem;
        }

        .value-card h3 {
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 15px;
        }

        .value-card p {
            color: var(--text-light);
            line-height: 1.6;
        }

        /* Mission Vision Section */
        .mission-vision {
            padding: 100px 0;
            background: var(--white);
        }

        .mv-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
        }

        .mv-card {
            background: linear-gradient(135deg, var(--secondary-color), #34495e);
            padding: 50px 40px;
            border-radius: 20px;
            color: var(--white);
            position: relative;
            overflow: hidden;
        }

        .mv-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: var(--primary-color);
            border-radius: 50%;
            opacity: 0.1;
            z-index: 1;
        }

        .mv-content {
            position: relative;
            z-index: 2;
        }

        .mv-icon {
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 25px;
        }

        .mv-card h3 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .mv-card p {
            font-size: 1.1rem;
            line-height: 1.7;
            color: #bdc3c7;
        }

        /* Team Section */
        .team-section {
            padding: 100px 0;
            background: var(--bg-light);
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .team-card {
            background: var(--white);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .team-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .team-image {
            height: 300px;
            background: var(--bg-light);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .team-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .team-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.3));
            z-index: 1;
        }

        .team-placeholder {
            width: 100px;
            height: 100px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 3rem;
            font-weight: 700;
        }

        .team-info {
            padding: 30px;
            text-align: center;
        }

        .team-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 5px;
        }

        .team-position {
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 15px;
        }

        .team-description {
            color: var(--text-light);
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .team-social {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .social-btn {
            width: 40px;
            height: 40px;
            background: var(--bg-light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--text-light);
            transition: all 0.3s ease;
        }

        .social-btn:hover {
            background: var(--primary-color);
            color: var(--white);
            transform: translateY(-2px);
        }

        /* CTA Section */
        .cta-section {
            padding: 80px 0;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: var(--white);
            text-align: center;
        }

        .cta-content h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .cta-content p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-primary {
            background: var(--white);
            color: var(--primary-color);
            border: 2px solid var(--white);
        }

        .btn-primary:hover {
            background: transparent;
            color: var(--white);
        }

        .btn-outline {
            background: transparent;
            color: var(--white);
            border: 2px solid var(--white);
        }

        .btn-outline:hover {
            background: var(--white);
            color: var(--primary-color);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-content h1 {
                font-size: 2.5rem;
            }

            .hero-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 30px;
            }

            .story-content {
                grid-template-columns: 1fr;
                gap: 50px;
            }

            .story-content .story-visual {
                order: -1;
            }

            .story-badge {
                bottom: 20px;
                right: 20px;
            }

            .mv-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .section-title h2 {
                font-size: 2rem;
            }

            .cta-content h2 {
                font-size: 2rem;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
</style>

    <!-- Hero Section -->
    <section class="about-hero">
        <div class="container">
            <div class="hero-content">
                <h1>Biz <span class="highlight">Kimiz?</span></h1>
                <p>15 yılı aşkın tecrübemizle endüstriyel makine sektöründe öncü bir firma olarak, kaliteli üretim ve müşteri memnuniyetini ön planda tutarak hizmet veriyoruz.</p>
                
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $stats['experience']; ?></span>
                        <span class="stat-label">Yıl Tecrübe</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $stats['projects']; ?></span>
                        <span class="stat-label">Tamamlanan Proje</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $stats['clients']; ?></span>
                        <span class="stat-label">Mutlu Müşteri</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $stats['team']; ?></span>
                        <span class="stat-label">Uzman Ekip</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Story Section -->
    <section class="story-section">
        <div class="container">
            <div class="story-content">
                <div class="story-text">
                    <h2>Hikayemiz</h2>
                    <p>GNG Makine, 2008 yılında Gaziantep'te kurulmuş, endüstriyel makine üretimi ve satışı alanında faaliyet gösteren bir şirkettir. Kuruluşumuzdan bu yana, kaliteli ürün ve hizmet anlayışımızla sektörde önemli bir konuma gelmiş bulunmaktayız.</p>
                    
                    <p>Müşterilerimizin ihtiyaçlarına yönelik özel çözümler geliştirmek, en son teknolojileri kullanarak verimli ve dayanıklı makineler üretmek temel prensiplerimizdir. Sürekli gelişim ve yenilik odaklı yaklaşımımızla, sektördeki yerimi güçlendirmeye devam ediyoruz.</p>
                    
                    <p>Bugün, 25 kişilik uzman ekibimizle birlikte, Türkiye'nin dört bir yanına ve yurt dışına kaliteli makine çözümleri sunmaktayız. Geleceğe güvenle bakıyor, teknolojik gelişmeleri yakından takip ederek müşterilerimize daha iyi hizmet vermek için çalışmalarımızı sürdürüyoruz.</p>
                </div>
                
                <div class="story-visual">
                    <div class="story-image">
                        <img src="<?php echo ASSETS_URL; ?>/images/about/story-image.jpg" alt="GNG Makine Hikayesi" loading="lazy">
                    </div>
                    <div class="story-badge">
                        <span class="badge-number">2008</span>
                        <span class="badge-text">Kuruluş Yılı</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Values Section -->
    <section class="values-section">
        <div class="container">
            <div class="section-title">
                <h2>Değerlerimiz</h2>
                <p>İş süreçlerimizde ve müşteri ilişkilerimizde rehber aldığımız temel değerlerimiz</p>
            </div>
            
            <div class="values-grid">
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-medal"></i>
                    </div>
                    <h3>Kalite</h3>
                    <p>Ürettiğimiz her makinede en yüksek kalite standartlarını uygular, müşterilerimize uzun ömürlü ve güvenilir çözümler sunarız.</p>
                </div>
                
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3>Yenilik</h3>
                    <p>Teknolojik gelişmeleri yakından takip eder, AR-GE çalışmalarımızla sektöre yenilikçi çözümler kazandırırız.</p>
                </div>
                
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3>Güvenilirlik</h3>
                    <p>Müşterilerimizle kurduğumuz güven ilişkisi, uzun vadeli iş birliklerimizin temelini oluşturur.</p>
                </div>
                
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Ekip Çalışması</h3>
                    <p>Güçlü ekip ruhumuz sayesinde projeleri başarıyla tamamlar, müşteri memnuniyetini sağlarız.</p>
                </div>
                
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <h3>Sürdürülebilirlik</h3>
                    <p>Çevre dostu üretim süreçleri benimser, gelecek nesillere yaşanabilir bir dünya bırakmayı hedefleriz.</p>
                </div>
                
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>Zamanında Teslimat</h3>
                    <p>Proje sürelerinde disiplinli yaklaşımımızla, taahhüt ettiğimiz tarihlerde teslimat gerçekleştiririz.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Mission & Vision -->
    <section class="mission-vision">
        <div class="container">
            <div class="mv-grid">
                <div class="mv-card">
                    <div class="mv-content">
                        <div class="mv-icon">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <h3>Misyonumuz</h3>
                        <p>Endüstriyel makine sektöründe müşterilerimizin ihtiyaçlarına en uygun, kaliteli ve teknolojik çözümler sunarak, üretim süreçlerinde verimlilik artışı sağlamak ve iş ortaklarımızın başarısına katkıda bulunmaktır.</p>
                    </div>
                </div>
                
                <div class="mv-card">
                    <div class="mv-content">
                        <div class="mv-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h3>Vizyonumuz</h3>
                        <p>Teknolojik yenilikleri yakından takip eden, sürdürülebilir büyüme odaklı, ulusal ve uluslararası arenada tanınan, sektörün öncü ve güvenilir markası olmaktır.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="team-section">
        <div class="container">
            <div class="section-title">
                <h2>Ekibimiz</h2>
                <p>Deneyimli ve uzman kadromuzla sizlere en iyi hizmeti sunmak için çalışıyoruz</p>
            </div>
            
            <div class="team-grid">
                <?php foreach ($team_members as $member): ?>
                    <div class="team-card">
                        <div class="team-image">
                            <div class="team-placeholder">
                                <?php echo strtoupper(substr($member['name'], 0, 1)); ?>
                            </div>
                        </div>
                        <div class="team-info">
                            <h4 class="team-name"><?php echo htmlspecialchars($member['name']); ?></h4>
                            <p class="team-position"><?php echo htmlspecialchars($member['position']); ?></p>
                            <p class="team-description"><?php echo htmlspecialchars($member['description']); ?></p>
                            <div class="team-social">
                                <a href="<?php echo htmlspecialchars($member['linkedin']); ?>" class="social-btn" title="LinkedIn">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                <a href="mailto:<?php echo htmlspecialchars($member['email']); ?>" class="social-btn" title="E-posta">
                                    <i class="fas fa-envelope"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Birlikte Çalışmaya Hazır mısınız?</h2>
                <p>Projeleriniz için en uygun makine çözümlerini birlikte keşfedelim.</p>
                <div class="cta-buttons">
                    <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-primary">
                        <i class="fas fa-phone"></i>
                        İletişime Geçin
                    </a>
                    <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-outline">
                        <i class="fas fa-search"></i>
                        Ürünleri İnceleyin
                    </a>
                </div>
            </div>
        </div>
    </section>

</script>

    <script>
        // Hakkımızda sayfası özel JavaScript kodları
        document.addEventListener('DOMContentLoaded', function() {
            // Counter animation for stats
            const statsNumbers = document.querySelectorAll('.stat-number');
            const observerOptions = {
                threshold: 0.5,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = entry.target;
                        const finalText = target.textContent;
                        const finalNumber = parseInt(finalText);
                        
                        if (!isNaN(finalNumber)) {
                            animateCounter(target, finalNumber, finalText);
                        }
                        observer.unobserve(target);
                    }
                });
            }, observerOptions);

            statsNumbers.forEach(stat => {
                observer.observe(stat);
            });

            function animateCounter(element, target, originalText) {
                let current = 0;
                const increment = target / 50;
                const suffix = originalText.replace(/\d+/g, '');
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    element.textContent = Math.floor(current) + suffix;
                }, 30);
            }

            // Parallax effect for hero section
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const heroSection = document.querySelector('.about-hero');
                if (heroSection) {
                    const rate = scrolled * -0.3;
                    heroSection.style.transform = `translateY(${rate}px)`;
                }
            });

            // Fade in animation for sections
            const sections = document.querySelectorAll('.story-section, .values-section, .mission-vision, .team-section');
            const sectionObserver = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -100px 0px'
            });

            sections.forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(50px)';
                section.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                sectionObserver.observe(section);
            });

            // Value cards hover effect
            const valueCards = document.querySelectorAll('.value-card');
            valueCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-15px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Team cards stagger animation
            const teamCards = document.querySelectorAll('.team-card');
            const teamObserver = new IntersectionObserver(function(entries) {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 200);
                        teamObserver.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.2
            });

            teamCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                teamObserver.observe(card);
            });

            // Mission/Vision cards animation
            const mvCards = document.querySelectorAll('.mv-card');
            const mvObserver = new IntersectionObserver(function(entries) {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateX(0)';
                        }, index * 300);
                        mvObserver.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.3
            });

            mvCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = index === 0 ? 'translateX(-50px)' : 'translateX(50px)';
                card.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                mvObserver.observe(card);
            });

            // Smooth scroll for CTA buttons
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    if (this.getAttribute('href').startsWith('#')) {
                        e.preventDefault();
                        const target = document.querySelector(this.getAttribute('href'));
                        if (target) {
                            target.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    }
                });
            });

            // Story image lazy loading effect
            const storyImage = document.querySelector('.story-image img');
            if (storyImage) {
                storyImage.addEventListener('load', function() {
                    this.style.opacity = '1';
                    this.style.transform = 'scale(1)';
                });
                
                storyImage.style.opacity = '0';
                storyImage.style.transform = 'scale(1.1)';
                storyImage.style.transition = 'opacity 1s ease, transform 1s ease';
            }

            // Add hover effect to social buttons
            document.querySelectorAll('.social-btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.1)';
                });
                
                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // CTA section entrance animation
            const ctaSection = document.querySelector('.cta-section');
            const ctaObserver = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const content = entry.target.querySelector('.cta-content');
                        content.style.opacity = '1';
                        content.style.transform = 'translateY(0)';
                        ctaObserver.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.3
            });

            if (ctaSection) {
                const ctaContent = ctaSection.querySelector('.cta-content');
                ctaContent.style.opacity = '0';
                ctaContent.style.transform = 'translateY(30px)';
                ctaContent.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                ctaObserver.observe(ctaSection);
            }
        });
    </script>

<?php include 'includes/footer.php'; ?>