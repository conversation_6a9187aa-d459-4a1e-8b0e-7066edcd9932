<?php
require_once 'config/config.php';

// Sayfa bilgileri
$page_title = 'Akademi - GNG Makine';
$page_description = 'GNG Makine Akademi - Eğitim içerikleri, teknik makaleler ve uzman görüşleri';

// Sayfalama için parametreler
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 12;
$offset = ($page - 1) * $per_page;

// Kategori filtresi
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;

// Arama filtresi
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Kategorileri getir
try {
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE status = 'active' ORDER BY name ASC");
    $stmt->execute();
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $categories = [];
}

// <PERSON><PERSON>e sayısını hesapla
try {
    $count_sql = "SELECT COUNT(*) FROM articles WHERE status = 'published'";
    $count_params = [];
    
    if ($category_filter > 0) {
        $count_sql .= " AND category_id = ?";
        $count_params[] = $category_filter;
    }
    
    if (!empty($search)) {
        $count_sql .= " AND (title LIKE ? OR content LIKE ? OR excerpt LIKE ?)";
        $search_term = "%{$search}%";
        $count_params[] = $search_term;
        $count_params[] = $search_term;
        $count_params[] = $search_term;
    }
    
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($count_params);
    $total_articles = $stmt->fetchColumn();
    $total_pages = ceil($total_articles / $per_page);
} catch (PDOException $e) {
    $total_articles = 0;
    $total_pages = 1;
}

// Makaleleri getir
try {
    $sql = "SELECT a.*, c.name as category_name, c.color as category_color 
            FROM articles a 
            LEFT JOIN categories c ON a.category_id = c.id 
            WHERE a.status = 'published'";
    $params = [];
    
    if ($category_filter > 0) {
        $sql .= " AND a.category_id = ?";
        $params[] = $category_filter;
    }
    
    if (!empty($search)) {
        $sql .= " AND (a.title LIKE ? OR a.content LIKE ? OR a.excerpt LIKE ?)";
        $search_term = "%{$search}%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    $sql .= " ORDER BY a.created_at DESC LIMIT ? OFFSET ?";
    $params[] = $per_page;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $articles = $stmt->fetchAll();
} catch (PDOException $e) {
    $articles = [];
}

// Öne çıkan makaleler
try {
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, c.color as category_color 
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        WHERE a.status = 'published' AND a.featured = 1
        ORDER BY a.created_at DESC 
        LIMIT 3
    ");
    $stmt->execute();
    $featured_articles = $stmt->fetchAll();
} catch (PDOException $e) {
    $featured_articles = [];
}
?>

<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary: #F9B233;
            --secondary: #8A8C8F;
            --dark: #2C3E50;
            --light: #ECF0F1;
            --white: #FFFFFF;
            --text-dark: #2C3E50;
            --text-light: #7F8C8D;
            --shadow: 0 10px 30px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
            --border-radius: 15px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: var(--white);
            padding: 100px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Search and Filter Section */
        .search-filter {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 30px;
            margin: -40px auto 60px;
            max-width: 800px;
            box-shadow: var(--shadow);
            position: relative;
            z-index: 10;
        }

        .search-form {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-dark);
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(249, 178, 51, 0.1);
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
        }

        .btn-primary {
            background: var(--primary);
            color: var(--white);
        }

        .btn-primary:hover {
            background: #e6a02e;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: var(--secondary);
            color: var(--white);
        }

        .btn-secondary:hover {
            background: #7a7c7f;
        }

        /* Featured Articles */
        .featured-section {
            margin-bottom: 80px;
        }

        .section-title {
            text-align: center;
            margin-bottom: 50px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 15px;
        }

        .section-title p {
            font-size: 1.1rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        .featured-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }

        /* Article Cards */
        .article-card {
            background: var(--white);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            position: relative;
        }

        .article-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .article-image {
            height: 200px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            position: relative;
            overflow: hidden;
        }

        .article-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .article-card:hover .article-image img {
            transform: scale(1.1);
        }

        .article-category {
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(255, 255, 255, 0.9);
            color: var(--text-dark);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .article-content {
            padding: 25px;
        }

        .article-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            color: var(--text-light);
        }

        .article-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .article-title a {
            color: inherit;
            text-decoration: none;
            transition: var(--transition);
        }

        .article-title a:hover {
            color: var(--primary);
        }

        .article-excerpt {
            color: var(--text-light);
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .article-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }

        .article-link:hover {
            color: var(--secondary);
            transform: translateX(5px);
        }

        /* Articles Grid */
        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 60px;
        }

        .pagination a,
        .pagination span {
            padding: 10px 15px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }

        .pagination a {
            background: var(--white);
            color: var(--text-dark);
            border: 2px solid #e9ecef;
        }

        .pagination a:hover {
            background: var(--primary);
            color: var(--white);
            border-color: var(--primary);
        }

        .pagination .current {
            background: var(--primary);
            color: var(--white);
            border: 2px solid var(--primary);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: var(--text-light);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero p {
                font-size: 1.1rem;
            }
            
            .search-form {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .articles-grid,
            .featured-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .section-title h2 {
                font-size: 2rem;
            }
            
            .pagination {
                flex-wrap: wrap;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .article-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .article-card:nth-child(2) { animation-delay: 0.1s; }
        .article-card:nth-child(3) { animation-delay: 0.2s; }
        .article-card:nth-child(4) { animation-delay: 0.3s; }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1><i class="fas fa-graduation-cap"></i> Akademi</h1>
            <p>Eğitim içerikleri, teknik makaleler ve uzman görüşleri ile bilginizi artırın</p>
        </div>
    </section>

    <!-- Search and Filter -->
    <div class="container">
        <div class="search-filter">
            <form method="GET" class="search-form">
                <div class="form-group">
                    <label for="search">Arama</label>
                    <input type="text" id="search" name="search" class="form-control" 
                           placeholder="Makale ara..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="form-group">
                    <label for="category">Kategori</label>
                    <select id="category" name="category" class="form-control">
                        <option value="0">Tüm Kategoriler</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" 
                                    <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Ara
                </button>
                <?php if (!empty($search) || $category_filter > 0): ?>
                    <a href="academy.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Temizle
                    </a>
                <?php endif; ?>
            </form>
        </div>
    </div>

    <!-- Main Content -->
    <main class="container">
        <?php if (empty($search) && $category_filter == 0 && !empty($featured_articles)): ?>
            <!-- Featured Articles -->
            <section class="featured-section">
                <div class="section-title">
                    <h2>Öne Çıkan Makaleler</h2>
                    <p>En popüler ve güncel içeriklerimiz</p>
                </div>
                
                <div class="featured-grid">
                    <?php foreach ($featured_articles as $article): ?>
                        <article class="article-card">
                            <div class="article-image">
                                <?php if (!empty($article['featured_image'])): ?>
                                    <img src="<?php echo UPLOADS_URL; ?>/articles/<?php echo htmlspecialchars($article['featured_image']); ?>" 
                                         alt="<?php echo htmlspecialchars($article['title']); ?>">
                                <?php else: ?>
                                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: white; font-size: 3rem;">
                                        <i class="fas fa-graduation-cap"></i>
                                    </div>
                                <?php endif; ?>
                                <?php if (!empty($article['category_name'])): ?>
                                    <div class="article-category">
                                        <?php echo htmlspecialchars($article['category_name']); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="article-content">
                                <div class="article-meta">
                                    <span><i class="fas fa-calendar-alt"></i> <?php echo date('d.m.Y', strtotime($article['created_at'])); ?></span>
                                    <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($article['author']); ?></span>
                                </div>
                                <h3 class="article-title">
                                    <a href="article.php?slug=<?php echo urlencode($article['slug']); ?>">
                                        <?php echo htmlspecialchars($article['title']); ?>
                                    </a>
                                </h3>
                                <?php if (!empty($article['excerpt'])): ?>
                                    <p class="article-excerpt">
                                        <?php echo htmlspecialchars(substr($article['excerpt'], 0, 150)) . '...'; ?>
                                    </p>
                                <?php endif; ?>
                                <a href="article.php?slug=<?php echo urlencode($article['slug']); ?>" class="article-link">
                                    Devamını Oku <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>
            </section>
        <?php endif; ?>

        <!-- All Articles -->
        <section>
            <div class="section-title">
                <h2>
                    <?php if (!empty($search)): ?>
                        "<?php echo htmlspecialchars($search); ?>" için Arama Sonuçları
                    <?php elseif ($category_filter > 0): ?>
                        <?php 
                        $selected_category = array_filter($categories, function($cat) use ($category_filter) {
                            return $cat['id'] == $category_filter;
                        });
                        if (!empty($selected_category)) {
                            echo htmlspecialchars(reset($selected_category)['name']) . ' Kategorisi';
                        }
                        ?>
                    <?php else: ?>
                        Tüm Makaleler
                    <?php endif; ?>
                </h2>
                <p><?php echo $total_articles; ?> makale bulundu</p>
            </div>

            <?php if (!empty($articles)): ?>
                <div class="articles-grid">
                    <?php foreach ($articles as $article): ?>
                        <article class="article-card">
                            <div class="article-image">
                                <?php if (!empty($article['featured_image'])): ?>
                                    <img src="<?php echo UPLOADS_URL; ?>/articles/<?php echo htmlspecialchars($article['featured_image']); ?>" 
                                         alt="<?php echo htmlspecialchars($article['title']); ?>">
                                <?php else: ?>
                                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: white; font-size: 3rem;">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                <?php endif; ?>
                                <?php if (!empty($article['category_name'])): ?>
                                    <div class="article-category">
                                        <?php echo htmlspecialchars($article['category_name']); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="article-content">
                                <div class="article-meta">
                                    <span><i class="fas fa-calendar-alt"></i> <?php echo date('d.m.Y', strtotime($article['created_at'])); ?></span>
                                    <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($article['author']); ?></span>
                                </div>
                                <h3 class="article-title">
                                    <a href="article.php?slug=<?php echo urlencode($article['slug']); ?>">
                                        <?php echo htmlspecialchars($article['title']); ?>
                                    </a>
                                </h3>
                                <?php if (!empty($article['excerpt'])): ?>
                                    <p class="article-excerpt">
                                        <?php echo htmlspecialchars(substr($article['excerpt'], 0, 120)) . '...'; ?>
                                    </p>
                                <?php endif; ?>
                                <a href="article.php?slug=<?php echo urlencode($article['slug']); ?>" class="article-link">
                                    Devamını Oku <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo $category_filter > 0 ? '&category=' . $category_filter : ''; ?>">
                                <i class="fas fa-chevron-left"></i> Önceki
                            </a>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <?php if ($i == $page): ?>
                                <span class="current"><?php echo $i; ?></span>
                            <?php else: ?>
                                <a href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo $category_filter > 0 ? '&category=' . $category_filter : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endif; ?>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo $category_filter > 0 ? '&category=' . $category_filter : ''; ?>">
                                Sonraki <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h3>Makale bulunamadı</h3>
                    <p>Arama kriterlerinizi değiştirerek tekrar deneyin.</p>
                    <a href="academy.php" class="btn btn-primary" style="margin-top: 20px;">
                        <i class="fas fa-home"></i> Tüm Makaleleri Görüntüle
                    </a>
                </div>
            <?php endif; ?>
        </section>
    </main>

    <?php include 'includes/footer.php'; ?>

    <script>
        // Smooth scroll for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading animation
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.3s ease';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });

        // Search form enhancement
        const searchForm = document.querySelector('.search-form');
        const searchInput = document.getElementById('search');
        
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchForm.submit();
            }
        });
    </script>
</body>
</html>
