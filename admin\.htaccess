# Admin Panel Security

# Prevent access to sensitive files
<FilesMatch "\.(log|bak|sql|inc|conf)$">
    Order Allow,<PERSON>y
    <PERSON> from all
</FilesMatch>

# Security headers for admin panel
<IfModule mod_headers.c>
    Header always set X-Frame-Options DENY
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Disable directory browsing
Options -Indexes

# Custom error pages
ErrorDocument 403 /admin/403.php
ErrorDocument 404 /admin/404.php

# Rate limiting for login attempts (if mod_evasive is available)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        5
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|tmp|temp|swp|swo)$">
    Order Allow,<PERSON>y
    <PERSON> from all
</FilesMatch>

# Block common attack patterns
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block SQL injection attempts
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER) [NC]
    RewriteRule ^(.*)$ - [F,L]
    
    # Block file injection attempts
    RewriteCond %{REQUEST_METHOD} GET
    RewriteCond %{QUERY_STRING} [a-zA-Z0-9_]=http:// [OR]
    RewriteCond %{QUERY_STRING} [a-zA-Z0-9_]=https:// [OR]
    RewriteCond %{QUERY_STRING} [a-zA-Z0-9_]=(\.\.//?)+ [OR]
    RewriteCond %{QUERY_STRING} [a-zA-Z0-9_]=/([a-z0-9_.]//?)+ [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>
