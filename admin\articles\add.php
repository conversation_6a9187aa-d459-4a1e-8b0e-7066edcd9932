<?php
session_start();
require_once '../../config/config.php';
require_once '../includes/ImageUploader.php';

// Giriş kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

$error_message = '';
$success_message = '';
$imageUploader = new ImageUploader();

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $excerpt = trim($_POST['excerpt'] ?? '');
    $status = $_POST['status'] ?? 'draft';
    $author = trim($_POST['author'] ?? '');
    $meta_title = trim($_POST['meta_title'] ?? '');
    $meta_description = trim($_POST['meta_description'] ?? '');
    
    // Validasyon
    if (empty($title)) {
        $error_message = 'Makale başlığı gereklidir.';
    } elseif (empty($content)) {
        $error_message = 'Makale içeriği gereklidir.';
    } else {
        try {
            // Slug oluştur
            $slug = createSlug($title);
            
            // Aynı slug var mı kontrol et
            $check_stmt = $pdo->prepare("SELECT id FROM articles WHERE slug = ?");
            $check_stmt->execute([$slug]);
            
            if ($check_stmt->fetch()) {
                $error_message = 'Bu makale başlığı zaten kullanılıyor.';
            } else {
                // Resim yükleme işlemleri
                $featured_image = null;
                $gallery_images = null;

                // Ana resim yükleme
                if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = $imageUploader->uploadSingle($_FILES['featured_image'], 'article_');
                    if ($upload_result['success']) {
                        $featured_image = $upload_result['filename'];
                    } else {
                        $error_message = 'Ana resim yüklenirken hata: ' . $upload_result['error'];
                    }
                }

                // Galeri resimleri yükleme
                if (isset($_FILES['gallery_images']) && !empty($_FILES['gallery_images']['name'][0])) {
                    $gallery_result = $imageUploader->uploadMultiple($_FILES['gallery_images'], 'article_gallery_');
                    if ($gallery_result['success_count'] > 0) {
                        $gallery_images = implode(',', $gallery_result['uploaded_files']);
                    }
                }

                if (empty($error_message)) {
                    // Makaleyi veritabanına ekle
                    $sql = "INSERT INTO articles (title, slug, content, excerpt, featured_image, gallery_images, status, author, meta_title, meta_description, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

                    $stmt = $pdo->prepare($sql);
                    $result = $stmt->execute([$title, $slug, $content, $excerpt, $featured_image, $gallery_images, $status, $author, $meta_title, $meta_description]);

                    if ($result) {
                        $success_message = 'Makale başarıyla eklendi.';
                        // Formu temizle
                        $title = $content = $excerpt = $author = $meta_title = $meta_description = '';
                        $status = 'draft';
                    } else {
                        $error_message = 'Makale eklenirken bir hata oluştu.';
                    }
                }
            }
            
        } catch (PDOException $e) {
            $error_message = 'Veritabanı hatası: ' . $e->getMessage();
        }
    }
}

$page_title = 'Yeni Makale Ekle';
include '../includes/header.php';
?>

<div class="admin-content">
    <div class="content-header">
        <h1 class="content-title">
            <i class="fas fa-plus"></i>
            Yeni Makale Ekle
        </h1>
        <div class="content-actions">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Geri Dön
            </a>
        </div>
    </div>
    
    <?php if ($error_message): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>
    
    <div class="form-card">
        <form method="POST" enctype="multipart/form-data" class="article-form">
            <div class="form-grid">
                <div class="form-group">
                    <label for="title" class="form-label">
                        <i class="fas fa-heading"></i>
                        Makale Başlığı *
                    </label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           class="form-input" 
                           value="<?php echo htmlspecialchars($title ?? ''); ?>" 
                           required>
                </div>
                
                <div class="form-group">
                    <label for="author" class="form-label">
                        <i class="fas fa-user"></i>
                        Yazar
                    </label>
                    <input type="text" 
                           id="author" 
                           name="author" 
                           class="form-input" 
                           value="<?php echo htmlspecialchars($author ?? ''); ?>" 
                           placeholder="Yazar adı">
                </div>
                
                <div class="form-group">
                    <label for="status" class="form-label">
                        <i class="fas fa-toggle-on"></i>
                        Durum
                    </label>
                    <select id="status" name="status" class="form-select">
                        <option value="draft" <?php echo ($status ?? 'draft') === 'draft' ? 'selected' : ''; ?>>Taslak</option>
                        <option value="published" <?php echo ($status ?? 'draft') === 'published' ? 'selected' : ''; ?>>Yayınla</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="excerpt" class="form-label">
                    <i class="fas fa-quote-left"></i>
                    Makale Özeti
                </label>
                <textarea id="excerpt" 
                          name="excerpt" 
                          class="form-textarea" 
                          rows="3" 
                          placeholder="Makale için kısa bir özet yazın..."><?php echo htmlspecialchars($excerpt ?? ''); ?></textarea>
            </div>
            
            <div class="form-group">
                <label for="content" class="form-label">
                    <i class="fas fa-align-left"></i>
                    Makale İçeriği *
                </label>
                <textarea id="content" 
                          name="content" 
                          class="form-textarea content-editor" 
                          rows="15" 
                          required><?php echo htmlspecialchars($content ?? ''); ?></textarea>
            </div>

            <!-- Fotoğraf Yönetimi -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-images"></i>
                    Fotoğraf Yönetimi
                </h3>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="featured_image" class="form-label">
                            <i class="fas fa-image"></i>
                            Öne Çıkan Resim
                        </label>
                        <div class="file-upload-area" onclick="document.getElementById('featured_image').click()">
                            <input type="file"
                                   id="featured_image"
                                   name="featured_image"
                                   class="file-input"
                                   accept="image/*"
                                   onchange="previewFeaturedImage(this)">
                            <div class="file-upload-text">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <span>Öne çıkan resmi seçin veya sürükleyin</span>
                                <small>JPG, PNG, GIF, WebP - Max 5MB</small>
                            </div>
                        </div>
                        <div id="featured_image_preview" class="image-preview"></div>
                    </div>

                    <div class="form-group">
                        <label for="gallery_images" class="form-label">
                            <i class="fas fa-images"></i>
                            Galeri Resimleri
                        </label>
                        <div class="file-upload-area" onclick="document.getElementById('gallery_images').click()">
                            <input type="file"
                                   id="gallery_images"
                                   name="gallery_images[]"
                                   class="file-input"
                                   accept="image/*"
                                   multiple
                                   onchange="previewGalleryImages(this)">
                            <div class="file-upload-text">
                                <i class="fas fa-images"></i>
                                <span>Galeri resimlerini seçin (Çoklu seçim)</span>
                                <small>JPG, PNG, GIF, WebP - Max 5MB her biri</small>
                            </div>
                        </div>
                        <div id="gallery_images_preview" class="gallery-preview"></div>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-search"></i>
                    SEO Ayarları
                </h3>
                
                <div class="form-group">
                    <label for="meta_title" class="form-label">Meta Başlık</label>
                    <input type="text" 
                           id="meta_title" 
                           name="meta_title" 
                           class="form-input" 
                           value="<?php echo htmlspecialchars($meta_title ?? ''); ?>" 
                           maxlength="60">
                    <small class="form-help">Maksimum 60 karakter</small>
                </div>
                
                <div class="form-group">
                    <label for="meta_description" class="form-label">Meta Açıklama</label>
                    <textarea id="meta_description" 
                              name="meta_description" 
                              class="form-textarea" 
                              rows="3" 
                              maxlength="160"><?php echo htmlspecialchars($meta_description ?? ''); ?></textarea>
                    <small class="form-help">Maksimum 160 karakter</small>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Makaleyi Kaydet
                </button>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    İptal
                </a>
            </div>
        </form>
    </div>
</div>

<style>
/* Form Styles */
.form-card {
    background: var(--white);
    border-radius: 15px;
    padding: 30px;
    box-shadow: var(--shadow);
}

.form-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: var(--transition);
    font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(249, 178, 51, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.content-editor {
    min-height: 300px;
    font-family: 'Courier New', monospace;
}

.form-help {
    display: block;
    margin-top: 5px;
    font-size: 0.8rem;
    color: var(--text-light);
}

.form-section {
    margin: 40px 0;
    padding: 25px;
    background: var(--bg-light);
    border-radius: 10px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid #e0e0e0;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background: #6c757d;
}

.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Fotoğraf Yönetimi Stilleri */
.form-section {
    margin-bottom: 40px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.section-title {
    color: var(--primary);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-upload-area {
    border: 2px dashed #ddd;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.file-upload-area:hover {
    border-color: var(--primary);
    background: #f0f8ff;
}

.file-upload-area.dragover {
    border-color: var(--primary);
    background: #e8f4fd;
}

.file-input {
    display: none;
}

.file-upload-text {
    color: #666;
}

.file-upload-text i {
    font-size: 2rem;
    color: var(--primary);
    margin-bottom: 10px;
    display: block;
}

.file-upload-text span {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
}

.file-upload-text small {
    color: #999;
    font-size: 0.85rem;
}

.image-preview {
    margin-top: 15px;
}

.preview-image {
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: relative;
    display: inline-block;
}

.preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.remove-preview {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-preview {
    margin-top: 15px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
}

.gallery-item {
    position: relative;
    aspect-ratio: 1;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Responsive */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        justify-content: center;
    }

    .gallery-preview {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}
</style>

<script>
// Öne çıkan resim önizleme
function previewFeaturedImage(input) {
    const preview = document.getElementById('featured_image_preview');
    preview.innerHTML = '';

    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Dosya boyutu kontrolü (5MB)
        if (file.size > 5242880) {
            alert('Dosya boyutu çok büyük! Maksimum 5MB olmalıdır.');
            input.value = '';
            return;
        }

        // Dosya türü kontrolü
        if (!file.type.match('image.*')) {
            alert('Lütfen sadece resim dosyası seçin!');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            const previewDiv = document.createElement('div');
            previewDiv.className = 'preview-image';
            previewDiv.innerHTML = `
                <img src="${e.target.result}" alt="Öne çıkan resim önizleme">
                <button type="button" class="remove-preview" onclick="removeFeaturedImage()">
                    <i class="fas fa-times"></i>
                </button>
            `;
            preview.appendChild(previewDiv);
        };
        reader.readAsDataURL(file);
    }
}

// Öne çıkan resim silme
function removeFeaturedImage() {
    document.getElementById('featured_image').value = '';
    document.getElementById('featured_image_preview').innerHTML = '';
}

// Galeri resimleri önizleme
function previewGalleryImages(input) {
    const preview = document.getElementById('gallery_images_preview');
    preview.innerHTML = '';

    if (input.files && input.files.length > 0) {
        // Maksimum 10 resim kontrolü
        if (input.files.length > 10) {
            alert('Maksimum 10 resim seçebilirsiniz!');
            input.value = '';
            return;
        }

        Array.from(input.files).forEach((file, index) => {
            // Dosya boyutu kontrolü (5MB)
            if (file.size > 5242880) {
                alert(`${file.name} dosyası çok büyük! Maksimum 5MB olmalıdır.`);
                return;
            }

            // Dosya türü kontrolü
            if (!file.type.match('image.*')) {
                alert(`${file.name} bir resim dosyası değil!`);
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const galleryItem = document.createElement('div');
                galleryItem.className = 'gallery-item';
                galleryItem.innerHTML = `
                    <img src="${e.target.result}" alt="Galeri resim ${index + 1}">
                    <button type="button" class="remove-preview" onclick="removeGalleryImage(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                preview.appendChild(galleryItem);
            };
            reader.readAsDataURL(file);
        });
    }
}

// Galeri resmi silme
function removeGalleryImage(index) {
    const input = document.getElementById('gallery_images');
    const dt = new DataTransfer();

    Array.from(input.files).forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    input.files = dt.files;
    previewGalleryImages(input);
}

// Drag & Drop işlevselliği
document.addEventListener('DOMContentLoaded', function() {
    const uploadAreas = document.querySelectorAll('.file-upload-area');

    uploadAreas.forEach(area => {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            area.addEventListener(eventName, preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            area.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            area.addEventListener(eventName, unhighlight, false);
        });

        area.addEventListener('drop', handleDrop, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight(e) {
        e.currentTarget.classList.add('dragover');
    }

    function unhighlight(e) {
        e.currentTarget.classList.remove('dragover');
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        const input = e.currentTarget.querySelector('input[type="file"]');

        input.files = files;

        if (input.id === 'featured_image') {
            previewFeaturedImage(input);
        } else if (input.id === 'gallery_images') {
            previewGalleryImages(input);
        }
    }

    // Form gönderme öncesi kontrol
    document.querySelector('.article-form').addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        const content = document.getElementById('content').value.trim();

        if (!title) {
            alert('Makale başlığı gereklidir!');
            e.preventDefault();
            return false;
        }

        if (!content) {
            alert('Makale içeriği gereklidir!');
            e.preventDefault();
            return false;
        }

        // Form gönderiliyor mesajı
        const submitBtn = document.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Kaydediliyor...';
        submitBtn.disabled = true;

        // Hata durumunda butonu eski haline getir
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 5000);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
