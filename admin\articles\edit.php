<?php
session_start();
require_once '../../config/config.php';
require_once '../includes/ImageUploader.php';

// <PERSON>iriş kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

$imageUploader = new ImageUploader();
$article_id = (int)($_GET['id'] ?? 0);

if ($article_id <= 0) {
    header('Location: index.php');
    exit;
}

// Makaleyi getir
try {
    $stmt = $pdo->prepare("SELECT * FROM articles WHERE id = ?");
    $stmt->execute([$article_id]);
    $article = $stmt->fetch();
    
    if (!$article) {
        header('Location: index.php');
        exit;
    }
} catch (PDOException $e) {
    die("Veritabanı hatası: " . $e->getMessage());
}

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $slug = trim($_POST['slug'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $excerpt = trim($_POST['excerpt'] ?? '');
    $author = trim($_POST['author'] ?? '');
    $status = $_POST['status'] ?? 'draft';
    $meta_title = trim($_POST['meta_title'] ?? '');
    $meta_description = trim($_POST['meta_description'] ?? '');
    
    $errors = [];
    
    // Validasyon
    if (empty($title)) {
        $errors[] = 'Makale başlığı gereklidir';
    }
    
    if (empty($content)) {
        $errors[] = 'Makale içeriği gereklidir';
    }
    
    if (empty($author)) {
        $errors[] = 'Yazar adı gereklidir';
    }
    
    // Slug oluştur
    if (empty($slug)) {
        $slug = createSlug($title);
    } else {
        $slug = createSlug($slug);
    }
    
    // Slug benzersizlik kontrolü (mevcut makale hariç)
    if (!empty($slug)) {
        $stmt = $pdo->prepare("SELECT id FROM articles WHERE slug = ? AND id != ?");
        $stmt->execute([$slug, $article_id]);
        if ($stmt->fetch()) {
            $slug = $slug . '-' . time();
        }
    }
    
    if (empty($errors)) {
        try {
            // Mevcut resim bilgilerini al
            $current_featured_image = $article['featured_image'];
            $current_gallery_images = $article['gallery_images'];

            // Öne çıkan resim yükleme
            $featured_image = $current_featured_image; // Mevcut resmi koru
            if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                $upload_result = $imageUploader->uploadSingle($_FILES['featured_image'], 'article_');
                if ($upload_result['success']) {
                    // Eski resmi sil
                    if ($current_featured_image) {
                        $imageUploader->deleteImage($current_featured_image);
                    }
                    $featured_image = $upload_result['filename'];
                } else {
                    $errors[] = 'Öne çıkan resim yüklenirken hata: ' . $upload_result['error'];
                }
            }

            // Öne çıkan resim silme kontrolü
            if (isset($_POST['remove_featured_image']) && $_POST['remove_featured_image'] === '1') {
                if ($current_featured_image) {
                    $imageUploader->deleteImage($current_featured_image);
                }
                $featured_image = null;
            }

            // Galeri resimleri yükleme
            $gallery_images = $current_gallery_images; // Mevcut resimleri koru
            if (isset($_FILES['gallery_images']) && !empty($_FILES['gallery_images']['name'][0])) {
                $gallery_result = $imageUploader->uploadMultiple($_FILES['gallery_images'], 'article_gallery_');
                if ($gallery_result['success_count'] > 0) {
                    // Mevcut galeri resimlerini al
                    $existing_gallery = !empty($current_gallery_images) ? explode(',', $current_gallery_images) : [];
                    // Yeni resimleri ekle
                    $all_gallery = array_merge($existing_gallery, $gallery_result['uploaded_files']);
                    $gallery_images = implode(',', $all_gallery);
                }
            }

            // Galeri resmi silme kontrolü
            if (isset($_POST['remove_gallery_images']) && !empty($_POST['remove_gallery_images'])) {
                $images_to_remove = explode(',', $_POST['remove_gallery_images']);
                foreach ($images_to_remove as $image_to_remove) {
                    $imageUploader->deleteImage(trim($image_to_remove));
                }

                // Kalan resimleri güncelle
                $existing_gallery = !empty($current_gallery_images) ? explode(',', $current_gallery_images) : [];
                $remaining_gallery = array_diff($existing_gallery, $images_to_remove);
                $gallery_images = !empty($remaining_gallery) ? implode(',', $remaining_gallery) : null;
            }

            if (empty($errors)) {
                $sql = "UPDATE articles SET
                        title = ?, slug = ?, content = ?, excerpt = ?, featured_image = ?, gallery_images = ?, author = ?,
                        status = ?, meta_title = ?, meta_description = ?, updated_at = NOW()
                        WHERE id = ?";

                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute([
                    $title, $slug, $content, $excerpt, $featured_image, $gallery_images, $author,
                    $status, $meta_title, $meta_description, $article_id
                ]);

                if ($result) {
                    $success_message = 'Makale başarıyla güncellendi!';

                    // Güncellenmiş veriyi tekrar çek
                    $stmt = $pdo->prepare("SELECT * FROM articles WHERE id = ?");
                    $stmt->execute([$article_id]);
                    $article = $stmt->fetch();
                } else {
                    $errors[] = 'Makale güncellenirken bir hata oluştu';
                }
            }
        } catch (PDOException $e) {
            $errors[] = 'Veritabanı hatası: ' . $e->getMessage();
        }
    }
}

$page_title = 'Makale Düzenle';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - GNG Makine Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #F9B233;
            --secondary-color: #8A8C8F;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --white: #ffffff;
            --bg-light: #f8f9fa;
            --border-color: #e9ecef;
            --success-color: #27ae60;
            --error-color: #e74c3c;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-light);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--white);
            padding: 20px;
            border-radius: 10px;
            box-shadow: var(--shadow);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: var(--text-dark);
            font-size: 1.8rem;
            font-weight: 600;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: var(--white);
        }

        .btn-secondary:hover {
            background: #6c757d;
            color: var(--white);
        }

        .form-container {
            background: var(--white);
            padding: 30px;
            border-radius: 15px;
            box-shadow: var(--shadow);
        }

        .form-grid {
            display: grid;
            gap: 25px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(249, 178, 51, 0.1);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 120px;
            font-family: 'Courier New', monospace;
        }

        .content-textarea {
            min-height: 300px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 25px;
            font-weight: 500;
        }

        .alert-success {
            background: rgba(39, 174, 96, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(39, 174, 96, 0.2);
        }

        .alert-error {
            background: rgba(231, 76, 60, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(231, 76, 60, 0.2);
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--white);
            padding: 12px 30px;
            font-size: 1rem;
        }

        .btn-primary:hover {
            background: #e6a429;
            color: var(--white);
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 25px;
            border-top: 1px solid var(--border-color);
        }

        .char-counter {
            font-size: 0.85rem;
            color: var(--text-light);
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-edit"></i> <?php echo $page_title; ?></h1>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Geri Dön
            </a>
        </div>

        <?php if (isset($success_message)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i>
                <ul style="margin: 10px 0 0 20px;">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <div class="form-container">
            <form method="POST" action="" enctype="multipart/form-data">
                <div class="form-grid">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="title">Makale Başlığı *</label>
                            <input type="text" id="title" name="title" class="form-control" 
                                   value="<?php echo htmlspecialchars($article['title']); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="slug">URL Slug</label>
                            <input type="text" id="slug" name="slug" class="form-control" 
                                   value="<?php echo htmlspecialchars($article['slug']); ?>"
                                   placeholder="Otomatik oluşturulacak">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="content">Makale İçeriği *</label>
                        <textarea id="content" name="content" class="form-control content-textarea" required><?php echo htmlspecialchars($article['content']); ?></textarea>
                    </div>

                    <div class="form-group">
                        <label for="excerpt">Özet</label>
                        <textarea id="excerpt" name="excerpt" class="form-control"><?php echo htmlspecialchars($article['excerpt']); ?></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="author">Yazar *</label>
                            <input type="text" id="author" name="author" class="form-control" 
                                   value="<?php echo htmlspecialchars($article['author']); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="status">Durum</label>
                            <select id="status" name="status" class="form-control">
                                <option value="draft" <?php echo $article['status'] === 'draft' ? 'selected' : ''; ?>>Taslak</option>
                                <option value="published" <?php echo $article['status'] === 'published' ? 'selected' : ''; ?>>Yayınlandı</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="meta_title">SEO Başlık</label>
                            <input type="text" id="meta_title" name="meta_title" class="form-control" 
                                   value="<?php echo htmlspecialchars($article['meta_title']); ?>" maxlength="60">
                            <div class="char-counter">0/60 karakter</div>
                        </div>
                        <div class="form-group">
                            <label for="meta_description">SEO Açıklama</label>
                            <textarea id="meta_description" name="meta_description" class="form-control" maxlength="160"><?php echo htmlspecialchars($article['meta_description']); ?></textarea>
                            <div class="char-counter">0/160 karakter</div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <a href="index.php" class="btn btn-secondary">İptal</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Güncelle
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Karakter sayacı
        function updateCharCounter(input, counter) {
            const current = input.value.length;
            const max = input.getAttribute('maxlength');
            counter.textContent = current + '/' + max + ' karakter';
            
            if (current > max * 0.9) {
                counter.style.color = '#e74c3c';
            } else {
                counter.style.color = '#7f8c8d';
            }
        }

        // Meta title karakter sayacı
        const metaTitleInput = document.getElementById('meta_title');
        const metaTitleCounter = metaTitleInput.nextElementSibling;
        updateCharCounter(metaTitleInput, metaTitleCounter);
        metaTitleInput.addEventListener('input', () => updateCharCounter(metaTitleInput, metaTitleCounter));

        // Meta description karakter sayacı
        const metaDescInput = document.getElementById('meta_description');
        const metaDescCounter = metaDescInput.nextElementSibling;
        updateCharCounter(metaDescInput, metaDescCounter);
        metaDescInput.addEventListener('input', () => updateCharCounter(metaDescInput, metaDescCounter));

        // Slug otomatik oluşturma
        const titleInput = document.getElementById('title');
        const slugInput = document.getElementById('slug');
        
        titleInput.addEventListener('input', function() {
            if (!slugInput.value || slugInput.dataset.auto !== 'false') {
                slugInput.value = createSlug(this.value);
                slugInput.dataset.auto = 'true';
            }
        });
        
        slugInput.addEventListener('input', function() {
            this.dataset.auto = 'false';
        });
        
        function createSlug(text) {
            const turkish = ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'Ç', 'Ğ', 'I', 'İ', 'Ö', 'Ş', 'Ü'];
            const english = ['c', 'g', 'i', 'o', 's', 'u', 'c', 'g', 'i', 'i', 'o', 's', 'u'];
            
            for (let i = 0; i < turkish.length; i++) {
                text = text.replace(new RegExp(turkish[i], 'g'), english[i]);
            }
            
            return text.toLowerCase()
                      .replace(/[^a-z0-9]+/g, '-')
                      .replace(/^-+|-+$/g, '');
        }
    </script>
</body>
</html>
