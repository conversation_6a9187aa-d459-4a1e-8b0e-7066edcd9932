<?php
session_start();
require_once '../../config/config.php';

// G<PERSON>ş kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

// Sayfalama
$page = (int)($_GET['page'] ?? 1);
$per_page = 10;
$offset = ($page - 1) * $per_page;

// Arama
$search = trim($_GET['search'] ?? '');
$where_clause = '';
$params = [];

if (!empty($search)) {
    $where_clause = "WHERE name LIKE ? OR description LIKE ?";
    $params = ["%$search%", "%$search%"];
}

try {
    // Toplam kategori sayısı
    $count_sql = "SELECT COUNT(*) FROM categories $where_clause";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total_categories = $count_stmt->fetchColumn();
    
    // Kategorileri getir
    $sql = "SELECT * FROM categories $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $categories = $stmt->fetchAll();
    
    // Toplam sayfa sayısı
    $total_pages = ceil($total_categories / $per_page);
    
} catch (PDOException $e) {
    $categories = [];
    $total_categories = 0;
    $total_pages = 0;
}

$page_title = 'Kategori Yönetimi';
include '../includes/header.php';
?>

<div class="admin-content">
    <div class="content-header">
        <h1 class="content-title">
            <i class="fas fa-folder"></i>
            Kategori Yönetimi
        </h1>
        <div class="content-actions">
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Yeni Kategori
            </a>
        </div>
    </div>
    
    <!-- Arama ve Filtreler -->
    <div class="filters-card">
        <form method="GET" class="filters-form">
            <div class="filter-group">
                <input type="text" 
                       name="search" 
                       placeholder="Kategori ara..." 
                       value="<?php echo htmlspecialchars($search); ?>"
                       class="filter-input">
                <button type="submit" class="btn btn-secondary">
                    <i class="fas fa-search"></i>
                    Ara
                </button>
                <?php if (!empty($search)): ?>
                    <a href="index.php" class="btn btn-outline">
                        <i class="fas fa-times"></i>
                        Temizle
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>
    
    <!-- Kategori Listesi -->
    <div class="table-card">
        <div class="table-header">
            <h3 class="table-title">
                Kategoriler (<?php echo $total_categories; ?>)
            </h3>
        </div>
        
        <?php if (empty($categories)): ?>
            <div class="empty-state">
                <i class="fas fa-folder-open"></i>
                <h3>Kategori Bulunamadı</h3>
                <p>
                    <?php if (!empty($search)): ?>
                        Arama kriterlerinize uygun kategori bulunamadı.
                    <?php else: ?>
                        Henüz hiç kategori eklenmemiş.
                    <?php endif; ?>
                </p>
                <a href="add.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    İlk Kategoriyi Ekle
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Kategori Adı</th>
                            <th>Açıklama</th>
                            <th>Durum</th>
                            <th>Ürün Sayısı</th>
                            <th>Oluşturma Tarihi</th>
                            <th>İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($categories as $category): ?>
                            <?php
                            // Kategori ürün sayısını getir
                            try {
                                $product_count_stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE category_id = ?");
                                $product_count_stmt->execute([$category['id']]);
                                $product_count = $product_count_stmt->fetchColumn();
                            } catch (PDOException $e) {
                                $product_count = 0;
                            }
                            ?>
                            <tr>
                                <td>
                                    <div class="category-info">
                                        <strong><?php echo htmlspecialchars($category['name']); ?></strong>
                                        <small class="category-slug"><?php echo htmlspecialchars($category['slug']); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div class="description-preview">
                                        <?php echo htmlspecialchars(substr($category['description'] ?? '', 0, 100)); ?>
                                        <?php if (strlen($category['description'] ?? '') > 100): ?>...<?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge status-<?php echo $category['status']; ?>">
                                        <?php echo $category['status'] === 'active' ? 'Aktif' : 'Pasif'; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="product-count">
                                        <?php echo $product_count; ?> ürün
                                    </span>
                                </td>
                                <td>
                                    <span class="date">
                                        <?php echo date('d.m.Y H:i', strtotime($category['created_at'])); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="edit.php?id=<?php echo $category['id']; ?>" 
                                           class="btn-action btn-edit" 
                                           title="Düzenle">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['name'], ENT_QUOTES); ?>')" 
                                                class="btn-action btn-delete" 
                                                title="Sil">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Sayfalama -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                           class="pagination-btn">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <a href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                           class="pagination-btn <?php echo $i === $page ? 'active' : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <a href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                           class="pagination-btn">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<script>
function deleteCategory(id, name) {
    if (confirm('Bu kategoriyi silmek istediğinizden emin misiniz?\n\nKategori: ' + name + '\n\nBu işlem geri alınamaz!')) {
        fetch('delete.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id=' + id
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('Bir hata oluştu: ' + error);
        });
    }
}
</script>

<style>
/* Table Styles */
.table-card {
    background: var(--white);
    border-radius: 15px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.table-header {
    padding: 25px;
    border-bottom: 1px solid #e0e0e0;
}

.table-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
}

.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.data-table th {
    background: var(--bg-light);
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.category-info strong {
    display: block;
    color: var(--text-dark);
    margin-bottom: 3px;
}

.category-slug {
    color: var(--text-light);
    font-size: 0.8rem;
}

.description-preview {
    max-width: 200px;
    color: var(--text-light);
    font-size: 0.9rem;
    line-height: 1.4;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.product-count {
    color: var(--primary-color);
    font-weight: 500;
}

.date {
    color: var(--text-light);
    font-size: 0.9rem;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.btn-action {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn-edit {
    background: #e3f2fd;
    color: #1976d2;
}

.btn-edit:hover {
    background: #1976d2;
    color: white;
}

.btn-delete {
    background: #ffebee;
    color: #d32f2f;
}

.btn-delete:hover {
    background: #d32f2f;
    color: white;
}

/* Filters */
.filters-card {
    background: var(--white);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
}

.filters-form {
    display: flex;
    gap: 20px;
    align-items: center;
}

.filter-group {
    display: flex;
    gap: 15px;
    align-items: center;
    flex: 1;
}

.filter-input {
    flex: 1;
    max-width: 300px;
    padding: 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.9rem;
}

.filter-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-outline {
    background: transparent;
    color: var(--text-light);
    border: 2px solid #e0e0e0;
}

.btn:hover {
    transform: translateY(-2px);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-light);
}

.empty-state i {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.empty-state h3 {
    color: var(--text-dark);
    margin-bottom: 10px;
}

.empty-state p {
    margin-bottom: 25px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
    padding: 25px;
}

.pagination-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-dark);
    transition: var(--transition);
}

.pagination-btn:hover,
.pagination-btn.active {
    background: var(--primary-color);
    color: var(--white);
}

/* Responsive */
@media (max-width: 768px) {
    .filters-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        flex-direction: column;
    }
    
    .filter-input {
        max-width: none;
    }
    
    .data-table {
        font-size: 0.8rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 10px 8px;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
