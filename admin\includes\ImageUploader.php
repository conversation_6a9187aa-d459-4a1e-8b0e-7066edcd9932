<?php

class ImageUploader {
    private $upload_dir;
    private $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    private $max_file_size = 5242880; // 5MB
    private $max_width = 2000;
    private $max_height = 2000;
    
    public function __construct($upload_dir = '../../uploads/products/') {
        $this->upload_dir = $upload_dir;
        $this->createUploadDir();
    }
    
    private function createUploadDir() {
        if (!file_exists($this->upload_dir)) {
            mkdir($this->upload_dir, 0755, true);
        }
        
        // .htaccess dosyası oluştur
        $htaccess_path = $this->upload_dir . '.htaccess';
        if (!file_exists($htaccess_path)) {
            $htaccess_content = "# Güvenlik için PHP dosyalarının çalıştırılmasını engelle\n";
            $htaccess_content .= "<Files *.php>\n";
            $htaccess_content .= "    Order Deny,Allow\n";
            $htaccess_content .= "    Deny from all\n";
            $htaccess_content .= "</Files>\n\n";
            $htaccess_content .= "# Sadece resim dosyalarına izin ver\n";
            $htaccess_content .= "<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">\n";
            $htaccess_content .= "    Order Allow,Deny\n";
            $htaccess_content .= "    Allow from all\n";
            $htaccess_content .= "</FilesMatch>\n";
            
            file_put_contents($htaccess_path, $htaccess_content);
        }
    }
    
    public function uploadSingle($file, $prefix = 'product_') {
        if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'error' => 'Dosya yükleme hatası'];
        }
        
        // Dosya boyutu kontrolü
        if ($file['size'] > $this->max_file_size) {
            return ['success' => false, 'error' => 'Dosya boyutu çok büyük (Max: 5MB)'];
        }
        
        // MIME type kontrolü
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mime_type, $this->allowed_types)) {
            return ['success' => false, 'error' => 'Geçersiz dosya türü. Sadece JPG, PNG, GIF, WebP dosyaları kabul edilir.'];
        }
        
        // Dosya uzantısını al
        $extension = $this->getExtensionFromMime($mime_type);
        
        // Benzersiz dosya adı oluştur
        $filename = $prefix . uniqid() . '_' . time() . '.' . $extension;
        $filepath = $this->upload_dir . $filename;
        
        // Resmi yükle ve boyutlandır
        if ($this->processAndSaveImage($file['tmp_name'], $filepath, $mime_type)) {
            return [
                'success' => true, 
                'filename' => $filename,
                'filepath' => $filepath,
                'url' => str_replace('../../', '', $filepath)
            ];
        } else {
            return ['success' => false, 'error' => 'Resim işlenirken hata oluştu'];
        }
    }
    
    public function uploadMultiple($files, $prefix = 'product_') {
        $results = [];
        $uploaded_files = [];
        
        // Dosyaları normalize et (tek dosya veya çoklu dosya)
        if (isset($files['name']) && is_array($files['name'])) {
            // Çoklu dosya
            for ($i = 0; $i < count($files['name']); $i++) {
                if ($files['error'][$i] === UPLOAD_ERR_OK) {
                    $file = [
                        'name' => $files['name'][$i],
                        'type' => $files['type'][$i],
                        'tmp_name' => $files['tmp_name'][$i],
                        'error' => $files['error'][$i],
                        'size' => $files['size'][$i]
                    ];
                    
                    $result = $this->uploadSingle($file, $prefix);
                    $results[] = $result;
                    
                    if ($result['success']) {
                        $uploaded_files[] = $result['filename'];
                    }
                }
            }
        } else {
            // Tek dosya
            $result = $this->uploadSingle($files, $prefix);
            $results[] = $result;
            
            if ($result['success']) {
                $uploaded_files[] = $result['filename'];
            }
        }
        
        return [
            'results' => $results,
            'uploaded_files' => $uploaded_files,
            'success_count' => count($uploaded_files),
            'total_count' => count($results)
        ];
    }
    
    private function processAndSaveImage($source_path, $destination_path, $mime_type) {
        // Resmi yükle
        switch ($mime_type) {
            case 'image/jpeg':
            case 'image/jpg':
                $image = imagecreatefromjpeg($source_path);
                break;
            case 'image/png':
                $image = imagecreatefrompng($source_path);
                break;
            case 'image/gif':
                $image = imagecreatefromgif($source_path);
                break;
            case 'image/webp':
                $image = imagecreatefromwebp($source_path);
                break;
            default:
                return false;
        }
        
        if (!$image) {
            return false;
        }
        
        // Orijinal boyutları al
        $original_width = imagesx($image);
        $original_height = imagesy($image);
        
        // Boyutlandırma gerekli mi?
        if ($original_width > $this->max_width || $original_height > $this->max_height) {
            // Orantılı boyutlandırma hesapla
            $ratio = min($this->max_width / $original_width, $this->max_height / $original_height);
            $new_width = round($original_width * $ratio);
            $new_height = round($original_height * $ratio);
            
            // Yeni resim oluştur
            $resized_image = imagecreatetruecolor($new_width, $new_height);
            
            // PNG ve GIF için şeffaflığı koru
            if ($mime_type === 'image/png' || $mime_type === 'image/gif') {
                imagealphablending($resized_image, false);
                imagesavealpha($resized_image, true);
                $transparent = imagecolorallocatealpha($resized_image, 255, 255, 255, 127);
                imagefill($resized_image, 0, 0, $transparent);
            }
            
            // Resmi boyutlandır
            imagecopyresampled($resized_image, $image, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height);
            
            // Eski resmi temizle
            imagedestroy($image);
            $image = $resized_image;
        }
        
        // Resmi kaydet
        $saved = false;
        switch ($mime_type) {
            case 'image/jpeg':
            case 'image/jpg':
                $saved = imagejpeg($image, $destination_path, 90);
                break;
            case 'image/png':
                $saved = imagepng($image, $destination_path, 8);
                break;
            case 'image/gif':
                $saved = imagegif($image, $destination_path);
                break;
            case 'image/webp':
                $saved = imagewebp($image, $destination_path, 90);
                break;
        }
        
        // Belleği temizle
        imagedestroy($image);
        
        return $saved;
    }
    
    private function getExtensionFromMime($mime_type) {
        $extensions = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp'
        ];
        
        return $extensions[$mime_type] ?? 'jpg';
    }
    
    public function deleteImage($filename) {
        if (empty($filename)) {
            return false;
        }
        
        $filepath = $this->upload_dir . $filename;
        
        if (file_exists($filepath)) {
            return unlink($filepath);
        }
        
        return false;
    }
    
    public function deleteMultipleImages($filenames) {
        if (empty($filenames)) {
            return false;
        }
        
        $deleted_count = 0;
        $filenames_array = is_array($filenames) ? $filenames : explode(',', $filenames);
        
        foreach ($filenames_array as $filename) {
            $filename = trim($filename);
            if ($this->deleteImage($filename)) {
                $deleted_count++;
            }
        }
        
        return $deleted_count;
    }
    
    public function getImageInfo($filename) {
        if (empty($filename)) {
            return null;
        }
        
        $filepath = $this->upload_dir . $filename;
        
        if (!file_exists($filepath)) {
            return null;
        }
        
        $info = getimagesize($filepath);
        $filesize = filesize($filepath);
        
        return [
            'filename' => $filename,
            'filepath' => $filepath,
            'url' => str_replace('../../', '', $filepath),
            'width' => $info[0] ?? 0,
            'height' => $info[1] ?? 0,
            'mime_type' => $info['mime'] ?? '',
            'filesize' => $filesize,
            'filesize_formatted' => $this->formatFileSize($filesize)
        ];
    }
    
    private function formatFileSize($bytes) {
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
}
?>
