        </div>
    </div>

    <style>
        /* Dashboard Styles */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: var(--white);
            border-radius: 15px;
            padding: 25px;
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
            gap: 20px;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
        }

        .stat-icon.products { background: linear-gradient(135deg, #3498db, #2980b9); }
        .stat-icon.categories { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .stat-icon.articles { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .stat-icon.messages { background: linear-gradient(135deg, #2ecc71, #27ae60); }

        .stat-info {
            flex: 1;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .stat-link {
            color: var(--text-light);
            font-size: 1.2rem;
            transition: var(--transition);
        }

        .stat-link:hover {
            color: var(--primary-color);
            transform: translateX(5px);
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .dashboard-card {
            background: var(--white);
            border-radius: 15px;
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .card-header {
            padding: 25px 25px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-dark);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-action {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .card-action:hover {
            color: var(--accent-color);
        }

        .card-body {
            padding: 0 25px 25px;
        }

        /* Item List */
        .item-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: var(--bg-light);
            border-radius: 10px;
            transition: var(--transition);
        }

        .item:hover {
            background: #f0f0f0;
        }

        .item-image {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .item-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .placeholder-image {
            width: 100%;
            height: 100%;
            background: var(--text-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
        }

        .item-info {
            flex: 1;
        }

        .item-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 5px;
        }

        .item-meta {
            display: flex;
            gap: 15px;
            font-size: 0.85rem;
            color: var(--text-light);
        }

        .category {
            color: var(--primary-color);
            font-weight: 500;
        }

        .item-actions {
            display: flex;
            gap: 10px;
        }

        .btn-edit {
            color: var(--primary-color);
            font-size: 1.1rem;
            padding: 5px;
            border-radius: 5px;
            transition: var(--transition);
        }

        .btn-edit:hover {
            background: var(--primary-color);
            color: var(--white);
        }

        /* Message List */
        .message-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message-item {
            padding: 20px;
            background: var(--bg-light);
            border-radius: 10px;
            border-left: 4px solid transparent;
            transition: var(--transition);
        }

        .message-item.unread {
            border-left-color: var(--primary-color);
            background: #fff9e6;
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .message-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-dark);
        }

        .message-date {
            font-size: 0.8rem;
            color: var(--text-light);
        }

        .message-subject {
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 8px;
        }

        .message-preview {
            color: var(--text-light);
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 15px;
        }

        .message-actions {
            text-align: right;
        }

        .btn-view {
            background: var(--primary-color);
            color: var(--white);
            padding: 6px 15px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.85rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-view:hover {
            background: var(--accent-color);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-light);
        }

        .empty-state i {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .empty-state p {
            margin-bottom: 20px;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--white);
        }

        .btn-primary:hover {
            background: var(--accent-color);
            transform: translateY(-2px);
        }

        /* Quick Actions */
        .quick-actions {
            margin-top: 40px;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 25px;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .action-card {
            background: var(--white);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: var(--shadow);
            text-decoration: none;
            transition: var(--transition);
            border: 2px solid transparent;
        }

        .action-card:hover {
            transform: translateY(-5px);
            border-color: var(--primary-color);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .action-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin: 0 auto 15px;
        }

        .action-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
        }

        .action-desc {
            color: var(--text-light);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .action-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .action-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>