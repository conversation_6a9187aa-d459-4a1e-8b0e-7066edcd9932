<aside class="admin-sidebar" id="adminSidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <i class="fas fa-cogs"></i>
            GNG Makine
        </div>
        <div class="sidebar-subtitle">Admin Panel</div>
    </div>

    <!-- Sidebar Navigation -->
    <nav class="sidebar-nav">
        <!-- Ana <PERSON> -->
        <div class="nav-section">
            <div class="nav-section-title">Ana <PERSON></div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>/admin/" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'index.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- <PERSON><PERSON><PERSON><PERSON> -->
        <div class="nav-section">
            <div class="nav-section-title">İçerik Yönetimi</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>/admin/products/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/products/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-boxes nav-icon"></i>
                        <span class="nav-text">Ürünler</span>
                        <span class="nav-badge"><?php echo $total_products ?? 0; ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>/admin/categories/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/categories/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-tags nav-icon"></i>
                        <span class="nav-text">Kategoriler</span>
                        <span class="nav-badge"><?php echo $total_categories ?? 0; ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>/admin/articles/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/articles/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-newspaper nav-icon"></i>
                        <span class="nav-text">Makaleler</span>
                        <span class="nav-badge"><?php echo $total_articles ?? 0; ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>/admin/media/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/media/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-images nav-icon"></i>
                        <span class="nav-text">Medya</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- İletişim -->
        <div class="nav-section">
            <div class="nav-section-title">İletişim</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>/admin/messages/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/messages/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-envelope nav-icon"></i>
                        <span class="nav-text">Mesajlar</span>
                        <?php if (isset($unread_messages) && $unread_messages > 0): ?>
                            <span class="nav-badge"><?php echo $unread_messages; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Sistem -->
        <div class="nav-section">
            <div class="nav-section-title">Sistem</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>/admin/settings/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/settings/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-cog nav-icon"></i>
                        <span class="nav-text">Ayarlar</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>/admin/users/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/users/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-users nav-icon"></i>
                        <span class="nav-text">Kullanıcılar</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>/admin/backup/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/backup/') !== false ? 'active' : ''; ?>">
                        <i class="fas fa-database nav-icon"></i>
                        <span class="nav-text">Yedekleme</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Site -->
        <div class="nav-section">
            <div class="nav-section-title">Site</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>/" target="_blank" class="nav-link">
                        <i class="fas fa-external-link-alt nav-icon"></i>
                        <span class="nav-text">Siteyi Görüntüle</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>/admin/logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt nav-icon"></i>
                        <span class="nav-text">Çıkış Yap</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>
</aside>

<script>
// Sidebar toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('adminSidebar');
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userMenu = document.getElementById('userMenu');

    // Sidebar toggle
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-open');
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('mobile-open');
                }
            }
        });
    }

    // User menu toggle
    if (userMenuBtn && userMenu) {
        userMenuBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('active');
        });

        // Close user menu when clicking outside
        document.addEventListener('click', function() {
            userMenu.classList.remove('active');
        });
    }

    // Prevent user menu from closing when clicking inside
    if (userMenu) {
        userMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
});
</script>