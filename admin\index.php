<?php
session_start();
require_once '../config/config.php';

// Giriş kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Dashboard istatistikleri
try {
    // Toplam ürün sayısı
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM products WHERE status = 'active'");
    $total_products = $stmt->fetch()['total'];

    // Toplam kategori sayısı
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM categories WHERE status = 'active'");
    $total_categories = $stmt->fetch()['total'];

    // Toplam makale sayısı
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM articles WHERE status = 'published'");
    $total_articles = $stmt->fetch()['total'];

    // Toplam mesaj sayısı
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM contact_messages WHERE status = 'unread'");
    $unread_messages = $stmt->fetch()['total'];

    // Son eklenen ürünler
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active'
        ORDER BY p.created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $recent_products = $stmt->fetchAll();

    // Son mesajlar
    $stmt = $pdo->prepare("
        SELECT * FROM contact_messages
        ORDER BY created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $recent_messages = $stmt->fetchAll();

} catch (PDOException $e) {
    $error_message = 'Veritabanı hatası: ' . $e->getMessage();
}

$page_title = 'Dashboard';
include 'includes/header.php';
?>

<div class="admin-content">
    <div class="content-header">
        <h1 class="content-title">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </h1>
        <p class="content-subtitle">Hoş geldiniz, <?php echo htmlspecialchars($_SESSION['admin_name']); ?>!</p>
    </div>

    <!-- İstatistik Kartları -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon products">
                <i class="fas fa-boxes"></i>
            </div>
            <div class="stat-info">
                <h3 class="stat-number"><?php echo number_format($total_products); ?></h3>
                <p class="stat-label">Toplam Ürün</p>
            </div>
            <a href="products/" class="stat-link">
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>

        <div class="stat-card">
            <div class="stat-icon categories">
                <i class="fas fa-tags"></i>
            </div>
            <div class="stat-info">
                <h3 class="stat-number"><?php echo number_format($total_categories); ?></h3>
                <p class="stat-label">Kategori</p>
            </div>
            <a href="categories/" class="stat-link">
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>

        <div class="stat-card">
            <div class="stat-icon articles">
                <i class="fas fa-newspaper"></i>
            </div>
            <div class="stat-info">
                <h3 class="stat-number"><?php echo number_format($total_articles); ?></h3>
                <p class="stat-label">Makale</p>
            </div>
            <a href="articles/" class="stat-link">
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>

        <div class="stat-card">
            <div class="stat-icon messages">
                <i class="fas fa-envelope"></i>
            </div>
            <div class="stat-info">
                <h3 class="stat-number"><?php echo number_format($unread_messages); ?></h3>
                <p class="stat-label">Okunmamış Mesaj</p>
            </div>
            <a href="messages/" class="stat-link">
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
    </div>

    <!-- İçerik Alanları -->
    <div class="dashboard-grid">
        <!-- Son Ürünler -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-box"></i>
                    Son Eklenen Ürünler
                </h3>
                <a href="products/" class="card-action">
                    Tümünü Gör <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_products)): ?>
                    <div class="item-list">
                        <?php foreach ($recent_products as $product): ?>
                            <div class="item">
                                <div class="item-image">
                                    <?php if ($product['main_image']): ?>
                                        <img src="<?php echo UPLOADS_URL; ?>/products/<?php echo htmlspecialchars($product['main_image']); ?>"
                                             alt="<?php echo htmlspecialchars($product['name']); ?>">
                                    <?php else: ?>
                                        <div class="placeholder-image">
                                            <i class="fas fa-image"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="item-info">
                                    <h4 class="item-title"><?php echo htmlspecialchars($product['name']); ?></h4>
                                    <p class="item-meta">
                                        <span class="category"><?php echo htmlspecialchars($product['category_name'] ?? 'Kategori Yok'); ?></span>
                                        <span class="date"><?php echo date('d.m.Y', strtotime($product['created_at'])); ?></span>
                                    </p>
                                </div>
                                <div class="item-actions">
                                    <a href="products/edit.php?id=<?php echo $product['id']; ?>" class="btn-edit" title="Düzenle">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-box-open"></i>
                        <p>Henüz ürün eklenmemiş.</p>
                        <a href="products/add.php" class="btn btn-primary">İlk Ürünü Ekle</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Son Mesajlar -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-comments"></i>
                    Son Mesajlar
                </h3>
                <a href="messages/" class="card-action">
                    Tümünü Gör <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_messages)): ?>
                    <div class="message-list">
                        <?php foreach ($recent_messages as $message): ?>
                            <div class="message-item <?php echo $message['status'] === 'unread' ? 'unread' : ''; ?>">
                                <div class="message-header">
                                    <h4 class="message-name"><?php echo htmlspecialchars($message['name']); ?></h4>
                                    <span class="message-date"><?php echo date('d.m.Y H:i', strtotime($message['created_at'])); ?></span>
                                </div>
                                <p class="message-subject"><?php echo htmlspecialchars($message['subject']); ?></p>
                                <p class="message-preview">
                                    <?php echo htmlspecialchars(substr($message['message'], 0, 100)) . '...'; ?>
                                </p>
                                <div class="message-actions">
                                    <a href="messages/view.php?id=<?php echo $message['id']; ?>" class="btn-view">
                                        Görüntüle
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <p>Henüz mesaj yok.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Hızlı Eylemler -->
    <div class="quick-actions">
        <h3 class="section-title">Hızlı Eylemler</h3>
        <div class="action-grid">
            <a href="products/add.php" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-plus"></i>
                </div>
                <h4 class="action-title">Yeni Ürün</h4>
                <p class="action-desc">Ürün kataloğuna yeni ürün ekle</p>
            </a>

            <a href="categories/add.php" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-tag"></i>
                </div>
                <h4 class="action-title">Yeni Kategori</h4>
                <p class="action-desc">Ürün kategorisi oluştur</p>
            </a>

            <a href="articles/add.php" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-pen"></i>
                </div>
                <h4 class="action-title">Yeni Makale</h4>
                <p class="action-desc">Blog makalesi yaz</p>
            </a>

            <a href="settings/" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <h4 class="action-title">Ayarlar</h4>
                <p class="action-desc">Site ayarlarını düzenle</p>
            </a>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>