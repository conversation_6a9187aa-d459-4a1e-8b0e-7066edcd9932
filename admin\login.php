<?php
/**
 * GNG Makine - Ad<PERSON>
 */

session_start();
require_once '../config/config.php';

// Zaten giriş yapmışsa dashboard'a yönlendir
if (isAdminLoggedIn()) {
    redirect(admin_url());
}

$page_title = 'Admin G<PERSON>i';
$login_error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    // CSRF token kontrolü
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $login_error = 'Güvenlik hatası. Lütfen sayfayı yenileyin.';
    } else {
        // Kullanıcı doğrulama
        try {
            $user = fetchOne("SELECT * FROM users WHERE username = ? AND status = 'active'", [$username]);
            
            if ($user && password_verify($password, $user['password'])) {
                // Giriş başarılı
                $_SESSION['admin_user_id'] = $user['id'];
                $_SESSION['admin_username'] = $user['username'];
                $_SESSION['admin_role'] = $user['role'];
                $_SESSION['admin_login_time'] = time();
                
                // Son giriş zamanını güncelle
                executeQuery("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);
                
                // Remember me
                if ($remember) {
                    $token = generateRandomString(32);
                    $expires = time() + (30 * 24 * 60 * 60); // 30 gün
                    
                    // Token'ı veritabanına kaydet
                    executeQuery("UPDATE users SET remember_token = ? WHERE id = ?", [$token, $user['id']]);
                    
                    // Cookie'yi set et
                    setcookie('admin_remember', $token, $expires, '/', '', true, true);
                }
                
                // Log kaydı
                writeLog("Admin login: " . $username, 'info');
                
                // Yönlendirme
                $redirect_url = $_GET['redirect'] ?? admin_url();
                redirect($redirect_url);
                
            } else {
                $login_error = 'Kullanıcı adı veya şifre hatalı.';
                writeLog("Failed admin login attempt: " . $username, 'warning');
            }
            
        } catch (Exception $e) {
            $login_error = 'Giriş yapılırken bir hata oluştu.';
            writeLog("Admin login error: " . $e->getMessage(), 'error');
        }
    }
}

// Remember me token kontrolü
if (!isAdminLoggedIn() && isset($_COOKIE['admin_remember'])) {
    try {
        $token = $_COOKIE['admin_remember'];
        $user = fetchOne("SELECT * FROM users WHERE remember_token = ? AND status = 'active'", [$token]);
        
        if ($user) {
            $_SESSION['admin_user_id'] = $user['id'];
            $_SESSION['admin_username'] = $user['username'];
            $_SESSION['admin_role'] = $user['role'];
            $_SESSION['admin_login_time'] = time();
            
            redirect(admin_url());
        }
    } catch (Exception $e) {
        // Token geçersiz, cookie'yi sil
        setcookie('admin_remember', '', time() - 3600, '/');
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo escape($page_title); ?> - <?php echo getSetting('site_name'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-header h2 {
            margin: 0;
            font-weight: 300;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .company-logo {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="company-logo">
                <i class="fas fa-cogs fa-2x"></i>
            </div>
            <h2>GNG Makine</h2>
            <p class="mb-0">Yönetim Paneli</p>
        </div>
        
        <div class="login-body">
            <?php if ($login_error): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo escape($login_error); ?>
            </div>
            <?php endif; ?>
            
            <form method="POST" class="login-form">
                <div class="form-floating mb-3">
                    <input type="text" class="form-control" id="username" name="username" 
                           placeholder="Kullanıcı Adı" required autofocus>
                    <label for="username">
                        <i class="fas fa-user me-2"></i>Kullanıcı Adı
                    </label>
                </div>
                
                <div class="form-floating mb-3">
                    <input type="password" class="form-control" id="password" name="password" 
                           placeholder="Şifre" required>
                    <label for="password">
                        <i class="fas fa-lock me-2"></i>Şifre
                    </label>
                </div>
                
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                    <label class="form-check-label" for="remember">
                        Beni Hatırla
                    </label>
                </div>
                
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <button type="submit" class="btn btn-primary btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>Giriş Yap
                </button>
            </form>
            
            <div class="text-center mt-3">
                <small class="text-muted">
                    <i class="fas fa-shield-alt me-1"></i>
                    Güvenli bağlantı ile korunmaktadır
                </small>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validation
        document.querySelector('.login-form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                e.preventDefault();
                alert('Lütfen tüm alanları doldurun.');
                return false;
            }
            
            // Loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Giriş yapılıyor...';
            submitBtn.disabled = true;
        });
        
        // Auto focus on username field
        document.getElementById('username').focus();
    </script>
</body>
</html>
