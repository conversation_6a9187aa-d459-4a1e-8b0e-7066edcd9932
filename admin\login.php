<?php
session_start();
require_once '../config/config.php';

// <PERSON>ğer zaten giriş yapmışsa admin paneline yönlendir
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: index.php');
    exit;
}

$error_message = '';
$success_message = '';

// Form gönderildiğinde
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        $error_message = 'Kullanıcı adı ve şifre gereklidir.';
    } else {
        try {
            // Debug: Gelen verileri kontrol et
            if (DEVELOPMENT) {
                error_log("Login attempt - Username: " . $username . ", Password length: " . strlen($password));
            }

            // Veritabanından admin kullanıcıyı getir
            $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE username = ? AND status = 'active'");
            $stmt->execute([$username]);
            $admin = $stmt->fetch();

            // Debug: Kullanıcı bulundu mu?
            if (DEVELOPMENT) {
                if ($admin) {
                    error_log("User found: " . $admin['username'] . ", ID: " . $admin['id']);
                } else {
                    error_log("User not found for username: " . $username);

                    // Tüm aktif kullanıcıları listele
                    $debug_stmt = $pdo->prepare("SELECT username, status FROM admin_users");
                    $debug_stmt->execute();
                    $all_users = $debug_stmt->fetchAll();
                    error_log("All users in database: " . print_r($all_users, true));
                }
            }

            if ($admin && password_verify($password, $admin['password'])) {
                // Giriş başarılı
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_user_id'] = $admin['id'];
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['admin_name'] = $admin['full_name'];
                $_SESSION['admin_role'] = $admin['role'];

                // Son giriş zamanını güncelle
                $update_stmt = $pdo->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
                $update_stmt->execute([$admin['id']]);

                header('Location: index.php');
                exit;
            } else {
                // Debug: Şifre kontrolü
                if (DEVELOPMENT && $admin) {
                    error_log("Password verification failed for user: " . $username);
                    error_log("Stored hash: " . $admin['password']);
                    error_log("Password verify result: " . (password_verify($password, $admin['password']) ? 'true' : 'false'));
                }
                $error_message = 'Geçersiz kullanıcı adı veya şifre.';
            }
        } catch (PDOException $e) {
            $error_message = 'Veritabanı hatası: ' . $e->getMessage();
            if (DEVELOPMENT) {
                error_log("Database error in login: " . $e->getMessage());
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Giriş - GNG Makine</title>
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            --primary-color: #F9B233;
            --secondary-color: #8A8C8F;
            --accent-color: #e67e22;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --bg-light: #f8f9fa;
            --white: #ffffff;
            --shadow: 0 4px 20px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: var(--white);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            backdrop-filter: blur(10px);
        }

        .login-header {
            background: linear-gradient(135deg, var(--text-dark) 0%, #34495e 100%);
            color: var(--white);
            padding: 40px 30px;
            text-align: center;
        }

        .login-logo {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .login-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .login-subtitle {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .login-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px 15px 50px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 1rem;
            transition: var(--transition);
            background: var(--white);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(249, 178, 51, 0.1);
        }

        .form-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            font-size: 1.1rem;
        }

        .form-group.has-label .form-icon {
            top: calc(50% + 12px);
        }

        .btn-login {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: var(--white);
            border: none;
            padding: 15px;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            margin-top: 10px;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(249, 178, 51, 0.3);
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-error {
            background: #fee;
            color: #c53030;
            border: 1px solid #fed7d7;
        }

        .alert-success {
            background: #f0fff4;
            color: #2f855a;
            border: 1px solid #c6f6d5;
        }

        .login-footer {
            background: var(--bg-light);
            padding: 20px 30px;
            text-align: center;
            font-size: 0.85rem;
            color: var(--text-light);
        }

        .back-to-site {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }

        .back-to-site:hover {
            color: var(--accent-color);
        }

        /* Loading state */
        .btn-login.loading {
            pointer-events: none;
            opacity: 0.7;
            position: relative;
        }

        .btn-login.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top-color: var(--white);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 480px) {
            .login-container {
                margin: 10px;
            }

            .login-header,
            .login-form {
                padding: 30px 20px;
            }

            .login-footer {
                padding: 15px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Header -->
        <div class="login-header">
            <div class="login-logo">
                <i class="fas fa-cogs"></i>
            </div>
            <h1 class="login-title">Admin Paneli</h1>
            <p class="login-subtitle">GNG Makine Yönetim Sistemi</p>
        </div>

        <!-- Form -->
        <div class="login-form">
            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="" id="loginForm">
                <div class="form-group has-label">
                    <label for="username" class="form-label">Kullanıcı Adı</label>
                    <div style="position: relative;">
                        <i class="fas fa-user form-icon"></i>
                        <input type="text"
                               id="username"
                               name="username"
                               class="form-input"
                               placeholder="Kullanıcı adınızı girin"
                               value="<?php echo htmlspecialchars($username ?? ''); ?>"
                               required>
                    </div>
                </div>

                <div class="form-group has-label">
                    <label for="password" class="form-label">Şifre</label>
                    <div style="position: relative;">
                        <i class="fas fa-lock form-icon"></i>
                        <input type="password"
                               id="password"
                               name="password"
                               class="form-input"
                               placeholder="Şifrenizi girin"
                               required>
                    </div>
                </div>

                <button type="submit" class="btn-login" id="loginBtn">
                    <span class="btn-text">Giriş Yap</span>
                </button>
            </form>
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <a href="<?php echo BASE_URL; ?>" class="back-to-site">
                <i class="fas fa-arrow-left"></i> Ana Siteye Dön
            </a>
        </div>
    </div>

    <script>
        // Form submission with loading state
        document.getElementById('loginForm').addEventListener('submit', function() {
            const btn = document.getElementById('loginBtn');
            btn.classList.add('loading');
            btn.querySelector('.btn-text').textContent = 'Giriş yapılıyor...';
        });

        // Auto-focus on first empty field
        document.addEventListener('DOMContentLoaded', function() {
            const username = document.getElementById('username');
            const password = document.getElementById('password');

            if (!username.value) {
                username.focus();
            } else {
                password.focus();
            }
        });
    </script>
</body>
</html>