<?php
/**
 * GNG Makine - <PERSON><PERSON>
 */

session_start();
require_once '../config/config.php';

// Log kaydı
if (isAdminLoggedIn()) {
    $username = getCurrentUser()['username'] ?? 'Unknown';
    writeLog("Admin logout: " . $username, 'info');
}

// Remember me cookie'sini temizle
if (isset($_COOKIE['admin_remember'])) {
    try {
        // Veritabanından token'ı temizle
        if (isAdminLoggedIn()) {
            $user_id = getCurrentUser()['id'];
            executeQuery("UPDATE users SET remember_token = NULL WHERE id = ?", [$user_id]);
        }
    } catch (Exception $e) {
        // Hata durumunda sessizce devam et
    }
    
    // Cookie'yi sil
    setcookie('admin_remember', '', time() - 3600, '/', '', true, true);
}

// Session'ı temizle
session_destroy();

// Yeni session başlat ve flash mesaj ekle
session_start();
setFlashMessage('Başarıyla çıkış yaptınız.', 'success');

// Login sayfasına yönlendir
redirect(admin_url('login.php'));
?>
