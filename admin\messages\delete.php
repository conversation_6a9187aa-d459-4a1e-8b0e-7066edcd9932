<?php
session_start();
require_once '../../config/config.php';

// <PERSON><PERSON>ş kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

// JSON response için header
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Geçersiz istek']);
    exit;
}

$message_id = (int)($_POST['id'] ?? 0);

if ($message_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Geçersiz mesaj ID']);
    exit;
}

try {
    // Önce mesajın var olup olmadığını kontrol et
    $stmt = $pdo->prepare("SELECT id FROM contact_messages WHERE id = ?");
    $stmt->execute([$message_id]);
    $message = $stmt->fetch();
    
    if (!$message) {
        echo json_encode(['success' => false, 'message' => 'Mesaj bulunamadı']);
        exit;
    }
    
    // Mesajı sil
    $stmt = $pdo->prepare("DELETE FROM contact_messages WHERE id = ?");
    $result = $stmt->execute([$message_id]);
    
    if ($result) {
        echo json_encode([
            'success' => true, 
            'message' => 'Mesaj başarıyla silindi'
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'Mesaj silinirken bir hata oluştu'
        ]);
    }
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'Veritabanı hatası: ' . $e->getMessage()
    ]);
}
?>
