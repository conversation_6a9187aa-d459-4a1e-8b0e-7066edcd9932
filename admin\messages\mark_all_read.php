<?php
session_start();
require_once '../../config/config.php';

// <PERSON><PERSON>ş kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

// JSON response için header
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Geçersiz istek']);
    exit;
}

try {
    // Tüm okunmamış mesajları okundu olarak işaretle
    $stmt = $pdo->prepare("UPDATE contact_messages SET status = 'read' WHERE status = 'unread'");
    $result = $stmt->execute();
    
    $affected_rows = $stmt->rowCount();
    
    if ($result) {
        echo json_encode([
            'success' => true, 
            'message' => $affected_rows . ' mesaj okundu olarak işaretlendi'
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'Mesajlar güncellenirken bir hata oluştu'
        ]);
    }
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'Veritabanı hatası: ' . $e->getMessage()
    ]);
}
?>
