<?php
session_start();
require_once '../../config/config.php';

// <PERSON><PERSON>ş kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

// JSON response için header
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Geçersiz istek']);
    exit;
}

$message_id = (int)($_POST['id'] ?? 0);

if ($message_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Geçersiz mesaj ID']);
    exit;
}

try {
    // Mesajı yanıtlandı olarak işaretle
    $stmt = $pdo->prepare("UPDATE contact_messages SET status = 'replied' WHERE id = ?");
    $result = $stmt->execute([$message_id]);
    
    if ($result) {
        echo json_encode([
            'success' => true, 
            'message' => 'Mesaj yanıtlandı olarak işaretlendi'
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'Mesaj güncellenirken bir hata oluştu'
        ]);
    }
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'Veritabanı hatası: ' . $e->getMessage()
    ]);
}
?>
