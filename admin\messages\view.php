<?php
session_start();
require_once '../../config/config.php';

// G<PERSON>ş kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    exit('Unauthorized');
}

$message_id = (int)($_GET['id'] ?? 0);

if ($message_id <= 0) {
    exit('Geçersiz mesaj ID');
}

try {
    // Mesajı getir
    $stmt = $pdo->prepare("SELECT * FROM contact_messages WHERE id = ?");
    $stmt->execute([$message_id]);
    $message = $stmt->fetch();
    
    if (!$message) {
        exit('Mesaj bulunamadı');
    }
    
    // Mesajı okundu olarak işaretle
    if ($message['status'] === 'unread') {
        $update_stmt = $pdo->prepare("UPDATE contact_messages SET status = 'read' WHERE id = ?");
        $update_stmt->execute([$message_id]);
    }
    
} catch (PDOException $e) {
    exit('Veritabanı hatası: ' . $e->getMessage());
}
?>

<div class="message-detail">
    <div class="message-info">
        <div class="info-row">
            <label>Gönderen:</label>
            <span><?php echo htmlspecialchars($message['name']); ?></span>
        </div>
        
        <div class="info-row">
            <label>E-posta:</label>
            <span>
                <a href="mailto:<?php echo htmlspecialchars($message['email']); ?>" class="email-link">
                    <?php echo htmlspecialchars($message['email']); ?>
                </a>
            </span>
        </div>
        
        <?php if (!empty($message['phone'])): ?>
            <div class="info-row">
                <label>Telefon:</label>
                <span>
                    <a href="tel:<?php echo htmlspecialchars($message['phone']); ?>" class="phone-link">
                        <?php echo htmlspecialchars($message['phone']); ?>
                    </a>
                </span>
            </div>
        <?php endif; ?>
        
        <div class="info-row">
            <label>Konu:</label>
            <span><?php echo htmlspecialchars($message['subject']); ?></span>
        </div>
        
        <div class="info-row">
            <label>Tarih:</label>
            <span><?php echo date('d.m.Y H:i:s', strtotime($message['created_at'])); ?></span>
        </div>
        
        <div class="info-row">
            <label>Durum:</label>
            <span class="status-badge status-<?php echo $message['status']; ?>">
                <?php 
                switch($message['status']) {
                    case 'unread': echo 'Okunmamış'; break;
                    case 'read': echo 'Okunmuş'; break;
                    case 'replied': echo 'Yanıtlanmış'; break;
                }
                ?>
            </span>
        </div>
    </div>
    
    <div class="message-content">
        <h4>Mesaj İçeriği:</h4>
        <div class="content-text">
            <?php echo nl2br(htmlspecialchars($message['message'])); ?>
        </div>
    </div>
    
    <div class="message-actions">
        <a href="mailto:<?php echo htmlspecialchars($message['email']); ?>?subject=Re: <?php echo urlencode($message['subject']); ?>" 
           class="btn btn-primary">
            <i class="fas fa-reply"></i>
            E-posta ile Yanıtla
        </a>
        
        <button onclick="markAsReplied(<?php echo $message['id']; ?>)" class="btn btn-success">
            <i class="fas fa-check"></i>
            Yanıtlandı İşaretle
        </button>
        
        <button onclick="deleteMessage(<?php echo $message['id']; ?>)" class="btn btn-danger">
            <i class="fas fa-trash"></i>
            Mesajı Sil
        </button>
    </div>
</div>

<script>
function markAsReplied(id) {
    if (confirm('Bu mesajı yanıtlandı olarak işaretlemek istediğinizden emin misiniz?')) {
        fetch('mark_replied.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id=' + id
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                closeModal();
                location.reload();
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('Bir hata oluştu: ' + error);
        });
    }
}

function deleteMessage(id) {
    if (confirm('Bu mesajı silmek istediğinizden emin misiniz?')) {
        fetch('delete.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id=' + id
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                closeModal();
                location.reload();
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('Bir hata oluştu: ' + error);
        });
    }
}
</script>

<style>
.message-detail {
    font-family: inherit;
}

.message-info {
    background: var(--bg-light);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
}

.info-row {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-start;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-row label {
    font-weight: 600;
    color: var(--text-dark);
    min-width: 100px;
    margin-right: 15px;
}

.info-row span {
    color: var(--text-dark);
    flex: 1;
}

.email-link,
.phone-link {
    color: var(--primary-color);
    text-decoration: none;
}

.email-link:hover,
.phone-link:hover {
    text-decoration: underline;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-unread {
    background: #fff3cd;
    color: #856404;
}

.status-read {
    background: #d1ecf1;
    color: #0c5460;
}

.status-replied {
    background: #d4edda;
    color: #155724;
}

.message-content {
    margin-bottom: 30px;
}

.message-content h4 {
    color: var(--text-dark);
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.content-text {
    background: var(--white);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e0e0e0;
    line-height: 1.6;
    color: var(--text-dark);
    white-space: pre-wrap;
}

.message-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-success {
    background: #28a745;
    color: var(--white);
}

.btn-danger {
    background: #dc3545;
    color: var(--white);
}

.btn:hover {
    transform: translateY(-2px);
    opacity: 0.9;
}

/* Responsive */
@media (max-width: 768px) {
    .info-row {
        flex-direction: column;
        gap: 5px;
    }
    
    .info-row label {
        min-width: auto;
        margin-right: 0;
    }
    
    .message-actions {
        flex-direction: column;
    }
    
    .btn {
        justify-content: center;
    }
}
</style>
