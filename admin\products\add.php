<?php
session_start();
require_once '../../config/config.php';

// <PERSON><PERSON>ş kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

$error_message = '';
$success_message = '';

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $category_id = (int)($_POST['category_id'] ?? 0);
    $status = $_POST['status'] ?? 'active';
    $meta_title = trim($_POST['meta_title'] ?? '');
    $meta_description = trim($_POST['meta_description'] ?? '');
    
    // Validasyon
    if (empty($name)) {
        $error_message = 'Ürün adı gereklidir.';
    } elseif (empty($description)) {
        $error_message = 'Ürün açıklaması gereklidir.';
    } else {
        try {
            // Slug oluştur
            $slug = createSlug($name);
            
            // Ürünü veritabanına ekle
            $sql = "INSERT INTO products (name, slug, description, category_id, status, meta_title, meta_description, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$name, $slug, $description, $category_id, $status, $meta_title, $meta_description]);
            
            if ($result) {
                $success_message = 'Ürün başarıyla eklendi.';
                // Formu temizle
                $name = $description = $meta_title = $meta_description = '';
                $category_id = 0;
                $status = 'active';
            } else {
                $error_message = 'Ürün eklenirken bir hata oluştu.';
            }
            
        } catch (PDOException $e) {
            $error_message = 'Veritabanı hatası: ' . $e->getMessage();
        }
    }
}

// Kategorileri getir
try {
    $categories_stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name");
    $categories = $categories_stmt->fetchAll();
} catch (PDOException $e) {
    $categories = [];
}

$page_title = 'Yeni Ürün Ekle';
include '../includes/header.php';
?>

<div class="admin-content">
    <div class="content-header">
        <h1 class="content-title">
            <i class="fas fa-plus"></i>
            Yeni Ürün Ekle
        </h1>
        <div class="content-actions">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Geri Dön
            </a>
        </div>
    </div>
    
    <?php if ($error_message): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>
    
    <div class="form-card">
        <form method="POST" class="product-form">
            <div class="form-grid">
                <div class="form-group">
                    <label for="name" class="form-label">
                        <i class="fas fa-tag"></i>
                        Ürün Adı *
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           class="form-input" 
                           value="<?php echo htmlspecialchars($name ?? ''); ?>" 
                           required>
                </div>
                
                <div class="form-group">
                    <label for="category_id" class="form-label">
                        <i class="fas fa-folder"></i>
                        Kategori
                    </label>
                    <select id="category_id" name="category_id" class="form-select">
                        <option value="0">Kategori Seçin</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" 
                                    <?php echo ($category_id ?? 0) == $category['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="status" class="form-label">
                        <i class="fas fa-toggle-on"></i>
                        Durum
                    </label>
                    <select id="status" name="status" class="form-select">
                        <option value="active" <?php echo ($status ?? 'active') === 'active' ? 'selected' : ''; ?>>Aktif</option>
                        <option value="inactive" <?php echo ($status ?? 'active') === 'inactive' ? 'selected' : ''; ?>>Pasif</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="description" class="form-label">
                    <i class="fas fa-align-left"></i>
                    Ürün Açıklaması *
                </label>
                <textarea id="description" 
                          name="description" 
                          class="form-textarea" 
                          rows="6" 
                          required><?php echo htmlspecialchars($description ?? ''); ?></textarea>
            </div>
            
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-search"></i>
                    SEO Ayarları
                </h3>
                
                <div class="form-group">
                    <label for="meta_title" class="form-label">Meta Başlık</label>
                    <input type="text" 
                           id="meta_title" 
                           name="meta_title" 
                           class="form-input" 
                           value="<?php echo htmlspecialchars($meta_title ?? ''); ?>" 
                           maxlength="60">
                    <small class="form-help">Maksimum 60 karakter</small>
                </div>
                
                <div class="form-group">
                    <label for="meta_description" class="form-label">Meta Açıklama</label>
                    <textarea id="meta_description" 
                              name="meta_description" 
                              class="form-textarea" 
                              rows="3" 
                              maxlength="160"><?php echo htmlspecialchars($meta_description ?? ''); ?></textarea>
                    <small class="form-help">Maksimum 160 karakter</small>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Ürünü Kaydet
                </button>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    İptal
                </a>
            </div>
        </form>
    </div>
</div>

<style>
/* Form Styles */
.form-card {
    background: var(--white);
    border-radius: 15px;
    padding: 30px;
    box-shadow: var(--shadow);
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: var(--transition);
    font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(249, 178, 51, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.form-help {
    display: block;
    margin-top: 5px;
    font-size: 0.8rem;
    color: var(--text-light);
}

.form-section {
    margin: 40px 0;
    padding: 25px;
    background: var(--bg-light);
    border-radius: 10px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid #e0e0e0;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background: #6c757d;
}

.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Responsive */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        justify-content: center;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
