<?php
session_start();
require_once '../../config/config.php';

// G<PERSON>ş kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

// JSON response için header
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Geçersiz istek']);
    exit;
}

$product_id = (int)($_POST['id'] ?? 0);

if ($product_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Geçersiz ürün ID']);
    exit;
}

try {
    // Önce ürünün var olup olmadığını kontrol et
    $stmt = $pdo->prepare("SELECT id, name, main_image FROM products WHERE id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();
    
    if (!$product) {
        echo json_encode(['success' => false, 'message' => 'Ürün bulunamadı']);
        exit;
    }
    
    // Ürünü sil
    $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
    $result = $stmt->execute([$product_id]);
    
    if ($result) {
        // Eğer ürünün resmi varsa, dosyayı da sil
        if (!empty($product['main_image'])) {
            $image_path = '../../uploads/products/' . $product['main_image'];
            if (file_exists($image_path)) {
                unlink($image_path);
            }
        }
        
        echo json_encode([
            'success' => true, 
            'message' => 'Ürün başarıyla silindi'
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'Ürün silinirken bir hata oluştu'
        ]);
    }
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'Veritabanı hatası: ' . $e->getMessage()
    ]);
}
?>
