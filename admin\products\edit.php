<?php
session_start();
require_once '../../config/config.php';
require_once '../includes/ImageUploader.php';

// <PERSON>iriş kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

$error_message = '';
$success_message = '';
$imageUploader = new ImageUploader();
$product_id = (int)($_GET['id'] ?? 0);

if ($product_id <= 0) {
    header('Location: index.php');
    exit;
}

// Ürünü getir
try {
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();
    
    if (!$product) {
        header('Location: index.php');
        exit;
    }
} catch (PDOException $e) {
    $error_message = 'Ürün bulunamadı.';
}

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $short_description = trim($_POST['short_description'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $specifications = trim($_POST['specifications'] ?? '');
    $features = trim($_POST['features'] ?? '');
    $category_id = (int)($_POST['category_id'] ?? 0);
    $price = !empty($_POST['price']) ? (float)$_POST['price'] : null;
    $currency = $_POST['currency'] ?? 'TRY';
    $stock_status = $_POST['stock_status'] ?? 'in_stock';
    $featured = isset($_POST['featured']) ? 1 : 0;
    $status = $_POST['status'] ?? 'active';
    $meta_title = trim($_POST['meta_title'] ?? '');
    $meta_description = trim($_POST['meta_description'] ?? '');

    // Validasyon
    if (empty($name)) {
        $error_message = 'Ürün adı gereklidir.';
    } elseif (empty($description)) {
        $error_message = 'Ürün açıklaması gereklidir.';
    } else {
        try {
            // Slug oluştur
            $slug = createSlug($name);

            // Mevcut resim bilgilerini al
            $current_main_image = $product['main_image'];
            $current_gallery_images = $product['gallery_images'];

            // Ana resim yükleme
            $main_image = $current_main_image; // Mevcut resmi koru
            if (isset($_FILES['main_image']) && $_FILES['main_image']['error'] === UPLOAD_ERR_OK) {
                $upload_result = $imageUploader->uploadSingle($_FILES['main_image'], 'main_');
                if ($upload_result['success']) {
                    // Eski resmi sil
                    if ($current_main_image) {
                        $imageUploader->deleteImage($current_main_image);
                    }
                    $main_image = $upload_result['filename'];
                } else {
                    $error_message = 'Ana resim yüklenirken hata: ' . $upload_result['error'];
                }
            }

            // Ana resim silme kontrolü
            if (isset($_POST['remove_main_image']) && $_POST['remove_main_image'] === '1') {
                if ($current_main_image) {
                    $imageUploader->deleteImage($current_main_image);
                }
                $main_image = null;
            }

            // Galeri resimleri yükleme
            $gallery_images = $current_gallery_images; // Mevcut resimleri koru
            if (isset($_FILES['gallery_images']) && !empty($_FILES['gallery_images']['name'][0])) {
                $gallery_result = $imageUploader->uploadMultiple($_FILES['gallery_images'], 'gallery_');
                if ($gallery_result['success_count'] > 0) {
                    // Mevcut galeri resimlerini al
                    $existing_gallery = !empty($current_gallery_images) ? explode(',', $current_gallery_images) : [];
                    // Yeni resimleri ekle
                    $all_gallery = array_merge($existing_gallery, $gallery_result['uploaded_files']);
                    $gallery_images = implode(',', $all_gallery);
                }
            }

            // Galeri resmi silme kontrolü
            if (isset($_POST['remove_gallery_images']) && !empty($_POST['remove_gallery_images'])) {
                $images_to_remove = explode(',', $_POST['remove_gallery_images']);
                foreach ($images_to_remove as $image_to_remove) {
                    $imageUploader->deleteImage(trim($image_to_remove));
                }

                // Kalan resimleri güncelle
                $existing_gallery = !empty($current_gallery_images) ? explode(',', $current_gallery_images) : [];
                $remaining_gallery = array_diff($existing_gallery, $images_to_remove);
                $gallery_images = !empty($remaining_gallery) ? implode(',', $remaining_gallery) : null;
            }

            if (empty($error_message)) {
                // Ürünü güncelle
                $sql = "UPDATE products SET
                        name = ?, slug = ?, short_description = ?, description = ?, specifications = ?, features = ?,
                        main_image = ?, gallery_images = ?, category_id = ?, price = ?, currency = ?, stock_status = ?,
                        featured = ?, status = ?, meta_title = ?, meta_description = ?, updated_at = NOW()
                        WHERE id = ?";

                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute([
                    $name, $slug, $short_description, $description, $specifications, $features,
                    $main_image, $gallery_images, $category_id, $price, $currency, $stock_status,
                    $featured, $status, $meta_title, $meta_description, $product_id
                ]);

                if ($result) {
                    $success_message = 'Ürün başarıyla güncellendi.';
                    // Güncel veriyi al
                    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
                    $stmt->execute([$product_id]);
                    $product = $stmt->fetch();
                } else {
                    $error_message = 'Ürün güncellenirken bir hata oluştu.';
                }
            }

        } catch (PDOException $e) {
            $error_message = 'Veritabanı hatası: ' . $e->getMessage();
        }
    }
}

// Kategorileri getir
try {
    $categories_stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name");
    $categories = $categories_stmt->fetchAll();
} catch (PDOException $e) {
    $categories = [];
}

$page_title = 'Ürün Düzenle';
include '../includes/header.php';
?>

<div class="admin-content">
    <div class="content-header">
        <h1 class="content-title">
            <i class="fas fa-edit"></i>
            Ürün Düzenle
        </h1>
        <div class="content-actions">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Geri Dön
            </a>
        </div>
    </div>
    
    <?php if ($error_message): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>
    
    <div class="form-card">
        <form method="POST" enctype="multipart/form-data" class="product-form">
            <div class="form-grid">
                <div class="form-group">
                    <label for="name" class="form-label">
                        <i class="fas fa-tag"></i>
                        Ürün Adı *
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           class="form-input" 
                           value="<?php echo htmlspecialchars($product['name']); ?>" 
                           required>
                </div>
                
                <div class="form-group">
                    <label for="category_id" class="form-label">
                        <i class="fas fa-folder"></i>
                        Kategori
                    </label>
                    <select id="category_id" name="category_id" class="form-select">
                        <option value="0">Kategori Seçin</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" 
                                    <?php echo $product['category_id'] == $category['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="status" class="form-label">
                        <i class="fas fa-toggle-on"></i>
                        Durum
                    </label>
                    <select id="status" name="status" class="form-select">
                        <option value="active" <?php echo $product['status'] === 'active' ? 'selected' : ''; ?>>Aktif</option>
                        <option value="inactive" <?php echo $product['status'] === 'inactive' ? 'selected' : ''; ?>>Pasif</option>
                    </select>
                </div>
            </div>
            
            <!-- Kısa Açıklama -->
            <div class="form-group">
                <label for="short_description" class="form-label">
                    <i class="fas fa-align-center"></i>
                    Kısa Açıklama
                </label>
                <textarea id="short_description"
                          name="short_description"
                          class="form-textarea"
                          rows="3"
                          placeholder="Ürünün kısa açıklaması..."><?php echo htmlspecialchars($product['short_description'] ?? ''); ?></textarea>
            </div>

            <!-- Detaylı Açıklama -->
            <div class="form-group">
                <label for="description" class="form-label">
                    <i class="fas fa-align-left"></i>
                    Detaylı Açıklama *
                </label>
                <textarea id="description"
                          name="description"
                          class="form-textarea"
                          rows="8"
                          required><?php echo htmlspecialchars($product['description']); ?></textarea>
            </div>

            <!-- Teknik Özellikler ve Özellikler -->
            <div class="form-grid">
                <div class="form-group">
                    <label for="specifications" class="form-label">
                        <i class="fas fa-cogs"></i>
                        Teknik Özellikler
                    </label>
                    <textarea id="specifications"
                              name="specifications"
                              class="form-textarea"
                              rows="6"
                              placeholder="Boyutlar, güç, kapasite vb..."><?php echo htmlspecialchars($product['specifications'] ?? ''); ?></textarea>
                </div>

                <div class="form-group">
                    <label for="features" class="form-label">
                        <i class="fas fa-star"></i>
                        Özellikler
                    </label>
                    <textarea id="features"
                              name="features"
                              class="form-textarea"
                              rows="6"
                              placeholder="Ürünün öne çıkan özellikleri..."><?php echo htmlspecialchars($product['features'] ?? ''); ?></textarea>
                </div>
            </div>

            <!-- Fiyat ve Stok Bilgileri -->
            <div class="form-grid">
                <div class="form-group">
                    <label for="price" class="form-label">
                        <i class="fas fa-tag"></i>
                        Fiyat
                    </label>
                    <div class="input-group">
                        <input type="number"
                               id="price"
                               name="price"
                               class="form-input"
                               step="0.01"
                               value="<?php echo htmlspecialchars($product['price'] ?? ''); ?>"
                               placeholder="0.00">
                        <select name="currency" class="form-select currency-select">
                            <option value="TRY" <?php echo ($product['currency'] ?? 'TRY') === 'TRY' ? 'selected' : ''; ?>>TRY</option>
                            <option value="USD" <?php echo ($product['currency'] ?? '') === 'USD' ? 'selected' : ''; ?>>USD</option>
                            <option value="EUR" <?php echo ($product['currency'] ?? '') === 'EUR' ? 'selected' : ''; ?>>EUR</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="stock_status" class="form-label">
                        <i class="fas fa-boxes"></i>
                        Stok Durumu
                    </label>
                    <select id="stock_status" name="stock_status" class="form-select">
                        <option value="in_stock" <?php echo ($product['stock_status'] ?? 'in_stock') === 'in_stock' ? 'selected' : ''; ?>>Stokta</option>
                        <option value="out_of_stock" <?php echo ($product['stock_status'] ?? '') === 'out_of_stock' ? 'selected' : ''; ?>>Stok Yok</option>
                        <option value="on_order" <?php echo ($product['stock_status'] ?? '') === 'on_order' ? 'selected' : ''; ?>>Sipariş Üzerine</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-star"></i>
                        Öne Çıkan Ürün
                    </label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox"
                               id="featured"
                               name="featured"
                               value="1"
                               <?php echo ($product['featured'] ?? 0) ? 'checked' : ''; ?>>
                        <label for="featured" class="checkbox-label">Bu ürünü ana sayfada öne çıkar</label>
                    </div>
                </div>
            </div>

            <!-- Fotoğraf Yönetimi -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-images"></i>
                    Fotoğraf Yönetimi
                </h3>

                <div class="form-grid">
                    <!-- Mevcut Ana Resim -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-image"></i>
                            Mevcut Ana Resim
                        </label>
                        <?php if (!empty($product['main_image'])): ?>
                            <div class="current-image">
                                <img src="<?php echo UPLOADS_URL; ?>/products/<?php echo htmlspecialchars($product['main_image']); ?>"
                                     alt="Ana resim" class="current-image-preview">
                                <div class="image-actions">
                                    <button type="button" class="btn btn-danger btn-sm" onclick="removeCurrentMainImage()">
                                        <i class="fas fa-trash"></i> Sil
                                    </button>
                                </div>
                                <input type="hidden" id="remove_main_image" name="remove_main_image" value="0">
                            </div>
                        <?php else: ?>
                            <div class="no-image">
                                <i class="fas fa-image"></i>
                                <span>Ana resim yok</span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Yeni Ana Resim -->
                    <div class="form-group">
                        <label for="main_image" class="form-label">
                            <i class="fas fa-upload"></i>
                            Yeni Ana Resim
                        </label>
                        <div class="file-upload-area" onclick="document.getElementById('main_image').click()">
                            <input type="file"
                                   id="main_image"
                                   name="main_image"
                                   class="file-input"
                                   accept="image/*"
                                   onchange="previewMainImage(this)">
                            <div class="file-upload-text">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <span>Yeni ana resim seçin</span>
                                <small>JPG, PNG, GIF, WebP - Max 5MB</small>
                            </div>
                        </div>
                        <div id="main_image_preview" class="image-preview"></div>
                    </div>
                </div>

                <!-- Mevcut Galeri Resimleri -->
                <?php if (!empty($product['gallery_images'])): ?>
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-images"></i>
                            Mevcut Galeri Resimleri
                        </label>
                        <div class="current-gallery">
                            <?php
                            $gallery_images = explode(',', $product['gallery_images']);
                            foreach ($gallery_images as $index => $image):
                                $image = trim($image);
                                if (!empty($image)):
                            ?>
                                <div class="gallery-item current-gallery-item" data-image="<?php echo htmlspecialchars($image); ?>">
                                    <img src="<?php echo UPLOADS_URL; ?>/products/<?php echo htmlspecialchars($image); ?>"
                                         alt="Galeri resim <?php echo $index + 1; ?>">
                                    <button type="button" class="remove-gallery-item" onclick="removeCurrentGalleryImage('<?php echo htmlspecialchars($image); ?>')">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            <?php
                                endif;
                            endforeach;
                            ?>
                        </div>
                        <input type="hidden" id="remove_gallery_images" name="remove_gallery_images" value="">
                    </div>
                <?php endif; ?>

                <!-- Yeni Galeri Resimleri -->
                <div class="form-group">
                    <label for="gallery_images" class="form-label">
                        <i class="fas fa-plus"></i>
                        Yeni Galeri Resimleri Ekle
                    </label>
                    <div class="file-upload-area" onclick="document.getElementById('gallery_images').click()">
                        <input type="file"
                               id="gallery_images"
                               name="gallery_images[]"
                               class="file-input"
                               accept="image/*"
                               multiple
                               onchange="previewGalleryImages(this)">
                        <div class="file-upload-text">
                            <i class="fas fa-images"></i>
                            <span>Yeni galeri resimlerini seçin (Çoklu seçim)</span>
                            <small>JPG, PNG, GIF, WebP - Max 5MB her biri</small>
                        </div>
                    </div>
                    <div id="gallery_images_preview" class="gallery-preview"></div>
                </div>
            </div>

            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-search"></i>
                    SEO Ayarları
                </h3>
                
                <div class="form-group">
                    <label for="meta_title" class="form-label">Meta Başlık</label>
                    <input type="text" 
                           id="meta_title" 
                           name="meta_title" 
                           class="form-input" 
                           value="<?php echo htmlspecialchars($product['meta_title'] ?? ''); ?>" 
                           maxlength="60">
                    <small class="form-help">Maksimum 60 karakter</small>
                </div>
                
                <div class="form-group">
                    <label for="meta_description" class="form-label">Meta Açıklama</label>
                    <textarea id="meta_description" 
                              name="meta_description" 
                              class="form-textarea" 
                              rows="3" 
                              maxlength="160"><?php echo htmlspecialchars($product['meta_description'] ?? ''); ?></textarea>
                    <small class="form-help">Maksimum 160 karakter</small>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Değişiklikleri Kaydet
                </button>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    İptal
                </a>
            </div>
        </form>
    </div>
</div>

<style>
/* Form Styles - Same as add.php */
.form-card {
    background: var(--white);
    border-radius: 15px;
    padding: 30px;
    box-shadow: var(--shadow);
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: var(--transition);
    font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(249, 178, 51, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.form-help {
    display: block;
    margin-top: 5px;
    font-size: 0.8rem;
    color: var(--text-light);
}

.form-section {
    margin: 40px 0;
    padding: 25px;
    background: var(--bg-light);
    border-radius: 10px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid #e0e0e0;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background: #6c757d;
}

.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Fotoğraf Yönetimi Stilleri */
.input-group {
    display: flex;
    gap: 0;
}

.input-group .form-input {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    flex: 1;
}

.currency-select {
    width: 80px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: none;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 0;
}

.checkbox-label {
    font-weight: normal;
    margin: 0;
    cursor: pointer;
}

.file-upload-area {
    border: 2px dashed #ddd;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: #f0f8ff;
}

.file-input {
    display: none;
}

.file-upload-text i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
    display: block;
}

.current-image {
    position: relative;
    display: inline-block;
    margin-bottom: 15px;
}

.current-image-preview {
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.image-actions {
    margin-top: 10px;
}

.no-image {
    padding: 40px;
    text-align: center;
    color: #999;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #ddd;
}

.no-image i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.current-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.current-gallery-item {
    position: relative;
    aspect-ratio: 1;
}

.current-gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.remove-gallery-item {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-preview {
    margin-top: 15px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* Responsive */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        justify-content: center;
    }

    .current-gallery {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}
</style>

<script>
// Mevcut ana resmi silme
function removeCurrentMainImage() {
    if (confirm('Ana resmi silmek istediğinizden emin misiniz?')) {
        document.getElementById('remove_main_image').value = '1';
        document.querySelector('.current-image').style.display = 'none';
    }
}

// Mevcut galeri resmini silme
let removedGalleryImages = [];

function removeCurrentGalleryImage(imageName) {
    if (confirm('Bu resmi silmek istediğinizden emin misiniz?')) {
        // Resmi listeden kaldır
        removedGalleryImages.push(imageName);
        document.getElementById('remove_gallery_images').value = removedGalleryImages.join(',');

        // DOM'dan kaldır
        const imageElement = document.querySelector(`[data-image="${imageName}"]`);
        if (imageElement) {
            imageElement.style.display = 'none';
        }
    }
}

// Ana resim önizleme
function previewMainImage(input) {
    const preview = document.getElementById('main_image_preview');
    preview.innerHTML = '';

    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Dosya boyutu kontrolü (5MB)
        if (file.size > 5242880) {
            alert('Dosya boyutu çok büyük! Maksimum 5MB olmalıdır.');
            input.value = '';
            return;
        }

        // Dosya türü kontrolü
        if (!file.type.match('image.*')) {
            alert('Lütfen sadece resim dosyası seçin!');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            const previewDiv = document.createElement('div');
            previewDiv.className = 'preview-image';
            previewDiv.innerHTML = `
                <img src="${e.target.result}" alt="Ana resim önizleme" style="max-width: 200px; max-height: 150px; border-radius: 8px;">
                <button type="button" class="remove-preview" onclick="removeMainImagePreview()" style="position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border: none; border-radius: 50%; width: 24px; height: 24px; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            `;
            previewDiv.style.position = 'relative';
            previewDiv.style.display = 'inline-block';
            previewDiv.style.marginTop = '15px';
            preview.appendChild(previewDiv);
        };
        reader.readAsDataURL(file);
    }
}

// Ana resim önizlemesini silme
function removeMainImagePreview() {
    document.getElementById('main_image').value = '';
    document.getElementById('main_image_preview').innerHTML = '';
}

// Galeri resimleri önizleme
function previewGalleryImages(input) {
    const preview = document.getElementById('gallery_images_preview');
    preview.innerHTML = '';

    if (input.files && input.files.length > 0) {
        // Maksimum 10 resim kontrolü
        if (input.files.length > 10) {
            alert('Maksimum 10 resim seçebilirsiniz!');
            input.value = '';
            return;
        }

        Array.from(input.files).forEach((file, index) => {
            // Dosya boyutu kontrolü (5MB)
            if (file.size > 5242880) {
                alert(`${file.name} dosyası çok büyük! Maksimum 5MB olmalıdır.`);
                return;
            }

            // Dosya türü kontrolü
            if (!file.type.match('image.*')) {
                alert(`${file.name} bir resim dosyası değil!`);
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const galleryItem = document.createElement('div');
                galleryItem.className = 'gallery-item';
                galleryItem.style.position = 'relative';
                galleryItem.style.aspectRatio = '1';
                galleryItem.innerHTML = `
                    <img src="${e.target.result}" alt="Galeri resim ${index + 1}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">
                    <button type="button" class="remove-preview" onclick="removeGalleryImagePreview(${index})" style="position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border: none; border-radius: 50%; width: 24px; height: 24px; cursor: pointer;">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                preview.appendChild(galleryItem);
            };
            reader.readAsDataURL(file);
        });
    }
}

// Galeri resmi önizlemesini silme
function removeGalleryImagePreview(index) {
    const input = document.getElementById('gallery_images');
    const dt = new DataTransfer();

    Array.from(input.files).forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    input.files = dt.files;
    previewGalleryImages(input);
}

// Form gönderme öncesi kontrol
document.addEventListener('DOMContentLoaded', function() {
    document.querySelector('.product-form').addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const description = document.getElementById('description').value.trim();

        if (!name) {
            alert('Ürün adı gereklidir!');
            e.preventDefault();
            return false;
        }

        if (!description) {
            alert('Ürün açıklaması gereklidir!');
            e.preventDefault();
            return false;
        }

        // Form gönderiliyor mesajı
        const submitBtn = document.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Güncelleniyor...';
        submitBtn.disabled = true;

        // Hata durumunda butonu eski haline getir
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 5000);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
