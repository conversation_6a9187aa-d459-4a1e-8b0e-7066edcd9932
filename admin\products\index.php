<?php
session_start();
require_once '../../config/config.php';

// Giriş kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

// Sayfalama ayarları
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 10;
$offset = ($page - 1) * $per_page;

// Arama ve filtreleme
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

// WHERE koşulları
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(p.name LIKE ? OR p.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($category_filter > 0) {
    $where_conditions[] = "p.category_id = ?";
    $params[] = $category_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = "p.status = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    // Toplam ürün sayısı
    $count_sql = "SELECT COUNT(*) as total FROM products p $where_clause";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total_products = $count_stmt->fetch()['total'];

    // Toplam sayfa sayısı
    $total_pages = ceil($total_products / $per_page);

    // Ürünleri getir
    $sql = "
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        $where_clause
        ORDER BY p.created_at DESC
        LIMIT $per_page OFFSET $offset
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll();

    // Kategorileri getir (filtre için)
    $categories_stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name");
    $categories = $categories_stmt->fetchAll();

} catch (PDOException $e) {
    $error_message = 'Veritabanı hatası: ' . $e->getMessage();
}

$page_title = 'Ürünler';
include '../includes/header.php';
?>

<div class="admin-content">
    <div class="content-header">
        <h1 class="content-title">
            <i class="fas fa-boxes"></i>
            Ürünler
        </h1>
        <div class="content-actions">
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Yeni Ürün Ekle
            </a>
        </div>
    </div>

    <!-- Filtreler -->
    <div class="filters-card">
        <form method="GET" class="filters-form">
            <div class="filter-group">
                <input type="text"
                       name="search"
                       placeholder="Ürün ara..."
                       value="<?php echo htmlspecialchars($search); ?>"
                       class="filter-input">
            </div>

            <div class="filter-group">
                <select name="category" class="filter-select">
                    <option value="">Tüm Kategoriler</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>"
                                <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="filter-group">
                <select name="status" class="filter-select">
                    <option value="">Tüm Durumlar</option>
                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Aktif</option>
                    <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>Pasif</option>
                </select>
            </div>

            <div class="filter-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    Filtrele
                </button>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    Temizle
                </a>
            </div>
        </form>
    </div>

    <!-- Ürün Listesi -->
    <div class="data-card">
        <div class="card-header">
            <h3 class="card-title">
                Ürün Listesi
                <span class="count-badge"><?php echo number_format($total_products); ?></span>
            </h3>
        </div>

        <div class="card-body">
            <?php if (!empty($products)): ?>
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Görsel</th>
                                <th>Ürün Adı</th>
                                <th>Kategori</th>
                                <th>Durum</th>
                                <th>Oluşturma Tarihi</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($products as $product): ?>
                                <tr>
                                    <td>
                                        <div class="product-image">
                                            <?php if ($product['main_image']): ?>
                                                <img src="<?php echo UPLOADS_URL; ?>/products/<?php echo htmlspecialchars($product['main_image']); ?>"
                                                     alt="<?php echo htmlspecialchars($product['name']); ?>">
                                            <?php else: ?>
                                                <div class="placeholder-image">
                                                    <i class="fas fa-image"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="product-info">
                                            <h4 class="product-name"><?php echo htmlspecialchars($product['name']); ?></h4>
                                            <p class="product-description">
                                                <?php echo htmlspecialchars(substr($product['description'], 0, 100)) . '...'; ?>
                                            </p>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($product['category_name']): ?>
                                            <span class="category-badge">
                                                <?php echo htmlspecialchars($product['category_name']); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="no-category">Kategori Yok</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo $product['status']; ?>">
                                            <?php echo $product['status'] === 'active' ? 'Aktif' : 'Pasif'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="date-text">
                                            <?php echo date('d.m.Y H:i', strtotime($product['created_at'])); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="view.php?id=<?php echo $product['id']; ?>"
                                               class="btn-action btn-view"
                                               title="Görüntüle">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $product['id']; ?>"
                                               class="btn-action btn-edit"
                                               title="Düzenle">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button onclick="deleteProduct(<?php echo $product['id']; ?>)"
                                                    class="btn-action btn-delete"
                                                    title="Sil">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Sayfalama -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination-wrapper">
                        <div class="pagination-info">
                            Toplam <?php echo number_format($total_products); ?> ürün,
                            <?php echo $page; ?>/<?php echo $total_pages; ?> sayfa
                        </div>

                        <nav class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=1<?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo $category_filter ? '&category=' . $category_filter : ''; ?><?php echo !empty($status_filter) ? '&status=' . $status_filter : ''; ?>"
                                   class="page-link">İlk</a>
                                <a href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo $category_filter ? '&category=' . $category_filter : ''; ?><?php echo !empty($status_filter) ? '&status=' . $status_filter : ''; ?>"
                                   class="page-link">Önceki</a>
                            <?php endif; ?>

                            <?php
                            $start = max(1, $page - 2);
                            $end = min($total_pages, $page + 2);

                            for ($i = $start; $i <= $end; $i++):
                            ?>
                                <a href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo $category_filter ? '&category=' . $category_filter : ''; ?><?php echo !empty($status_filter) ? '&status=' . $status_filter : ''; ?>"
                                   class="page-link <?php echo $i == $page ? 'active' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo $category_filter ? '&category=' . $category_filter : ''; ?><?php echo !empty($status_filter) ? '&status=' . $status_filter : ''; ?>"
                                   class="page-link">Sonraki</a>
                                <a href="?page=<?php echo $total_pages; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo $category_filter ? '&category=' . $category_filter : ''; ?><?php echo !empty($status_filter) ? '&status=' . $status_filter : ''; ?>"
                                   class="page-link">Son</a>
                            <?php endif; ?>
                        </nav>
                    </div>
                <?php endif; ?>

            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-box-open"></i>
                    <h3>Ürün Bulunamadı</h3>
                    <p>
                        <?php if (!empty($search) || $category_filter || !empty($status_filter)): ?>
                            Arama kriterlerinize uygun ürün bulunamadı.
                        <?php else: ?>
                            Henüz hiç ürün eklenmemiş.
                        <?php endif; ?>
                    </p>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        İlk Ürünü Ekle
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* Admin Products Styles */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.content-actions {
    display: flex;
    gap: 15px;
}

.filters-card {
    background: var(--white);
    border-radius: 15px;
    padding: 25px;
    box-shadow: var(--shadow);
    margin-bottom: 25px;
}

.filters-form {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-input,
.filter-select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: var(--transition);
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.filter-actions {
    display: flex;
    gap: 10px;
}

.data-card {
    background: var(--white);
    border-radius: 15px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.card-header {
    padding: 25px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    gap: 10px;
}

.count-badge {
    background: var(--primary-color);
    color: var(--white);
    font-size: 0.8rem;
    padding: 4px 10px;
    border-radius: 12px;
    font-weight: 600;
}

.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.data-table th {
    background: var(--bg-light);
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.product-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder-image {
    width: 100%;
    height: 100%;
    background: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.product-info {
    max-width: 300px;
}

.product-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 5px;
}

.product-description {
    color: var(--text-light);
    font-size: 0.85rem;
    line-height: 1.4;
}

.category-badge {
    background: var(--primary-color);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.no-category {
    color: var(--text-light);
    font-style: italic;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.date-text {
    color: var(--text-light);
    font-size: 0.85rem;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.btn-action {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.btn-view {
    background: #17a2b8;
    color: var(--white);
}

.btn-view:hover {
    background: #138496;
}

.btn-edit {
    background: var(--primary-color);
    color: var(--white);
}

.btn-edit:hover {
    background: var(--accent-color);
}

.btn-delete {
    background: #dc3545;
    color: var(--white);
}

.btn-delete:hover {
    background: #c82333;
}

.pagination-wrapper {
    padding: 25px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination-info {
    color: var(--text-light);
    font-size: 0.9rem;
}

.pagination {
    display: flex;
    gap: 5px;
}

.page-link {
    padding: 8px 12px;
    border-radius: 6px;
    text-decoration: none;
    color: var(--text-dark);
    border: 1px solid #e0e0e0;
    transition: var(--transition);
}

.page-link:hover,
.page-link.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background: #6c757d;
}

/* Responsive */
@media (max-width: 768px) {
    .content-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }

    .filters-form {
        flex-direction: column;
    }

    .filter-group {
        min-width: auto;
    }

    .filter-actions {
        justify-content: center;
    }

    .pagination-wrapper {
        flex-direction: column;
        gap: 15px;
    }

    .data-table {
        font-size: 0.85rem;
    }

    .data-table th,
    .data-table td {
        padding: 10px 8px;
    }

    .product-info {
        max-width: 200px;
    }
}
</style>

<script>
function deleteProduct(productId) {
    if (confirm('Bu ürünü silmek istediğinizden emin misiniz?')) {
        // AJAX ile silme işlemi
        fetch('delete.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id=' + productId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Bir hata oluştu.');
        });
    }
}
</script>

<?php include '../includes/footer.php'; ?>