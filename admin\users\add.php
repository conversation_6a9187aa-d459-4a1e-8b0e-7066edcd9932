<?php
session_start();
require_once '../includes/header.php';

// Admin kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit();
}

$success = '';
$error = '';

// Form gönderildiğinde
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $full_name = trim($_POST['full_name'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $role = $_POST['role'] ?? 'user';
    $status = $_POST['status'] ?? 'active';
    
    // Validasyon
    if (empty($username)) {
        $error = 'Kullan<PERSON><PERSON>ı adı gereklidir.';
    } elseif (strlen($username) < 3) {
        $error = 'Kullanıcı adı en az 3 karakter olmalıdır.';
    } elseif (empty($password)) {
        $error = 'Şifre gereklidir.';
    } elseif (strlen($password) < 6) {
        $error = 'Şifre en az 6 karakter olmalıdır.';
    } elseif ($password !== $confirm_password) {
        $error = 'Şifreler eşleşmiyor.';
    } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Geçerli bir e-posta adresi giriniz.';
    } else {
        try {
            // Kullanıcı adı kontrolü
            $stmt = $pdo->prepare("SELECT id FROM admin_users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->fetch()) {
                $error = 'Bu kullanıcı adı zaten kullanılıyor.';
            } else {
                // E-posta kontrolü (eğer girilmişse)
                if (!empty($email)) {
                    $stmt = $pdo->prepare("SELECT id FROM admin_users WHERE email = ?");
                    $stmt->execute([$email]);
                    if ($stmt->fetch()) {
                        $error = 'Bu e-posta adresi zaten kullanılıyor.';
                    }
                }
                
                if (empty($error)) {
                    // Şifreyi hashle
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    
                    // Kullanıcıyı ekle
                    $stmt = $pdo->prepare("INSERT INTO admin_users (username, email, full_name, password, role, status, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
                    $stmt->execute([
                        $username,
                        !empty($email) ? $email : null,
                        !empty($full_name) ? $full_name : null,
                        $hashed_password,
                        $role,
                        $status
                    ]);
                    
                    $success = 'Kullanıcı başarıyla eklendi.';
                    
                    // Formu temizle
                    $username = $email = $full_name = '';
                    $role = 'user';
                    $status = 'active';
                }
            }
        } catch (PDOException $e) {
            $error = 'Kullanıcı eklenirken hata oluştu: ' . $e->getMessage();
        }
    }
}

$page_title = 'Yeni Kullanıcı Ekle';
$page_description = 'Yeni admin kullanıcısı ekleyin';
?>

<div class="admin-content">
    <div class="content-header">
        <div class="header-left">
            <h1><i class="fas fa-user-plus"></i> Yeni Kullanıcı Ekle</h1>
            <p>Yeni admin kullanıcısı oluşturun</p>
        </div>
        <div class="header-actions">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Geri Dön
            </a>
        </div>
    </div>

    <?php if ($success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success); ?>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <div class="content-body">
        <div class="form-container">
            <form method="POST" class="admin-form">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="username" class="form-label required">Kullanıcı Adı</label>
                        <input type="text" id="username" name="username" class="form-control" 
                               value="<?php echo htmlspecialchars($username ?? ''); ?>" 
                               required minlength="3" maxlength="50">
                        <small class="form-text">En az 3 karakter, sadece harf, rakam ve alt çizgi kullanın</small>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">E-posta</label>
                        <input type="email" id="email" name="email" class="form-control" 
                               value="<?php echo htmlspecialchars($email ?? ''); ?>" 
                               maxlength="100">
                        <small class="form-text">İsteğe bağlı</small>
                    </div>

                    <div class="form-group">
                        <label for="full_name" class="form-label">Ad Soyad</label>
                        <input type="text" id="full_name" name="full_name" class="form-control" 
                               value="<?php echo htmlspecialchars($full_name ?? ''); ?>" 
                               maxlength="100">
                        <small class="form-text">İsteğe bağlı</small>
                    </div>

                    <div class="form-group">
                        <label for="role" class="form-label required">Rol</label>
                        <select id="role" name="role" class="form-control" required>
                            <option value="user" <?php echo ($role ?? 'user') === 'user' ? 'selected' : ''; ?>>Kullanıcı</option>
                            <option value="admin" <?php echo ($role ?? '') === 'admin' ? 'selected' : ''; ?>>Admin</option>
                        </select>
                        <small class="form-text">Admin: Tüm yetkilere sahip, Kullanıcı: Sınırlı yetkiler</small>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label required">Şifre</label>
                        <input type="password" id="password" name="password" class="form-control" 
                               required minlength="6" maxlength="100">
                        <small class="form-text">En az 6 karakter</small>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password" class="form-label required">Şifre Tekrar</label>
                        <input type="password" id="confirm_password" name="confirm_password" class="form-control" 
                               required minlength="6" maxlength="100">
                        <small class="form-text">Şifreyi tekrar giriniz</small>
                    </div>

                    <div class="form-group">
                        <label for="status" class="form-label required">Durum</label>
                        <select id="status" name="status" class="form-control" required>
                            <option value="active" <?php echo ($status ?? 'active') === 'active' ? 'selected' : ''; ?>>Aktif</option>
                            <option value="inactive" <?php echo ($status ?? '') === 'inactive' ? 'selected' : ''; ?>>Pasif</option>
                        </select>
                        <small class="form-text">Pasif kullanıcılar giriş yapamaz</small>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Kullanıcı Ekle
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> İptal
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.form-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.form-label.required::after {
    content: ' *';
    color: #e74c3c;
}

.form-control {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #F9B233;
    box-shadow: 0 0 0 3px rgba(249, 178, 51, 0.1);
}

.form-text {
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<script>
// Şifre eşleşme kontrolü
document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function checkPasswordMatch() {
        if (password.value && confirmPassword.value) {
            if (password.value === confirmPassword.value) {
                confirmPassword.style.borderColor = '#28a745';
            } else {
                confirmPassword.style.borderColor = '#dc3545';
            }
        } else {
            confirmPassword.style.borderColor = '#e9ecef';
        }
    }
    
    password.addEventListener('input', checkPasswordMatch);
    confirmPassword.addEventListener('input', checkPasswordMatch);
});
</script>

<?php require_once '../includes/footer.php'; ?>
