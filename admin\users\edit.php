<?php
session_start();
require_once '../includes/header.php';

// Admin kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit();
}

$user_id = $_GET['id'] ?? 0;
$success = '';
$error = '';

// Kullanıcıyı getir
try {
    $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        header('Location: index.php');
        exit();
    }
} catch (PDOException $e) {
    $error = 'Kullanıcı bilgileri yüklenirken hata oluştu: ' . $e->getMessage();
}

// Form gönderildiğinde
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $user) {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $full_name = trim($_POST['full_name'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $role = $_POST['role'] ?? 'user';
    $status = $_POST['status'] ?? 'active';
    
    // Validasyon
    if (empty($username)) {
        $error = 'Kullanıcı adı gereklidir.';
    } elseif (strlen($username) < 3) {
        $error = 'Kullanıcı adı en az 3 karakter olmalıdır.';
    } elseif (!empty($password) && strlen($password) < 6) {
        $error = 'Şifre en az 6 karakter olmalıdır.';
    } elseif (!empty($password) && $password !== $confirm_password) {
        $error = 'Şifreler eşleşmiyor.';
    } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Geçerli bir e-posta adresi giriniz.';
    } else {
        try {
            // Kullanıcı adı kontrolü (kendisi hariç)
            $stmt = $pdo->prepare("SELECT id FROM admin_users WHERE username = ? AND id != ?");
            $stmt->execute([$username, $user_id]);
            if ($stmt->fetch()) {
                $error = 'Bu kullanıcı adı zaten kullanılıyor.';
            } else {
                // E-posta kontrolü (eğer girilmişse ve kendisi hariç)
                if (!empty($email)) {
                    $stmt = $pdo->prepare("SELECT id FROM admin_users WHERE email = ? AND id != ?");
                    $stmt->execute([$email, $user_id]);
                    if ($stmt->fetch()) {
                        $error = 'Bu e-posta adresi zaten kullanılıyor.';
                    }
                }
                
                if (empty($error)) {
                    // Güncelleme sorgusu
                    if (!empty($password)) {
                        // Şifre ile güncelle
                        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("UPDATE admin_users SET username = ?, email = ?, full_name = ?, password = ?, role = ?, status = ?, updated_at = NOW() WHERE id = ?");
                        $stmt->execute([
                            $username,
                            !empty($email) ? $email : null,
                            !empty($full_name) ? $full_name : null,
                            $hashed_password,
                            $role,
                            $status,
                            $user_id
                        ]);
                    } else {
                        // Şifre olmadan güncelle
                        $stmt = $pdo->prepare("UPDATE admin_users SET username = ?, email = ?, full_name = ?, role = ?, status = ?, updated_at = NOW() WHERE id = ?");
                        $stmt->execute([
                            $username,
                            !empty($email) ? $email : null,
                            !empty($full_name) ? $full_name : null,
                            $role,
                            $status,
                            $user_id
                        ]);
                    }
                    
                    $success = 'Kullanıcı başarıyla güncellendi.';
                    
                    // Güncel bilgileri getir
                    $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE id = ?");
                    $stmt->execute([$user_id]);
                    $user = $stmt->fetch(PDO::FETCH_ASSOC);
                }
            }
        } catch (PDOException $e) {
            $error = 'Kullanıcı güncellenirken hata oluştu: ' . $e->getMessage();
        }
    }
}

$page_title = 'Kullanıcı Düzenle';
$page_description = 'Admin kullanıcısını düzenleyin';
?>

<div class="admin-content">
    <div class="content-header">
        <div class="header-left">
            <h1><i class="fas fa-user-edit"></i> Kullanıcı Düzenle</h1>
            <p>Admin kullanıcısını düzenleyin</p>
        </div>
        <div class="header-actions">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Geri Dön
            </a>
        </div>
    </div>

    <?php if ($success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success); ?>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <?php if ($user): ?>
    <div class="content-body">
        <div class="form-container">
            <form method="POST" class="admin-form">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="username" class="form-label required">Kullanıcı Adı</label>
                        <input type="text" id="username" name="username" class="form-control" 
                               value="<?php echo htmlspecialchars($user['username']); ?>" 
                               required minlength="3" maxlength="50">
                        <small class="form-text">En az 3 karakter, sadece harf, rakam ve alt çizgi kullanın</small>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">E-posta</label>
                        <input type="email" id="email" name="email" class="form-control" 
                               value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" 
                               maxlength="100">
                        <small class="form-text">İsteğe bağlı</small>
                    </div>

                    <div class="form-group">
                        <label for="full_name" class="form-label">Ad Soyad</label>
                        <input type="text" id="full_name" name="full_name" class="form-control" 
                               value="<?php echo htmlspecialchars($user['full_name'] ?? ''); ?>" 
                               maxlength="100">
                        <small class="form-text">İsteğe bağlı</small>
                    </div>

                    <div class="form-group">
                        <label for="role" class="form-label required">Rol</label>
                        <select id="role" name="role" class="form-control" required>
                            <option value="user" <?php echo $user['role'] === 'user' ? 'selected' : ''; ?>>Kullanıcı</option>
                            <option value="admin" <?php echo $user['role'] === 'admin' ? 'selected' : ''; ?>>Admin</option>
                        </select>
                        <small class="form-text">Admin: Tüm yetkilere sahip, Kullanıcı: Sınırlı yetkiler</small>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Yeni Şifre</label>
                        <input type="password" id="password" name="password" class="form-control" 
                               minlength="6" maxlength="100">
                        <small class="form-text">Boş bırakırsanız şifre değişmez. En az 6 karakter</small>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password" class="form-label">Yeni Şifre Tekrar</label>
                        <input type="password" id="confirm_password" name="confirm_password" class="form-control" 
                               minlength="6" maxlength="100">
                        <small class="form-text">Yeni şifreyi tekrar giriniz</small>
                    </div>

                    <div class="form-group">
                        <label for="status" class="form-label required">Durum</label>
                        <select id="status" name="status" class="form-control" required>
                            <option value="active" <?php echo $user['status'] === 'active' ? 'selected' : ''; ?>>Aktif</option>
                            <option value="inactive" <?php echo $user['status'] === 'inactive' ? 'selected' : ''; ?>>Pasif</option>
                        </select>
                        <small class="form-text">Pasif kullanıcılar giriş yapamaz</small>
                    </div>
                </div>

                <div class="user-info-section">
                    <h3>Kullanıcı Bilgileri</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Kayıt Tarihi:</label>
                            <span><?php echo date('d.m.Y H:i', strtotime($user['created_at'])); ?></span>
                        </div>
                        <div class="info-item">
                            <label>Son Güncelleme:</label>
                            <span><?php echo $user['updated_at'] ? date('d.m.Y H:i', strtotime($user['updated_at'])) : 'Hiç güncellenmemiş'; ?></span>
                        </div>
                        <div class="info-item">
                            <label>Son Giriş:</label>
                            <span><?php echo $user['last_login'] ? date('d.m.Y H:i', strtotime($user['last_login'])) : 'Hiç giriş yapmamış'; ?></span>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Güncelle
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> İptal
                    </a>
                </div>
            </form>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
.form-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.user-info-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.user-info-section h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-item label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.info-item span {
    color: #2c3e50;
    font-weight: 500;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.form-label.required::after {
    content: ' *';
    color: #e74c3c;
}

.form-control {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #F9B233;
    box-shadow: 0 0 0 3px rgba(249, 178, 51, 0.1);
}

.form-text {
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<script>
// Şifre eşleşme kontrolü
document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function checkPasswordMatch() {
        if (password.value && confirmPassword.value) {
            if (password.value === confirmPassword.value) {
                confirmPassword.style.borderColor = '#28a745';
            } else {
                confirmPassword.style.borderColor = '#dc3545';
            }
        } else {
            confirmPassword.style.borderColor = '#e9ecef';
        }
    }
    
    password.addEventListener('input', checkPasswordMatch);
    confirmPassword.addEventListener('input', checkPasswordMatch);
});
</script>

<?php require_once '../includes/footer.php'; ?>
