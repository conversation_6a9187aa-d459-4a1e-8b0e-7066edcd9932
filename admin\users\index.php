<?php
session_start();
require_once '../includes/header.php';

// Admin kontrolü
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit();
}

// Kullanıcıları getir
try {
    $stmt = $pdo->prepare("SELECT id, username, email, full_name, role, status, created_at, last_login FROM admin_users ORDER BY created_at DESC");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Kullanıcılar yüklenirken hata oluştu: " . $e->getMessage();
    $users = [];
}

// Sayfa bilgileri
$page_title = 'Kullanıcı Yönetimi';
$page_description = 'Ad<PERSON> kullanı<PERSON>ılarını yönetin';
?>

<div class="admin-content">
    <div class="content-header">
        <div class="header-left">
            <h1><i class="fas fa-users"></i> Kullanıcı Yönetimi</h1>
            <p>Admin kullanıcılarını görüntüleyin ve yönetin</p>
        </div>
        <div class="header-actions">
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> Yeni Kullanıcı Ekle
            </a>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <div class="content-body">
        <div class="data-table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Kullanıcı Adı</th>
                        <th>Ad Soyad</th>
                        <th>E-posta</th>
                        <th>Rol</th>
                        <th>Durum</th>
                        <th>Kayıt Tarihi</th>
                        <th>Son Giriş</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($users)): ?>
                        <tr>
                            <td colspan="9" class="text-center">
                                <div class="empty-state">
                                    <i class="fas fa-users"></i>
                                    <h3>Kullanıcı Bulunamadı</h3>
                                    <p>Henüz hiç kullanıcı eklenmemiş.</p>
                                    <a href="add.php" class="btn btn-primary">İlk Kullanıcıyı Ekle</a>
                                </div>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($user['id']); ?></td>
                                <td>
                                    <div class="user-info">
                                        <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($user['full_name'] ?? '-'); ?></td>
                                <td><?php echo htmlspecialchars($user['email'] ?? '-'); ?></td>
                                <td>
                                    <span class="badge badge-<?php echo $user['role'] === 'admin' ? 'danger' : 'primary'; ?>">
                                        <?php echo ucfirst(htmlspecialchars($user['role'])); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-<?php echo $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                        <?php echo $user['status'] === 'active' ? 'Aktif' : 'Pasif'; ?>
                                    </span>
                                </td>
                                <td><?php echo date('d.m.Y H:i', strtotime($user['created_at'])); ?></td>
                                <td>
                                    <?php if ($user['last_login']): ?>
                                        <?php echo date('d.m.Y H:i', strtotime($user['last_login'])); ?>
                                    <?php else: ?>
                                        <span class="text-muted">Hiç giriş yapmamış</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="edit.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-primary" title="Düzenle">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if ($user['id'] != $_SESSION['admin_id']): ?>
                                            <a href="delete.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-danger" 
                                               title="Sil" onclick="return confirm('Bu kullanıcıyı silmek istediğinizden emin misiniz?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
.user-info {
    display: flex;
    flex-direction: column;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.badge-primary { background: #007bff; color: white; }
.badge-success { background: #28a745; color: white; }
.badge-danger { background: #dc3545; color: white; }
.badge-secondary { background: #6c757d; color: white; }

.action-buttons {
    display: flex;
    gap: 5px;
}

.empty-state {
    padding: 40px;
    text-align: center;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #495057;
}

.text-muted {
    color: #6c757d;
}
</style>

<?php require_once '../includes/footer.php'; ?>
