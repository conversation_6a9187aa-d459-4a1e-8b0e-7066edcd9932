/* GNG Makine - Animations CSS */

/* Base animation classes */
.animated {
    animation-duration: 0.8s;
    animation-fill-mode: both;
}

.animate-fast {
    animation-duration: 0.4s;
}

.animate-slow {
    animation-duration: 1.2s;
}

/* Fade animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scale animations */
@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes zoomOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* Slide animations */
@keyframes slideInUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Bounce animations */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translateY(0);
    }
    40%, 43% {
        transform: translateY(-20px);
    }
    70% {
        transform: translateY(-10px);
    }
    90% {
        transform: translateY(-4px);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Shake animation */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* Pulse animation */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Rotate animations */
@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-180deg);
    }
    to {
        opacity: 1;
        transform: rotate(0deg);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Flip animations */
@keyframes flipInX {
    from {
        opacity: 0;
        transform: perspective(400px) rotateX(90deg);
    }
    40% {
        transform: perspective(400px) rotateX(-20deg);
    }
    60% {
        transform: perspective(400px) rotateX(10deg);
    }
    80% {
        transform: perspective(400px) rotateX(-5deg);
    }
    to {
        opacity: 1;
        transform: perspective(400px) rotateX(0deg);
    }
}

@keyframes flipInY {
    from {
        opacity: 0;
        transform: perspective(400px) rotateY(90deg);
    }
    40% {
        transform: perspective(400px) rotateY(-20deg);
    }
    60% {
        transform: perspective(400px) rotateY(10deg);
    }
    80% {
        transform: perspective(400px) rotateY(-5deg);
    }
    to {
        opacity: 1;
        transform: perspective(400px) rotateY(0deg);
    }
}

/* Animation classes */
.animate-fadeIn { animation-name: fadeIn; }
.animate-fadeInUp { animation-name: fadeInUp; }
.animate-fadeInDown { animation-name: fadeInDown; }
.animate-fadeInLeft { animation-name: fadeInLeft; }
.animate-fadeInRight { animation-name: fadeInRight; }
.animate-zoomIn { animation-name: zoomIn; }
.animate-zoomOut { animation-name: zoomOut; }
.animate-slideInUp { animation-name: slideInUp; }
.animate-slideInDown { animation-name: slideInDown; }
.animate-slideInLeft { animation-name: slideInLeft; }
.animate-slideInRight { animation-name: slideInRight; }
.animate-bounce { animation-name: bounce; }
.animate-bounceIn { animation-name: bounceIn; }
.animate-shake { animation-name: shake; }
.animate-pulse { animation-name: pulse; }
.animate-rotateIn { animation-name: rotateIn; }
.animate-spin { animation-name: spin; }
.animate-flipInX { animation-name: flipInX; }
.animate-flipInY { animation-name: flipInY; }

/* Loading animations */
@keyframes loading-dots {
    0%, 20% {
        color: transparent;
        text-shadow: 0.25em 0 0 transparent, 0.5em 0 0 transparent;
    }
    40% {
        color: var(--primary-color);
        text-shadow: 0.25em 0 0 transparent, 0.5em 0 0 transparent;
    }
    60% {
        text-shadow: 0.25em 0 0 var(--primary-color), 0.5em 0 0 transparent;
    }
    80%, 100% {
        text-shadow: 0.25em 0 0 var(--primary-color), 0.5em 0 0 var(--primary-color);
    }
}

.loading-dots::after {
    content: '...';
    animation: loading-dots 1.5s infinite;
}

/* Progress bar animation */
@keyframes progress-fill {
    from {
        width: 0%;
    }
}

.progress-fill {
    animation: progress-fill 2s ease-out;
}

/* Hover effects */
.hover-lift {
    transition: transform 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Floating animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.floating {
    animation: float 3s ease-in-out infinite;
}

/* Typing effect */
@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--primary-color);
    }
}

.typing-effect {
    overflow: hidden;
    border-right: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

/* Stagger animation delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }

/* Infinite animations */
.infinite {
    animation-iteration-count: infinite;
}

/* Animation utilities */
.no-animation {
    animation: none !important;
}

.animation-paused {
    animation-play-state: paused;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .floating,
    .infinite {
        animation: none !important;
    }
}
