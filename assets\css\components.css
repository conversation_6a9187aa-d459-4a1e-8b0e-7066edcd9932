/* GNG Makine - Components CSS */

/* Modal Components */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--white);
    border-radius: 20px;
    padding: 40px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--primary-color);
}

.modal-header {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.modal-body {
    margin-bottom: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

/* Tabs Component */
.tabs {
    width: 100%;
}

.tab-nav {
    display: flex;
    border-bottom: 2px solid #e0e0e0;
    margin-bottom: 30px;
}

.tab-button {
    background: none;
    border: none;
    padding: 15px 25px;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-light);
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-button:hover,
.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    min-height: 200px;
}

.tab-pane {
    display: none;
    animation: fadeInUp 0.5s ease;
}

.tab-pane.active {
    display: block;
}

/* Accordion Component */
.accordion {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.accordion-item {
    border-bottom: 1px solid #e0e0e0;
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-header {
    background: var(--bg-light);
    padding: 20px 25px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s ease;
}

.accordion-header:hover {
    background: #f0f0f0;
}

.accordion-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.accordion-icon {
    font-size: 1.2rem;
    color: var(--primary-color);
    transition: transform 0.3s ease;
}

.accordion-item.active .accordion-icon {
    transform: rotate(180deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.accordion-body {
    padding: 25px;
    background: var(--white);
    color: var(--text-light);
    line-height: 1.6;
}

/* Carousel Component */
.carousel {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
}

.carousel-inner {
    display: flex;
    transition: transform 0.5s ease;
}

.carousel-slide {
    min-width: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.carousel-slide.active {
    opacity: 1;
}

.carousel-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.5);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.carousel-controls:hover {
    background: var(--primary-color);
}

.carousel-prev {
    left: 20px;
}

.carousel-next {
    right: 20px;
}

.carousel-indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.carousel-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.carousel-indicator.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

/* Tooltip Component */
.tooltip {
    position: absolute;
    background: var(--text-dark);
    color: var(--white);
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.9rem;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    animation: fadeIn 0.3s ease forwards;
}

.tooltip::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 5px solid transparent;
}

.tooltip-top::after {
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    border-top-color: var(--text-dark);
}

.tooltip-bottom::after {
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: var(--text-dark);
}

.tooltip-left::after {
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    border-left-color: var(--text-dark);
}

.tooltip-right::after {
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    border-right-color: var(--text-dark);
}

/* Dropdown Component */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-trigger {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 12px 20px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.dropdown-trigger:hover {
    background: var(--accent-color);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white);
    border-radius: 10px;
    box-shadow: var(--shadow);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 12px 20px;
    color: var(--text-dark);
    text-decoration: none;
    transition: background 0.3s ease;
}

.dropdown-item:hover {
    background: var(--bg-light);
    color: var(--primary-color);
}

/* Progress Bar Component */
.progress-bar {
    background: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    height: 10px;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 10px;
    width: 0%;
    transition: width 2s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progress-shine 2s ease-in-out infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Counter Component */
.counter {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

/* Notification Component */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--white);
    border-radius: 10px;
    box-shadow: var(--shadow);
    padding: 20px;
    max-width: 400px;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.notification-message {
    color: var(--text-dark);
    font-size: 1rem;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-light);
}

.notification-success {
    border-left: 4px solid #27ae60;
}

.notification-error {
    border-left: 4px solid #e74c3c;
}

.notification-warning {
    border-left: 4px solid #f39c12;
}

.notification-info {
    border-left: 4px solid #3498db;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-color);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modal-content {
        padding: 20px;
        margin: 20px;
    }

    .tab-nav {
        flex-wrap: wrap;
    }

    .tab-button {
        flex: 1;
        min-width: 120px;
    }

    .carousel-controls {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}
