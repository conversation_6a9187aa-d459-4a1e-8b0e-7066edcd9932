/* GNG Makine - Responsive Design CSS */

/* Mobile First Approach */

/* Extra Small devices (phones, 576px and down) */
@media (max-width: 575.98px) {
    .container {
        padding: 0 15px;
    }
    
    /* Typography */
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    h4 { font-size: 1.25rem; }
    
    /* Navbar */
    .main-navbar {
        padding: 10px 0;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .navbar-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    /* Hero sections */
    .hero-section {
        padding: 60px 0 40px;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2rem;
        margin-bottom: 15px;
    }
    
    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 25px;
    }
    
    /* Buttons */
    .btn {
        padding: 12px 20px;
        font-size: 0.9rem;
    }
    
    .btn-group {
        flex-direction: column;
        gap: 10px;
    }
    
    /* Grid layouts */
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    /* Cards */
    .card {
        margin-bottom: 20px;
    }
    
    .card-body {
        padding: 20px;
    }
    
    /* Forms */
    .form-row {
        flex-direction: column;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    /* Statistics */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .stat-item {
        padding: 20px 15px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    /* Footer */
    .footer-grid {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }
    
    .footer-column {
        margin-bottom: 30px;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .container {
        max-width: 540px;
    }
    
    /* Grid layouts */
    .grid-2 {
        grid-template-columns: 1fr;
    }
    
    .grid-3,
    .grid-4 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    /* Statistics */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    /* Products grid */
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    /* Services grid */
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .container {
        max-width: 720px;
    }
    
    /* Navbar */
    .mobile-menu-toggle {
        display: none;
    }
    
    .navbar-menu {
        display: flex;
    }
    
    /* Grid layouts */
    .grid-2 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .grid-3 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .grid-4 {
        grid-template-columns: repeat(3, 1fr);
    }
    
    /* Hero sections */
    .hero-section {
        padding: 80px 0 60px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    /* Statistics */
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    /* Products */
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .products-layout {
        grid-template-columns: 250px 1fr;
        gap: 30px;
    }
    
    /* Footer */
    .footer-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 40px;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .container {
        max-width: 960px;
    }
    
    /* Grid layouts */
    .grid-3 {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .grid-4 {
        grid-template-columns: repeat(3, 1fr);
    }
    
    /* Hero sections */
    .hero-section {
        padding: 100px 0 80px;
    }
    
    /* Statistics */
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    /* Products */
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    /* Footer */
    .footer-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
    
    /* Grid layouts */
    .grid-4 {
        grid-template-columns: repeat(4, 1fr);
    }
    
    /* Hero sections */
    .hero-section {
        padding: 120px 0 100px;
    }
    
    .hero-title {
        font-size: 3.5rem;
    }
    
    /* Products */
    .products-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    /* Footer */
    .footer-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Mobile Menu Styles */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 280px;
    height: 100vh;
    background: var(--white);
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 9999;
    padding: 80px 0 20px;
    overflow-y: auto;
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu-open .mobile-menu-overlay {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-dark);
    cursor: pointer;
    padding: 5px;
}

.mobile-menu-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-menu-item {
    border-bottom: 1px solid #f0f0f0;
}

.mobile-menu-link {
    display: block;
    padding: 15px 25px;
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.mobile-menu-link:hover,
.mobile-menu-link.active {
    background: var(--bg-light);
    color: var(--primary-color);
    padding-left: 35px;
}

/* Responsive Images */
.responsive-img {
    max-width: 100%;
    height: auto;
}

/* Responsive Videos */
.responsive-video {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
}

.responsive-video iframe,
.responsive-video video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Hide/Show elements based on screen size */
.hide-mobile {
    display: block;
}

.show-mobile {
    display: none;
}

@media (max-width: 767.98px) {
    .hide-mobile {
        display: none;
    }
    
    .show-mobile {
        display: block;
    }
}

.hide-tablet {
    display: block;
}

.show-tablet {
    display: none;
}

@media (min-width: 768px) and (max-width: 991.98px) {
    .hide-tablet {
        display: none;
    }
    
    .show-tablet {
        display: block;
    }
}

.hide-desktop {
    display: block;
}

.show-desktop {
    display: none;
}

@media (min-width: 992px) {
    .hide-desktop {
        display: none;
    }
    
    .show-desktop {
        display: block;
    }
}

/* Responsive Text Alignment */
@media (max-width: 767.98px) {
    .text-center-mobile {
        text-align: center;
    }
    
    .text-left-mobile {
        text-align: left;
    }
    
    .text-right-mobile {
        text-align: right;
    }
}

/* Responsive Spacing */
@media (max-width: 767.98px) {
    .mb-mobile-0 { margin-bottom: 0; }
    .mb-mobile-1 { margin-bottom: 10px; }
    .mb-mobile-2 { margin-bottom: 20px; }
    .mb-mobile-3 { margin-bottom: 30px; }
    
    .mt-mobile-0 { margin-top: 0; }
    .mt-mobile-1 { margin-top: 10px; }
    .mt-mobile-2 { margin-top: 20px; }
    .mt-mobile-3 { margin-top: 30px; }
    
    .p-mobile-1 { padding: 10px; }
    .p-mobile-2 { padding: 20px; }
    .p-mobile-3 { padding: 30px; }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .main-navbar,
    .mobile-menu,
    .back-to-top,
    .modal {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
    
    a {
        text-decoration: underline;
        color: #000;
    }
    
    .btn {
        border: 1px solid #000;
        background: transparent;
        color: #000;
    }
}
