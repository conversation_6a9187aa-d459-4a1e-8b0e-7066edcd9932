/**
 * GNG Makine - Animations JavaScript
 * Sayfa animasyonları ve görsel efektler
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize animations
    initScrollAnimations();
    initParallaxEffects();
    initHoverAnimations();
    initLoadingAnimations();
    initTypingEffect();
    
});

/**
 * Scroll-based animations
 */
function initScrollAnimations() {
    // Elements to animate on scroll
    const animatedElements = document.querySelectorAll('[data-animate]');
    
    if (!animatedElements.length) return;
    
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const animationType = element.getAttribute('data-animate');
                const delay = element.getAttribute('data-delay') || 0;
                
                setTimeout(() => {
                    element.classList.add('animate-' + animationType);
                    element.classList.add('animated');
                }, delay);
                
                observer.unobserve(element);
            }
        });
    }, observerOptions);
    
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

/**
 * Parallax effects
 */
function initParallaxEffects() {
    const parallaxElements = document.querySelectorAll('[data-parallax]');
    
    if (!parallaxElements.length) return;
    
    window.addEventListener('scroll', throttle(function() {
        const scrollTop = window.pageYOffset;
        
        parallaxElements.forEach(element => {
            const speed = parseFloat(element.getAttribute('data-parallax')) || 0.5;
            const yPos = -(scrollTop * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    }, 16));
}

/**
 * Hover animations
 */
function initHoverAnimations() {
    // Card hover effects
    const cards = document.querySelectorAll('.product-card, .service-card, .category-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Button hover effects
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Image hover effects
    const hoverImages = document.querySelectorAll('.hover-zoom img');
    
    hoverImages.forEach(img => {
        const container = img.parentElement;
        
        container.addEventListener('mouseenter', function() {
            img.style.transform = 'scale(1.1)';
        });
        
        container.addEventListener('mouseleave', function() {
            img.style.transform = 'scale(1)';
        });
    });
}

/**
 * Loading animations
 */
function initLoadingAnimations() {
    // Page load animation
    const pageLoader = document.querySelector('.page-loader');
    if (pageLoader) {
        window.addEventListener('load', function() {
            pageLoader.classList.add('fade-out');
            setTimeout(() => {
                pageLoader.style.display = 'none';
            }, 500);
        });
    }
    
    // Content fade-in
    const fadeElements = document.querySelectorAll('.fade-in');
    fadeElements.forEach((element, index) => {
        setTimeout(() => {
            element.classList.add('visible');
        }, index * 100);
    });
}

/**
 * Typing effect
 */
function initTypingEffect() {
    const typingElements = document.querySelectorAll('[data-typing]');
    
    typingElements.forEach(element => {
        const text = element.getAttribute('data-typing');
        const speed = parseInt(element.getAttribute('data-typing-speed')) || 100;
        
        element.textContent = '';
        typeText(element, text, speed);
    });
}

function typeText(element, text, speed) {
    let i = 0;
    const timer = setInterval(() => {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
        } else {
            clearInterval(timer);
        }
    }, speed);
}

/**
 * Floating elements animation
 */
function initFloatingElements() {
    const floatingElements = document.querySelectorAll('.floating-element');
    
    floatingElements.forEach((element, index) => {
        const duration = 3000 + (index * 500); // Different duration for each element
        const delay = index * 200; // Staggered start
        
        setTimeout(() => {
            animateFloat(element, duration);
        }, delay);
    });
}

function animateFloat(element, duration) {
    const startY = 0;
    const endY = -20;
    let startTime = null;
    
    function animate(currentTime) {
        if (startTime === null) startTime = currentTime;
        const elapsed = currentTime - startTime;
        const progress = (elapsed % duration) / duration;
        
        // Use sine wave for smooth up-down motion
        const y = startY + (endY - startY) * Math.sin(progress * Math.PI * 2);
        element.style.transform = `translateY(${y}px)`;
        
        requestAnimationFrame(animate);
    }
    
    requestAnimationFrame(animate);
}

/**
 * Stagger animation for lists
 */
function staggerAnimation(elements, animationClass, delay = 100) {
    elements.forEach((element, index) => {
        setTimeout(() => {
            element.classList.add(animationClass);
        }, index * delay);
    });
}

/**
 * Pulse animation
 */
function pulseElement(element, duration = 1000) {
    element.style.animation = `pulse ${duration}ms ease-in-out`;
    
    setTimeout(() => {
        element.style.animation = '';
    }, duration);
}

/**
 * Shake animation
 */
function shakeElement(element, duration = 500) {
    element.style.animation = `shake ${duration}ms ease-in-out`;
    
    setTimeout(() => {
        element.style.animation = '';
    }, duration);
}

/**
 * Bounce animation
 */
function bounceElement(element, duration = 600) {
    element.style.animation = `bounce ${duration}ms ease-in-out`;
    
    setTimeout(() => {
        element.style.animation = '';
    }, duration);
}

/**
 * Fade animations
 */
function fadeIn(element, duration = 300) {
    element.style.opacity = '0';
    element.style.display = 'block';
    
    let opacity = 0;
    const increment = 1 / (duration / 16);
    
    const timer = setInterval(() => {
        opacity += increment;
        if (opacity >= 1) {
            opacity = 1;
            clearInterval(timer);
        }
        element.style.opacity = opacity;
    }, 16);
}

function fadeOut(element, duration = 300) {
    let opacity = 1;
    const decrement = 1 / (duration / 16);
    
    const timer = setInterval(() => {
        opacity -= decrement;
        if (opacity <= 0) {
            opacity = 0;
            element.style.display = 'none';
            clearInterval(timer);
        }
        element.style.opacity = opacity;
    }, 16);
}

/**
 * Slide animations
 */
function slideDown(element, duration = 300) {
    element.style.height = '0';
    element.style.overflow = 'hidden';
    element.style.display = 'block';
    
    const targetHeight = element.scrollHeight;
    let height = 0;
    const increment = targetHeight / (duration / 16);
    
    const timer = setInterval(() => {
        height += increment;
        if (height >= targetHeight) {
            height = targetHeight;
            element.style.height = 'auto';
            element.style.overflow = 'visible';
            clearInterval(timer);
        } else {
            element.style.height = height + 'px';
        }
    }, 16);
}

function slideUp(element, duration = 300) {
    const startHeight = element.offsetHeight;
    let height = startHeight;
    const decrement = startHeight / (duration / 16);
    
    element.style.overflow = 'hidden';
    
    const timer = setInterval(() => {
        height -= decrement;
        if (height <= 0) {
            height = 0;
            element.style.display = 'none';
            clearInterval(timer);
        }
        element.style.height = height + 'px';
    }, 16);
}

/**
 * Utility function: Throttle (reused from main.js)
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Export animation functions
window.GNGAnimations = {
    staggerAnimation,
    pulseElement,
    shakeElement,
    bounceElement,
    fadeIn,
    fadeOut,
    slideDown,
    slideUp,
    typeText
};
