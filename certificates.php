<?php
require_once 'config/config.php';

// Sayfa bilgileri
$page_title = 'Sertifikalar - GNG Makine';
$page_description = 'GNG Makine kalite sertifikaları, belgeler ve akreditasyonlar';

// Sertifikaları getir (örnek veri - gerçek uygulamada veritabanından gelecek)
$certificates = [
    [
        'id' => 1,
        'title' => 'ISO 9001:2015 Kalite Yönetim Sistemi',
        'description' => 'Kalite yönetim sistemimizin uluslararası standartlara uygunluğunu belgeleyen sertifika.',
        'issuer' => 'TÜV SÜD',
        'issue_date' => '2023-01-15',
        'expiry_date' => '2026-01-15',
        'certificate_number' => 'ISO-9001-2023-001',
        'image' => 'iso-9001.jpg',
        'category' => 'Kalite Yönetimi',
        'status' => 'active'
    ],
    [
        'id' => 2,
        'title' => 'ISO 14001:2015 Çevre Yönetim Sistemi',
        'description' => 'Çevresel sorumluluklarımızı yerine getirdiğimizi gösteren çevre yönetim sistemi sertifikası.',
        'issuer' => 'Bureau Veritas',
        'issue_date' => '2023-03-20',
        'expiry_date' => '2026-03-20',
        'certificate_number' => 'ISO-14001-2023-002',
        'image' => 'iso-14001.jpg',
        'category' => 'Çevre Yönetimi',
        'status' => 'active'
    ],
    [
        'id' => 3,
        'title' => 'OHSAS 18001 İş Sağlığı ve Güvenliği',
        'description' => 'İş sağlığı ve güvenliği yönetim sistemimizin standartlara uygunluğunu belgeleyen sertifika.',
        'issuer' => 'SGS',
        'issue_date' => '2023-02-10',
        'expiry_date' => '2026-02-10',
        'certificate_number' => 'OHSAS-18001-2023-003',
        'image' => 'ohsas-18001.jpg',
        'category' => 'İş Güvenliği',
        'status' => 'active'
    ],
    [
        'id' => 4,
        'title' => 'CE Uygunluk Beyanı',
        'description' => 'Ürünlerimizin Avrupa Birliği standartlarına uygunluğunu belgeleyen CE işareti.',
        'issuer' => 'Notified Body 1234',
        'issue_date' => '2023-04-05',
        'expiry_date' => '2028-04-05',
        'certificate_number' => 'CE-2023-004',
        'image' => 'ce-certificate.jpg',
        'category' => 'Ürün Uygunluğu',
        'status' => 'active'
    ],
    [
        'id' => 5,
        'title' => 'TSE Hizmet Yeterlilik Belgesi',
        'description' => 'Türk Standartları Enstitüsü tarafından verilen hizmet yeterlilik belgesi.',
        'issuer' => 'TSE',
        'issue_date' => '2023-05-12',
        'expiry_date' => '2025-05-12',
        'certificate_number' => 'TSE-HYB-2023-005',
        'image' => 'tse-certificate.jpg',
        'category' => 'Hizmet Yeterliliği',
        'status' => 'active'
    ],
    [
        'id' => 6,
        'title' => 'Kaizen Sürekli İyileştirme Sertifikası',
        'description' => 'Sürekli iyileştirme felsefesini benimseyen organizasyonlara verilen sertifika.',
        'issuer' => 'Kaizen Institute',
        'issue_date' => '2023-06-18',
        'expiry_date' => '2025-06-18',
        'certificate_number' => 'KAIZEN-2023-006',
        'image' => 'kaizen-certificate.jpg',
        'category' => 'Sürekli İyileştirme',
        'status' => 'active'
    ]
];

// Kategorileri çıkar
$categories = array_unique(array_column($certificates, 'category'));
sort($categories);
?>

<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            --primary: #F9B233;
            --secondary: #8A8C8F;
            --dark: #2C3E50;
            --light: #ECF0F1;
            --white: #FFFFFF;
            --text-dark: #2C3E50;
            --text-light: #7F8C8D;
            --shadow: 0 10px 30px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
            --border-radius: 15px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: var(--white);
            padding: 100px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Main Content */
        .main-content {
            padding: 80px 0;
        }

        .section-title {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 15px;
        }

        .section-title p {
            font-size: 1.1rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Filter Tabs */
        .filter-tabs {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 50px;
        }

        .filter-tab {
            padding: 12px 25px;
            background: var(--white);
            border: 2px solid #e9ecef;
            border-radius: 25px;
            color: var(--text-dark);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
            cursor: pointer;
        }

        .filter-tab:hover,
        .filter-tab.active {
            background: var(--primary);
            color: var(--white);
            border-color: var(--primary);
            transform: translateY(-2px);
        }

        /* Certificates Grid */
        .certificates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }

        .certificate-card {
            background: var(--white);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            position: relative;
        }

        .certificate-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .certificate-image {
            height: 200px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .certificate-image i {
            font-size: 4rem;
            color: var(--white);
            opacity: 0.8;
        }

        .certificate-status {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #28a745;
            color: var(--white);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .certificate-content {
            padding: 25px;
        }

        .certificate-category {
            color: var(--primary);
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .certificate-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .certificate-description {
            color: var(--text-light);
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .certificate-details {
            border-top: 1px solid #eee;
            padding-top: 20px;
        }

        .certificate-detail {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .certificate-detail:last-child {
            margin-bottom: 0;
        }

        .certificate-detail-label {
            color: var(--text-light);
            font-weight: 600;
        }

        .certificate-detail-value {
            color: var(--text-dark);
            font-weight: 500;
        }

        .certificate-number {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 8px;
            margin-top: 15px;
            text-align: center;
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: var(--text-dark);
            border: 1px solid #e9ecef;
        }

        /* Stats Section */
        .stats-section {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 40px;
            margin-bottom: 60px;
            box-shadow: var(--shadow);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            text-align: center;
        }

        .stat-item {
            padding: 20px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1rem;
            color: var(--text-dark);
            font-weight: 600;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1.1rem;
            }

            .certificates-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .filter-tabs {
                justify-content: flex-start;
                overflow-x: auto;
                padding-bottom: 10px;
            }

            .section-title h2 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .certificate-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .certificate-card:nth-child(2) { animation-delay: 0.1s; }
        .certificate-card:nth-child(3) { animation-delay: 0.2s; }
        .certificate-card:nth-child(4) { animation-delay: 0.3s; }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1><i class="fas fa-certificate"></i> Sertifikalar</h1>
            <p>Kalite, güvenlik ve çevre standartlarına uygunluğumuzu belgeleyen sertifikalarımız</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Stats Section -->
            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo count($certificates); ?></div>
                        <div class="stat-label">Aktif Sertifika</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo count($categories); ?></div>
                        <div class="stat-label">Kategori</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Uygunluk Oranı</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">15+</div>
                        <div class="stat-label">Yıllık Tecrübe</div>
                    </div>
                </div>
            </div>

            <div class="section-title">
                <h2>Sertifikalarımız</h2>
                <p>Uluslararası standartlara uygunluğumuzu belgeleyen sertifika ve akreditasyonlarımız</p>
            </div>

            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <button class="filter-tab active" data-category="all">
                    <i class="fas fa-th"></i> Tümü
                </button>
                <?php foreach ($categories as $category): ?>
                    <button class="filter-tab" data-category="<?php echo htmlspecialchars($category); ?>">
                        <?php echo htmlspecialchars($category); ?>
                    </button>
                <?php endforeach; ?>
            </div>

            <!-- Certificates Grid -->
            <div class="certificates-grid">
                <?php foreach ($certificates as $certificate): ?>
                    <div class="certificate-card" data-category="<?php echo htmlspecialchars($certificate['category']); ?>">
                        <div class="certificate-image">
                            <i class="fas fa-award"></i>
                            <div class="certificate-status">
                                <?php echo $certificate['status'] === 'active' ? 'Aktif' : 'Pasif'; ?>
                            </div>
                        </div>
                        <div class="certificate-content">
                            <div class="certificate-category">
                                <?php echo htmlspecialchars($certificate['category']); ?>
                            </div>
                            <h3 class="certificate-title">
                                <?php echo htmlspecialchars($certificate['title']); ?>
                            </h3>
                            <p class="certificate-description">
                                <?php echo htmlspecialchars($certificate['description']); ?>
                            </p>
                            <div class="certificate-details">
                                <div class="certificate-detail">
                                    <span class="certificate-detail-label">Veren Kurum:</span>
                                    <span class="certificate-detail-value"><?php echo htmlspecialchars($certificate['issuer']); ?></span>
                                </div>
                                <div class="certificate-detail">
                                    <span class="certificate-detail-label">Veriliş Tarihi:</span>
                                    <span class="certificate-detail-value"><?php echo date('d.m.Y', strtotime($certificate['issue_date'])); ?></span>
                                </div>
                                <div class="certificate-detail">
                                    <span class="certificate-detail-label">Geçerlilik:</span>
                                    <span class="certificate-detail-value"><?php echo date('d.m.Y', strtotime($certificate['expiry_date'])); ?></span>
                                </div>
                            </div>
                            <div class="certificate-number">
                                Sertifika No: <?php echo htmlspecialchars($certificate['certificate_number']); ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </main>

    <?php include 'includes/footer.php'; ?>

    <script>
        // Filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filterTabs = document.querySelectorAll('.filter-tab');
            const certificateCards = document.querySelectorAll('.certificate-card');

            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');

                    // Update active tab
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    // Filter certificates
                    certificateCards.forEach(card => {
                        if (category === 'all' || card.getAttribute('data-category') === category) {
                            card.style.display = 'block';
                            card.style.animation = 'fadeInUp 0.6s ease forwards';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });
        });

        // Smooth scroll for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading animation
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.3s ease';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });

        // Certificate card hover effects
        document.querySelectorAll('.certificate-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>