<?php
// config/config.php - Genel Site Ayarları

// Geliştirme modu (production'da false yapın)
define('DEVELOPMENT', true);

// Site temel ayarları
define('SITE_URL', 'http://localhost/gngmakine');
define('BASE_URL', 'http://localhost/gngmakine');
define('SITE_NAME', 'GNG Makine');
define('SITE_TITLE', 'GNG Makine - Profesyonel Makine Çözümleri');
define('SITE_DESCRIPTION', 'Endüstriyel kalitede makine çözümleri ile üretiminizi artırın');

// Do<PERSON>a yolları
define('UPLOAD_PATH', $_SERVER['DOCUMENT_ROOT'] . '/gngmakine/uploads/');
define('UPLOAD_URL', 'http://localhost/gngmakine/uploads');
define('UPLOADS_URL', 'http://localhost/gngmakine/uploads');
define('ASSETS_URL', 'http://localhost/gngmakine/assets');

// Güvenlik ayarları
define('SESSION_LIFETIME', 3600); // 1 saat
define('CSRF_TOKEN_NAME', 'csrf_token');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// E-posta ayarları
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'GNG Makine');

// Sayfalama ayarları
define('ITEMS_PER_PAGE', 12);
define('ADMIN_ITEMS_PER_PAGE', 20);

// Resim ayarları
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('MAX_IMAGE_WIDTH', 1920);
define('MAX_IMAGE_HEIGHT', 1080);
define('THUMBNAIL_WIDTH', 300);
define('THUMBNAIL_HEIGHT', 300);

// Önbellek ayarları
define('CACHE_ENABLED', false);
define('CACHE_LIFETIME', 3600);

// Timezone ayarı
date_default_timezone_set('Europe/Istanbul');

// Veritabanı ayarları
$db_host = 'localhost';
$db_name = 'gng_makine';
$db_user = 'root';
$db_pass = '';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
} catch(PDOException $e) {
    if (DEVELOPMENT) {
        die("Veritabanı bağlantı hatası: " . $e->getMessage());
    } else {
        die("Veritabanı bağlantı hatası oluştu.");
    }
}

// Session ayarları
if (session_status() === PHP_SESSION_NONE) {
    session_set_cookie_params([
        'lifetime' => SESSION_LIFETIME,
        'path' => '/',
        'domain' => '',
        'secure' => isset($_SERVER['HTTPS']),
        'httponly' => true,
        'samesite' => 'Strict'
    ]);
}

/**
 * Site URL oluşturma fonksiyonu
 */
function url($path = '') {
    $base_url = rtrim(SITE_URL, '/');
    $path = ltrim($path, '/');
    return $base_url . ($path ? '/' . $path : '');
}

/**
 * Asset URL oluşturma fonksiyonu
 */
function asset($path) {
    return ASSETS_URL . ltrim($path, '/');
}

/**
 * Upload URL oluşturma fonksiyonu
 */
function upload_url($path) {
    return UPLOAD_URL . ltrim($path, '/');
}

/**
 * XSS koruması için string temizleme
 */
function escape($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * CSRF token oluşturma
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * CSRF token doğrulama
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Veritabanı sorgu fonksiyonları
 */
function executeQuery($sql, $params = []) {
    global $pdo;
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        if (DEVELOPMENT) {
            die("SQL Hatası: " . $e->getMessage());
        } else {
            error_log("SQL Error: " . $e->getMessage());
            return false;
        }
    }
}

function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetchAll() : [];
}

function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetch() : false;
}

function getLastInsertId() {
    global $pdo;
    return $pdo->lastInsertId();
}

/**
 * Kullanıcı giriş kontrolü
 */
function isLoggedIn() {
    return isset($_SESSION['admin_user_id']) && !empty($_SESSION['admin_user_id']);
}

/**
 * Admin kullanıcı bilgilerini alma
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return false;
    }

    $sql = "SELECT * FROM admin_users WHERE id = ? AND status = 'active'";
    return fetchOne($sql, [$_SESSION['admin_user_id']]);
}

/**
 * Yetki kontrolü
 */
function hasPermission($permission) {
    $user = getCurrentUser();
    if (!$user) {
        return false;
    }
    
    // Admin her şeyi yapabilir
    if ($user['role'] === 'admin') {
        return true;
    }
    
    // Diğer roller için özel kontroller
    $permissions = [
        'editor' => ['create_content', 'edit_content', 'delete_content', 'manage_media'],
        'viewer' => ['view_content']
    ];
    
    return isset($permissions[$user['role']]) && in_array($permission, $permissions[$user['role']]);
}

/**
 * Site ayarını alma
 */
function getSetting($key, $default = null) {
    static $settings = [];
    
    if (empty($settings)) {
        $results = fetchAll("SELECT setting_key, setting_value FROM site_settings");
        foreach ($results as $setting) {
            $settings[$setting['setting_key']] = $setting['setting_value'];
        }
    }
    
    return isset($settings[$key]) ? $settings[$key] : $default;
}

/**
 * Site ayarını güncelleme
 */
function updateSetting($key, $value) {
    $sql = "INSERT INTO site_settings (setting_key, setting_value) VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE setting_value = ?";
    return executeQuery($sql, [$key, $value, $value]);
}

/**
 * Slug oluşturma fonksiyonu
 */
function createSlug($text) {
    // Türkçe karakterleri değiştir
    $turkish = ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'Ç', 'Ğ', 'I', 'İ', 'Ö', 'Ş', 'Ü'];
    $english = ['c', 'g', 'i', 'o', 's', 'u', 'c', 'g', 'i', 'i', 'o', 's', 'u'];
    $text = str_replace($turkish, $english, $text);
    
    // Küçük harfe çevir ve özel karakterleri kaldır
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9]+/', '-', $text);
    $text = trim($text, '-');
    
    return $text;
}

/**
 * Breadcrumb oluşturma
 */
function generateBreadcrumb($items) {
    $html = '<ol class="breadcrumb">';
    $html .= '<li class="breadcrumb-item"><a href="/"><i class="fas fa-home"></i> Ana Sayfa</a></li>';
    
    foreach ($items as $item) {
        if (isset($item['url'])) {
            $html .= '<li class="breadcrumb-item"><a href="' . escape($item['url']) . '">' . escape($item['title']) . '</a></li>';
        } else {
            $html .= '<li class="breadcrumb-item active">' . escape($item['title']) . '</li>';
        }
    }
    
    $html .= '</ol>';
    return $html;
}

/**
 * Dosya boyutunu okunabilir formata çevirme
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Tarih formatını Türkçe'ye çevirme
 */
function formatDateTurkish($date, $format = 'd F Y') {
    $months = [
        'January' => 'Ocak', 'February' => 'Şubat', 'March' => 'Mart',
        'April' => 'Nisan', 'May' => 'Mayıs', 'June' => 'Haziran',
        'July' => 'Temmuz', 'August' => 'Ağustos', 'September' => 'Eylül',
        'October' => 'Ekim', 'November' => 'Kasım', 'December' => 'Aralık'
    ];
    
    $days = [
        'Monday' => 'Pazartesi', 'Tuesday' => 'Salı', 'Wednesday' => 'Çarşamba',
        'Thursday' => 'Perşembe', 'Friday' => 'Cuma', 'Saturday' => 'Cumartesi',
        'Sunday' => 'Pazar'
    ];
    
    $formatted = date($format, strtotime($date));
    $formatted = str_replace(array_keys($months), array_values($months), $formatted);
    $formatted = str_replace(array_keys($days), array_values($days), $formatted);
    
    return $formatted;
}

/**
 * Başarı mesajı ekleme
 */
function addSuccessMessage($message) {
    $_SESSION['success_messages'][] = $message;
}

/**
 * Hata mesajı ekleme
 */
function addErrorMessage($message) {
    $_SESSION['error_messages'][] = $message;
}

/**
 * Mesajları alma ve temizleme
 */
function getMessages() {
    $messages = [
        'success' => $_SESSION['success_messages'] ?? [],
        'error' => $_SESSION['error_messages'] ?? []
    ];
    
    unset($_SESSION['success_messages'], $_SESSION['error_messages']);
    
    return $messages;
}

/**
 * Yönlendirme fonksiyonu
 */
function redirect($url, $permanent = false) {
    if (!headers_sent()) {
        if ($permanent) {
            header('HTTP/1.1 301 Moved Permanently');
        }
        header('Location: ' . $url);
        exit;
    }
}

/**
 * JSON response gönderme
 */
function jsonResponse($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}
?>