<?php
/**
 * GNG Makine - Genel Konfigürasyon Dosyası
 */

// Oturumu başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Hata raporlama (production'da kapatılabilir)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone ayarı
date_default_timezone_set('Europe/Istanbul');

// Dosya yolları
define('ROOT_PATH', __DIR__ . '/../');
define('UPLOAD_PATH', ROOT_PATH . 'uploads/');
define('ASSETS_PATH', ROOT_PATH . 'assets/');
define('INCLUDES_PATH', ROOT_PATH . 'includes/');
define('ADMIN_PATH', ROOT_PATH . 'admin/');

// Site ayarları
define('SITE_NAME', 'GNG Makine');
define('SITE_URL', 'http://localhost/gngmakine');
define('SITE_DESCRIPTION', 'Profesyonel endüstriyel makine çözümleri');
define('SITE_KEYWORDS', 'makine, endüstriyel, üretim, GNG');

// URL yolları
define('BASE_URL', SITE_URL);
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_URL', BASE_URL . '/uploads');
define('ADMIN_URL', BASE_URL . '/admin');

// Güvenlik ayarları
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 saat

// Dosya yükleme ayarları
define('MAX_FILE_SIZE', 10485760); // 10MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'xls', 'xlsx']);

// Sayfalama ayarları
define('PRODUCTS_PER_PAGE', 12);
define('ARTICLES_PER_PAGE', 10);
define('ADMIN_ITEMS_PER_PAGE', 20);

// Sosyal medya
define('FACEBOOK_URL', 'https://facebook.com/gngmakine');
define('TWITTER_URL', 'https://twitter.com/gngmakine');
define('INSTAGRAM_URL', 'https://instagram.com/gngmakine');
define('LINKEDIN_URL', 'https://linkedin.com/company/gngmakine');

// Debug modu
define('DEBUG_MODE', true);

// Sabitleri dahil et (önce constants.php'yi dahil et)
require_once __DIR__ . '/constants.php';

// Veritabanı dosyasını dahil et
require_once __DIR__ . '/database.php';

// Yardımcı fonksiyonları dahil et
require_once ROOT_PATH . 'includes/functions.php';

// Site ayarlarını veritabanından çek
$site_settings = [];
try {
    $db = Database::getInstance();
    $settings = $db->fetchAll("SELECT setting_key, setting_value FROM site_settings");
    
    foreach ($settings as $setting) {
        $site_settings[$setting['setting_key']] = $setting['setting_value'];
    }
} catch (Exception $e) {
    // Hata durumunda varsayılan ayarları kullan
    $site_settings = [
        'site_title' => 'GNG Makine',
        'site_description' => 'Profesyonel makine çözümleri',
        'company_name' => 'GNG Makine Ltd. Şti.',
        'company_email' => '<EMAIL>',
        'company_phone' => '+90 555 123 45 67',
        'company_address' => 'Gaziantep, Türkiye'
    ];
}

// Meta etiketler
define('DEFAULT_META_TITLE', $site_settings['site_title'] ?? 'GNG Makine');
define('DEFAULT_META_DESCRIPTION', $site_settings['site_description'] ?? 'Profesyonel makine çözümleri');
define('DEFAULT_META_KEYWORDS', 'makine, endüstriyel, üretim, GNG, Gaziantep');
?>
?>