<?php
/**
 * GNG Makine - Geliştirilmiş Konfigürasyon
 * Debug ve hata yakalama ile güçlendirilmiş
 */

// Hata raporlama ve debug ayarları
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/error.log');

// Geliştirme modu (production'da false yapın)
define('DEVELOPMENT', true);
define('DEBUG_MODE', true);

// Site ayarları dizisi
$site_settings = [
    'company_name' => 'GNG Makine',
    'site_description' => 'Profesyonel endüstriyel makine çözümleri ile sektörde lider konumumuzu sürdürüyoruz.',
    'company_address' => 'Organize Sanayi Bölgesi, 1. Cadde No:15, İstanbul',
    'company_phone' => '+90 ************',
    'company_email' => '<EMAIL>',
    'whatsapp_number' => '+90 ************'
];

// Site temel ayarları
define('SITE_URL', 'http://localhost/gngmakine');
define('BASE_URL', 'http://localhost/gngmakine');
define('SITE_NAME', 'GNG Makine');
define('SITE_TITLE', 'GNG Makine - Profesyonel Makine Çözümleri');
define('SITE_DESCRIPTION', 'Endüstriyel kalitede makine çözümleri ile üretiminizi artırın');

// Dosya yolları
define('UPLOAD_PATH', $_SERVER['DOCUMENT_ROOT'] . '/gngmakine/uploads/');
define('UPLOAD_URL', 'http://localhost/gngmakine/uploads');
define('UPLOADS_URL', 'http://localhost/gngmakine/uploads');
define('ASSETS_URL', 'http://localhost/gngmakine/assets');

// Güvenlik ayarları
define('SESSION_LIFETIME', 3600); // 1 saat
define('CSRF_TOKEN_NAME', 'csrf_token');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Timezone ayarı
date_default_timezone_set('Europe/Istanbul');

// Veritabanı ayarları
$db_config = [
    'host' => 'localhost',
    'dbname' => 'gng_makine',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]
];

// Global query counter for debugging
$GLOBALS['query_count'] = 0;

// Veritabanı bağlantısı
try {
    $dsn = sprintf(
        "mysql:host=%s;dbname=%s;charset=%s",
        $db_config['host'],
        $db_config['dbname'],
        $db_config['charset']
    );

    $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], $db_config['options']);
    
    if (DEBUG_MODE) {
        echo "<!-- Veritabanı bağlantısı başarılı -->\n";
    }
    
} catch(PDOException $e) {
    $error_message = "Veritabanı bağlantı hatası: " . $e->getMessage();
    
    if (DEVELOPMENT) {
        die("<div style='background:#f8d7da;color:#721c24;padding:20px;margin:20px;border:1px solid #f5c6cb;border-radius:5px;'>
                <h3>Veritabanı Bağlantı Hatası</h3>
                <p><strong>Hata:</strong> {$error_message}</p>
                <p><strong>Dosya:</strong> " . __FILE__ . "</p>
                <p><strong>Satır:</strong> " . __LINE__ . "</p>
                <hr>
                <h4>Kontrol Edilecekler:</h4>
                <ul>
                    <li>MySQL servisi çalışıyor mu?</li>
                    <li>Veritabanı adı doğru mu? (<code>{$db_config['dbname']}</code>)</li>
                    <li>Kullanıcı adı ve şifre doğru mu?</li>
                    <li>Veritabanı oluşturuldu mu?</li>
                </ul>
            </div>");
    } else {
        error_log($error_message);
        die("Veritabanı bağlantısında bir sorun oluştu. Lütfen daha sonra tekrar deneyin.");
    }
}

// Session başlatma
if (session_status() === PHP_SESSION_NONE) {
    session_set_cookie_params([
        'lifetime' => SESSION_LIFETIME,
        'path' => '/',
        'domain' => '',
        'secure' => isset($_SERVER['HTTPS']),
        'httponly' => true,
        'samesite' => 'Strict'
    ]);
    session_start();
}

/**
 * Gelişmiş veritabanı sorgu fonksiyonu
 */
function executeQuery($sql, $params = []) {
    global $pdo;
    $GLOBALS['query_count']++;
    
    try {
        if (DEBUG_MODE) {
            error_log("SQL Query #{$GLOBALS['query_count']}: " . $sql);
            if (!empty($params)) {
                error_log("Parameters: " . json_encode($params));
            }
        }
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($params);
        
        if (!$result) {
            throw new PDOException("Query execution failed");
        }
        
        return $stmt;
    } catch (PDOException $e) {
        $error_message = "SQL Hatası: " . $e->getMessage() . " | SQL: " . $sql;
        
        if (DEVELOPMENT) {
            die("<div style='background:#f8d7da;color:#721c24;padding:20px;margin:20px;border:1px solid #f5c6cb;border-radius:5px;'>
                    <h3>SQL Hatası</h3>
                    <p><strong>Hata:</strong> {$e->getMessage()}</p>
                    <p><strong>SQL:</strong> <code>{$sql}</code></p>
                    <p><strong>Parametreler:</strong> <code>" . json_encode($params) . "</code></p>
                </div>");
        } else {
            error_log($error_message);
            return false;
        }
    }
}

function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetchAll() : [];
}

function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetch() : false;
}

/**
 * Veritabanı test fonksiyonu
 */
function testDatabaseConnection() {
    try {
        $tables = fetchAll("SHOW TABLES");
        $table_count = count($tables);
        
        if (DEBUG_MODE) {
            echo "<!-- Veritabanında {$table_count} tablo bulundu -->\n";
            foreach ($tables as $table) {
                $table_name = array_values($table)[0];
                $count = fetchOne("SELECT COUNT(*) as count FROM `{$table_name}`");
                echo "<!-- Tablo '{$table_name}': {$count['count']} kayıt -->\n";
            }
        }
        
        return true;
    } catch (Exception $e) {
        if (DEBUG_MODE) {
            echo "<!-- Veritabanı test hatası: " . $e->getMessage() . " -->\n";
        }
        return false;
    }
}

/**
 * CSRF token oluşturma
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * XSS koruması
 */
function escape($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * URL oluşturma fonksiyonları
 */
function url($path = '') {
    $base_url = rtrim(SITE_URL, '/');
    $path = ltrim($path, '/');
    return $base_url . ($path ? '/' . $path : '');
}

function asset($path) {
    return ASSETS_URL . '/' . ltrim($path, '/');
}

/**
 * Debug bilgisi yazdırma
 */
function debugInfo($title, $data) {
    if (DEBUG_MODE) {
        echo "<!-- DEBUG: {$title} -->\n";
        echo "<!-- " . print_r($data, true) . " -->\n";
    }
}

// Veritabanı bağlantısını test et
if (DEBUG_MODE) {
    testDatabaseConnection();
}

// Debug modunda ekstra bilgiler
if (DEBUG_MODE) {
    echo "<!-- Debug Mode Aktif -->\n";
    echo "<!-- PHP Version: " . PHP_VERSION . " -->\n";
    echo "<!-- Memory Usage: " . memory_get_usage(true) . " bytes -->\n";
    echo "<!-- Time: " . date('Y-m-d H:i:s') . " -->\n";
}
?>