<?php
/**
 * GNG Makine - Sabitler Dosyası
 * Tüm uygulama sabitleri bu dosyada tanımlanır
 */

// ============================================================================
// VERİTABANI SABİTLERİ
// ============================================================================

// Tablo adları
define('TBL_ADMIN_USERS', 'admin_users');
define('TBL_CATEGORIES', 'categories');
define('TBL_PRODUCTS', 'products');
define('TBL_ARTICLES', 'articles');
define('TBL_MEDIA', 'media');
define('TBL_SITE_SETTINGS', 'site_settings');
define('TBL_CONTACT_MESSAGES', 'contact_messages');
define('TBL_ADMIN_SESSIONS', 'admin_sessions');

// ============================================================================
// DURUM SABİTLERİ
// ============================================================================

// Genel durumlar
define('STATUS_ACTIVE', 'active');
define('STATUS_INACTIVE', 'inactive');
define('STATUS_DRAFT', 'draft');
define('STATUS_ARCHIVED', 'archived');
define('STATUS_PUBLISHED', 'published');

// Kullanıcı rolleri
define('ROLE_ADMIN', 'admin');
define('ROLE_EDITOR', 'editor');
define('ROLE_VIEWER', 'viewer');

// İletişim mesajı durumları
define('MESSAGE_NEW', 'new');
define('MESSAGE_READ', 'read');
define('MESSAGE_REPLIED', 'replied');
define('MESSAGE_ARCHIVED', 'archived');

// Dosya tipleri
define('FILE_TYPE_IMAGE', 'image');
define('FILE_TYPE_VIDEO', 'video');
define('FILE_TYPE_DOCUMENT', 'document');
define('FILE_TYPE_OTHER', 'other');

// ============================================================================
// DOSYA VE YÜKLEME SABİTLERİ
// ============================================================================

// Dosya boyut limitleri (byte)
define('MAX_IMAGE_SIZE', 5242880); // 5MB
define('MAX_DOCUMENT_SIZE', 10485760); // 10MB
define('MAX_VIDEO_SIZE', 52428800); // 50MB

// İzin verilen dosya uzantıları
define('ALLOWED_IMAGE_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']);
define('ALLOWED_DOCUMENT_EXTENSIONS', ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']);
define('ALLOWED_VIDEO_EXTENSIONS', ['mp4', 'webm', 'ogg', 'avi', 'mov']);

// Resim boyutları
define('THUMBNAIL_WIDTH', 300);
define('THUMBNAIL_HEIGHT', 200);
define('MEDIUM_WIDTH', 600);
define('MEDIUM_HEIGHT', 400);
define('LARGE_WIDTH', 1200);
define('LARGE_HEIGHT', 800);

// ============================================================================
// SAYFALAMA SABİTLERİ
// ============================================================================

define('DEFAULT_PAGE_SIZE', 10);
define('PRODUCTS_PAGE_SIZE', 12);
define('ARTICLES_PAGE_SIZE', 8);
define('ADMIN_PAGE_SIZE', 25);
define('SEARCH_PAGE_SIZE', 15);

// ============================================================================
// GÜVENLİK SABİTLERİ
// ============================================================================

// Oturum sabitleri
define('SESSION_LIFETIME', 3600); // 1 saat
define('REMEMBER_ME_LIFETIME', 2592000); // 30 gün
define('CSRF_TOKEN_LIFETIME', 7200); // 2 saat

// Giriş denemeleri
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 dakika
define('PASSWORD_MIN_LENGTH', 8);

// Rate limiting
define('RATE_LIMIT_REQUESTS', 100);
define('RATE_LIMIT_WINDOW', 3600); // 1 saat

// ============================================================================
// CACHE SABİTLERİ
// ============================================================================

define('CACHE_DEFAULT_TTL', 3600); // 1 saat
define('CACHE_PRODUCTS_TTL', 1800); // 30 dakika
define('CACHE_CATEGORIES_TTL', 7200); // 2 saat
define('CACHE_SETTINGS_TTL', 86400); // 24 saat

// Cache anahtarları
define('CACHE_KEY_PRODUCTS', 'products_');
define('CACHE_KEY_CATEGORIES', 'categories_');
define('CACHE_KEY_ARTICLES', 'articles_');
define('CACHE_KEY_SETTINGS', 'settings');

// ============================================================================
// E-POSTA SABİTLERİ
// ============================================================================

// E-posta türleri
define('EMAIL_TYPE_CONTACT', 'contact');
define('EMAIL_TYPE_NEWSLETTER', 'newsletter');
define('EMAIL_TYPE_NOTIFICATION', 'notification');
define('EMAIL_TYPE_PASSWORD_RESET', 'password_reset');

// E-posta şablonları
define('EMAIL_TEMPLATE_CONTACT', 'contact_form');
define('EMAIL_TEMPLATE_NEWSLETTER', 'newsletter_subscription');
define('EMAIL_TEMPLATE_WELCOME', 'welcome');
define('EMAIL_TEMPLATE_PASSWORD_RESET', 'password_reset');

// ============================================================================
// LOG SABİTLERİ
// ============================================================================

// Log seviyeleri
define('LOG_LEVEL_DEBUG', 'DEBUG');
define('LOG_LEVEL_INFO', 'INFO');
define('LOG_LEVEL_WARNING', 'WARNING');
define('LOG_LEVEL_ERROR', 'ERROR');
define('LOG_LEVEL_CRITICAL', 'CRITICAL');

// Log dosyaları
define('LOG_FILE_APP', 'app.log');
define('LOG_FILE_ERROR', 'error.log');
define('LOG_FILE_ACCESS', 'access.log');
define('LOG_FILE_SECURITY', 'security.log');

// ============================================================================
// CUSTOM APPLICATION CONSTANTS
// ============================================================================

// Company specific
if (!defined('COMPANY_FOUNDED_YEAR')) {
    define('COMPANY_FOUNDED_YEAR', 2010);
}
if (!defined('COMPANY_EXPERIENCE_YEARS')) {
    define('COMPANY_EXPERIENCE_YEARS', date('Y') - COMPANY_FOUNDED_YEAR);
}

// Product categories (enum values for consistency)
define('CATEGORY_PRODUCTION', 'uretim');
define('CATEGORY_WORKER', 'calisan');
define('CATEGORY_FIELD', 'alan');
define('CATEGORY_CUSTOMER', 'musteri');

// Feature flags
define('FEATURE_NEWSLETTER', true);
define('FEATURE_LIVE_CHAT', true);
define('FEATURE_MULTI_LANGUAGE', false);
define('FEATURE_E_COMMERCE', false);
define('FEATURE_BOOKING_SYSTEM', false);

// Theme settings
define('THEME_PRIMARY_COLOR', '#F9B233');
define('THEME_SECONDARY_COLOR', '#8A8C8F');
define('THEME_ACCENT_COLOR', '#e67e22');

// Contact form subjects
define('CONTACT_SUBJECT_GENERAL', 'Genel Bilgi');
define('CONTACT_SUBJECT_QUOTE', 'Fiyat Teklifi');
define('CONTACT_SUBJECT_SUPPORT', 'Teknik Destek');
define('CONTACT_SUBJECT_PARTNERSHIP', 'İş Ortaklığı');

// ============================================================================
// HATA VE BAŞARI MESAJLARI
// ============================================================================

define('ERROR_GENERAL', 'Bir hata oluştu. Lütfen tekrar deneyin.');
define('ERROR_NOT_FOUND', 'Aradığınız sayfa bulunamadı.');
define('SUCCESS_GENERAL', 'İşlem başarıyla tamamlandı.');
define('SUCCESS_EMAIL_SENT', 'E-posta başarıyla gönderildi.');

/**
 * Helper functions for constants
 */
function get_constants_by_prefix($prefix) {
    $constants = get_defined_constants(true)['user'];
    $filtered = [];
    
    foreach ($constants as $name => $value) {
        if (strpos($name, $prefix) === 0) {
            $filtered[$name] = $value;
        }
    }
    
    return $filtered;
}

function get_constant_or_default($constant_name, $default = null) {
    return defined($constant_name) ? constant($constant_name) : $default;
}
?>