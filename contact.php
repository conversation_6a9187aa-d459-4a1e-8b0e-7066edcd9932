<?php
/**
 * GNG Makine - <PERSON><PERSON><PERSON><PERSON><PERSON>
 */

// Konfigürasyonu da<PERSON> et
require_once __DIR__ . '/config/config.php';

// Sayfa meta bilgileri
$page_title = "İletişim - " . SITE_NAME;
$page_description = "GNG Makine ile iletişime geçin. Endüstriyel makine ihtiyaçlarınız için uzman ekibimizden destek alın.";
$page_keywords = "iletişim, GNG Makine, endüstriyel makine, destek, telefon, adres";
$current_page = "contact";

// Form gönderildi mi kontrol et
$form_success = false;
$form_error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Form verilerini al ve temizle
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');

    // Basit validasyon
    if (empty($name) || empty($email) || empty($message)) {
        $form_error = 'Lütfen tüm zorunlu alanları doldurun.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $form_error = 'Geçerli bir e-posta adresi girin.';
    } else {
        try {
            // Veritabanına kaydet
            $stmt = $pdo->prepare("INSERT INTO contact_messages (name, email, phone, subject, message, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$name, $email, $phone, $subject, $message]);

            $form_success = true;

            // Form verilerini temizle
            $name = $email = $phone = $subject = $message = '';

        } catch (PDOException $e) {
            error_log("Contact form error: " . $e->getMessage());
            $form_error = 'Mesajınız gönderilirken bir hata oluştu. Lütfen tekrar deneyin.';
        }
    }
}

include 'includes/header.php';
?>

<style>
/* İletişim Sayfası Özel Stilleri */
.page-header {
    background: linear-gradient(135deg, var(--text-dark), #34495e);
    color: var(--white);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('<?php echo ASSETS_URL; ?>/images/patterns/dots.png') repeat;
    opacity: 0.1;
    z-index: 1;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 20px;
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
    font-size: 1rem;
}

.breadcrumb a {
    color: var(--white);
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.breadcrumb a:hover {
    opacity: 1;
}

.breadcrumb span {
    opacity: 0.6;
}

/* Contact Section */
.contact-section {
    padding: 100px 0;
    background: var(--white);
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: start;
}

.contact-info h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 25px;
}

.contact-info p {
    font-size: 1.1rem;
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 40px;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 25px;
    background: var(--bg-light);
    border-radius: 15px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-content h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 8px;
}

.contact-content p {
    color: var(--text-light);
    margin: 0;
    line-height: 1.5;
}

.contact-content a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.contact-content a:hover {
    color: var(--accent-color);
}

/* Contact Form */
.contact-form {
    background: var(--white);
    padding: 40px;
    border-radius: 20px;
    box-shadow: var(--shadow);
    border: 2px solid var(--bg-light);
}

.contact-form h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 30px;
    text-align: center;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 8px;
    font-size: 1rem;
}

.form-group .required {
    color: var(--primary-color);
}

.form-control {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid var(--bg-light);
    border-radius: 10px;
    font-size: 1rem;
    font-family: var(--font-family);
    transition: all 0.3s ease;
    background: var(--white);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.1);
}

.form-control::placeholder {
    color: var(--text-light);
}

textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.alert {
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.btn-submit {
    width: 100%;
    padding: 18px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-submit:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(243, 156, 18, 0.3);
}

/* Map Section */
.map-section {
    padding: 80px 0;
    background: var(--bg-light);
}

.map-container {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow);
    height: 400px;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header h1 {
        font-size: 2.2rem;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 50px;
    }

    .contact-form {
        padding: 30px 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .contact-item {
        padding: 20px;
    }

    .contact-icon {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}
</style>

<?php include 'includes/navbar.php'; ?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1>İletişim</h1>
        <p>Endüstriyel makine ihtiyaçlarınız için uzman ekibimizle iletişime geçin. Size en uygun çözümü sunmak için buradayız.</p>

        <div class="breadcrumb">
            <a href="<?php echo BASE_URL; ?>">Ana Sayfa</a>
            <span><i class="fas fa-chevron-right"></i></span>
            <span>İletişim</span>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="contact-section">
    <div class="container">
        <div class="contact-grid">
            <!-- Contact Info -->
            <div class="contact-info">
                <h2>Bizimle İletişime Geçin</h2>
                <p>15 yılı aşkın tecrübemizle endüstriyel makine sektöründe güvenilir çözüm ortağınızız. Projeleriniz için en uygun makine çözümlerini birlikte belirleyelim.</p>

                <div class="contact-details">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-content">
                            <h3>Adresimiz</h3>
                            <p>Organize Sanayi Bölgesi<br>
                            Makine Sanayi Caddesi No: 123<br>
                            34000 İstanbul, Türkiye</p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-content">
                            <h3>Telefon</h3>
                            <p><a href="tel:+902121234567">+90 (212) 123 45 67</a><br>
                            <a href="tel:+905321234567">+90 (532) 123 45 67</a></p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-content">
                            <h3>E-posta</h3>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a><br>
                            <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="contact-content">
                            <h3>Çalışma Saatleri</h3>
                            <p>Pazartesi - Cuma: 08:00 - 18:00<br>
                            Cumartesi: 09:00 - 16:00<br>
                            Pazar: Kapalı</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Form -->
            <div class="contact-form">
                <h2>Mesaj Gönderin</h2>

                <?php if ($form_success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        Mesajınız başarıyla gönderildi! En kısa sürede size dönüş yapacağız.
                    </div>
                <?php endif; ?>

                <?php if ($form_error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($form_error); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Ad Soyad <span class="required">*</span></label>
                            <input type="text" id="name" name="name" class="form-control"
                                   value="<?php echo htmlspecialchars($name ?? ''); ?>"
                                   placeholder="Adınız ve soyadınız" required>
                        </div>

                        <div class="form-group">
                            <label for="email">E-posta <span class="required">*</span></label>
                            <input type="email" id="email" name="email" class="form-control"
                                   value="<?php echo htmlspecialchars($email ?? ''); ?>"
                                   placeholder="<EMAIL>" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone">Telefon</label>
                            <input type="tel" id="phone" name="phone" class="form-control"
                                   value="<?php echo htmlspecialchars($phone ?? ''); ?>"
                                   placeholder="+90 (5xx) xxx xx xx">
                        </div>

                        <div class="form-group">
                            <label for="subject">Konu</label>
                            <select id="subject" name="subject" class="form-control">
                                <option value="">Konu seçin</option>
                                <option value="Genel Bilgi" <?php echo ($subject ?? '') === 'Genel Bilgi' ? 'selected' : ''; ?>>Genel Bilgi</option>
                                <option value="Ürün Talebi" <?php echo ($subject ?? '') === 'Ürün Talebi' ? 'selected' : ''; ?>>Ürün Talebi</option>
                                <option value="Teknik Destek" <?php echo ($subject ?? '') === 'Teknik Destek' ? 'selected' : ''; ?>>Teknik Destek</option>
                                <option value="Fiyat Teklifi" <?php echo ($subject ?? '') === 'Fiyat Teklifi' ? 'selected' : ''; ?>>Fiyat Teklifi</option>
                                <option value="Diğer" <?php echo ($subject ?? '') === 'Diğer' ? 'selected' : ''; ?>>Diğer</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="message">Mesajınız <span class="required">*</span></label>
                        <textarea id="message" name="message" class="form-control"
                                  placeholder="Mesajınızı buraya yazın..." required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                    </div>

                    <button type="submit" class="btn-submit">
                        <i class="fas fa-paper-plane"></i>
                        Mesaj Gönder
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="map-section">
    <div class="container">
        <div class="section-header">
            <h2>Konumumuz</h2>
            <p>Organize Sanayi Bölgesi'ndeki modern tesisimizde sizi ağırlamaktan mutluluk duyarız.</p>
        </div>

        <div class="map-container">
            <div style="text-align: center;">
                <i class="fas fa-map-marked-alt" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 20px;"></i>
                <p>Harita entegrasyonu için Google Maps API'si eklenecek</p>
                <p style="color: var(--text-light); font-size: 0.9rem;">Organize Sanayi Bölgesi, Makine Sanayi Caddesi No: 123, İstanbul</p>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>