<?php
require_once '../config/config.php';

// Admin kullanıcısı oluştur
$username = 'admin';
$password = 'admin123';
$email = '<EMAIL>';
$full_name = 'Admin User';

// <PERSON><PERSON>reyi hash'le
$hashed_password = password_hash($password, PASSWORD_DEFAULT);

try {
    // Admin kullanıcısını ekle
    $sql = "INSERT INTO admin_users (username, password, email, full_name, role, status, created_at) 
            VALUES (?, ?, ?, ?, 'admin', 'active', NOW())";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([$username, $hashed_password, $email, $full_name]);
    
    if ($result) {
        echo "Admin kullanıcısı başarıyla oluşturuldu!<br>";
        echo "Kullanıcı Adı: " . $username . "<br>";
        echo "Şifre: " . $password . "<br>";
        echo "E-posta: " . $email . "<br>";
    } else {
        echo "Admin kullanıcısı oluşturulamadı!";
    }
    
} catch (PDOException $e) {
    echo "Hata: " . $e->getMessage();
}
?>
