<?php
require_once '../config/config.php';

// Admin kullanıcısı oluştur
$username = 'gngadmin';
$password = 'GNG2024!';
$email = '<EMAIL>';
$full_name = 'GNG Admin';

// <PERSON><PERSON>reyi hash'le
$hashed_password = password_hash($password, PASSWORD_DEFAULT);

echo "<h2>GNG Makine Admin Kullanıcısı Oluşturma</h2>";

try {
    // Önce mevcut admin kullanıcılarını kontrol et
    $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM admin_users WHERE username = ? OR email = ?");
    $check_stmt->execute([$username, $email]);
    $existing_count = $check_stmt->fetchColumn();

    if ($existing_count > 0) {
        echo "<p style='color: orange;'>⚠️ Bu kullanıcı adı veya e-posta zaten mevcut!</p>";

        // Mevcut kullanıcıyı güncelle
        $update_sql = "UPDATE admin_users SET password = ?, full_name = ?, role = 'admin', status = 'active', updated_at = NOW()
                       WHERE username = ? OR email = ?";
        $update_stmt = $pdo->prepare($update_sql);
        $update_result = $update_stmt->execute([$hashed_password, $full_name, $username, $email]);

        if ($update_result) {
            echo "<p style='color: green;'>✅ Mevcut admin kullanıcısı güncellendi!</p>";
        } else {
            echo "<p style='color: red;'>❌ Kullanıcı güncellenemedi!</p>";
        }
    } else {
        // Yeni admin kullanıcısını ekle
        $sql = "INSERT INTO admin_users (username, password, email, full_name, role, status, created_at)
                VALUES (?, ?, ?, ?, 'admin', 'active', NOW())";

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([$username, $hashed_password, $email, $full_name]);

        if ($result) {
            echo "<p style='color: green;'>✅ Yeni admin kullanıcısı başarıyla oluşturuldu!</p>";
        } else {
            echo "<p style='color: red;'>❌ Admin kullanıcısı oluşturulamadı!</p>";
        }
    }

    echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Giriş Bilgileri:</h3>";
    echo "<strong>Kullanıcı Adı:</strong> " . $username . "<br>";
    echo "<strong>Şifre:</strong> " . $password . "<br>";
    echo "<strong>E-posta:</strong> " . $email . "<br>";
    echo "<strong>Ad Soyad:</strong> " . $full_name . "<br>";
    echo "</div>";

    echo "<p><a href='../admin/login.php' style='background: #F9B233; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Admin Paneline Git</a></p>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Veritabanı Hatası: " . $e->getMessage() . "</p>";
    echo "<p>Lütfen şunları kontrol edin:</p>";
    echo "<ul>";
    echo "<li>MySQL servisi çalışıyor mu?</li>";
    echo "<li>'gng_makine' veritabanı var mı?</li>";
    echo "<li>'admin_users' tablosu oluşturuldu mu?</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: 'Inter', Arial, sans-serif;
    margin: 20px;
    background: #f8f9fa;
    color: #2c3e50;
}
h2 { color: #F9B233; }
</style>
