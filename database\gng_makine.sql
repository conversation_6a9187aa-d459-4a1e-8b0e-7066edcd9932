-- GNG Makine Veritabanı Yapısı
-- Veritabanı: gng_makine

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- Veritabanı oluştur
CREATE DATABASE IF NOT EXISTS `gng_makine` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `gng_makine`;

-- --------------------------------------------------------

-- Kategoriler tablosu
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text,
  `image` varchar(255) DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `status` enum('active','inactive') DEFAULT 'active',
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `parent_id` (`parent_id`),
  KEY `status` (`status`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Ürünler tablosu
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `short_description` text,
  `description` longtext,
  `specifications` longtext,
  `features` longtext,
  `main_image` varchar(255) DEFAULT NULL,
  `gallery_images` text,
  `category_id` int(11) DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `currency` varchar(3) DEFAULT 'TRY',
  `stock_status` enum('in_stock','out_of_stock','on_order') DEFAULT 'in_stock',
  `featured` tinyint(1) DEFAULT 0,
  `status` enum('active','inactive','draft') DEFAULT 'draft',
  `sort_order` int(11) DEFAULT 0,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text,
  `meta_keywords` text,
  `views` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `category_id` (`category_id`),
  KEY `status` (`status`),
  KEY `featured` (`featured`),
  KEY `sort_order` (`sort_order`),
  FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Makaleler/Blog tablosu
CREATE TABLE `articles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `excerpt` text,
  `content` longtext,
  `featured_image` varchar(255) DEFAULT NULL,
  `author` varchar(100) DEFAULT NULL,
  `featured` tinyint(1) DEFAULT 0,
  `status` enum('published','draft','archived') DEFAULT 'draft',
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text,
  `meta_keywords` text,
  `views` int(11) DEFAULT 0,
  `published_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `status` (`status`),
  KEY `featured` (`featured`),
  KEY `published_at` (`published_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Site ayarları tablosu
CREATE TABLE `site_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `setting_type` enum('text','textarea','number','boolean','file') DEFAULT 'text',
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- İletişim mesajları tablosu
CREATE TABLE `contact_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(150) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `company` varchar(150) DEFAULT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `status` enum('new','read','replied','archived') DEFAULT 'new',
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Admin kullanıcıları tablosu
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(150) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `role` enum('admin','editor','viewer') DEFAULT 'editor',
  `status` enum('active','inactive') DEFAULT 'active',
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Medya dosyaları tablosu
CREATE TABLE `media` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filename` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) DEFAULT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `file_type` enum('image','document','video','other') DEFAULT 'other',
  `alt_text` varchar(255) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `description` text,
  `uploaded_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `file_type` (`file_type`),
  KEY `uploaded_by` (`uploaded_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- ÖRNEK VERİLER
-- --------------------------------------------------------

-- Site ayarları
INSERT INTO `site_settings` (`setting_key`, `setting_value`, `setting_type`, `description`) VALUES
('site_title', 'GNG Makine - Profesyonel Makine Çözümleri', 'text', 'Site başlığı'),
('site_description', 'Endüstriyel kalitede makine çözümleri ile üretiminizi artırın', 'textarea', 'Site açıklaması'),
('company_name', 'GNG Makine', 'text', 'Şirket adı'),
('company_phone', '+90 ************', 'text', 'Şirket telefonu'),
('company_email', '<EMAIL>', 'text', 'Şirket e-postası'),
('company_address', 'Organize Sanayi Bölgesi, 1. Cadde No:15, İstanbul', 'textarea', 'Şirket adresi'),
('logo_path', '/assets/images/logo.png', 'file', 'Site logosu'),
('favicon_path', '/assets/images/favicon.ico', 'file', 'Site favicon'),
('social_facebook', 'https://facebook.com/gngmakine', 'text', 'Facebook sayfası'),
('social_instagram', 'https://instagram.com/gngmakine', 'text', 'Instagram sayfası'),
('social_linkedin', 'https://linkedin.com/company/gngmakine', 'text', 'LinkedIn sayfası'),
('contact_form_email', '<EMAIL>', 'text', 'İletişim formu e-postası');

-- Kategoriler
INSERT INTO `categories` (`name`, `slug`, `description`, `sort_order`, `status`) VALUES
('Kesim Makineleri', 'kesim-makineleri', 'Profesyonel kesim makineleri ve ekipmanları', 1, 'active'),
('Kaynak Makineleri', 'kaynak-makineleri', 'Endüstriyel kaynak makineleri ve aksesuarları', 2, 'active'),
('Tornalar', 'tornalar', 'CNC ve konvansiyonel torna tezgahları', 3, 'active'),
('Freze Makineleri', 'freze-makineleri', 'Hassas işleme için freze makineleri', 4, 'active'),
('Pres Makineleri', 'pres-makineleri', 'Hidrolik ve mekanik pres makineleri', 5, 'active'),
('Taşlama Makineleri', 'taslama-makineleri', 'Yüzey ve silindirik taşlama makineleri', 6, 'active');

-- Ürünler
INSERT INTO `products` (`name`, `slug`, `short_description`, `description`, `specifications`, `features`, `category_id`, `price`, `featured`, `status`, `sort_order`) VALUES
('CNC Torna Tezgahı CT-250', 'cnc-torna-tezgahi-ct-250', 'Yüksek hassasiyetli CNC torna tezgahı', 'Endüstriyel üretim için tasarlanmış yüksek hassasiyetli CNC torna tezgahı. Modern kontrol sistemi ve güçlü motor yapısı ile verimli üretim sağlar.', 'Çap: 250mm\nBoy: 1000mm\nMotor Gücü: 15kW\nDevir: 4000 rpm\nKontrol: Fanuc', 'Yüksek hassasiyet\nOtomatik takım değiştirici\nSoğutma sistemi\nDijital gösterge', 3, 450000.00, 1, 'active', 1),
('Hidrolik Pres HP-100', 'hidrolik-pres-hp-100', '100 ton kapasiteli hidrolik pres', 'Ağır sanayi uygulamaları için 100 ton kapasiteli hidrolik pres makinesi. Güvenlik sistemleri ve hassas kontrol ile donatılmıştır.', 'Kapasite: 100 ton\nÇalışma Alanı: 800x600mm\nStrok: 400mm\nMotor: 11kW', 'Güvenlik ışık perdesi\nPLC kontrol\nAcil durdurma sistemi\nHassas basınç kontrolü', 5, 180000.00, 1, 'active', 2),
('Plazma Kesim Makinesi PK-40', 'plazma-kesim-makinesi-pk-40', '40A plazma kesim makinesi', 'Hassas kesim işlemleri için 40 amper plazma kesim makinesi. Çelik, paslanmaz çelik ve alüminyum kesimi için idealdir.', 'Akım: 40A\nKesim Kalınlığı: 12mm\nGüç: 380V\nAğırlık: 25kg', 'Temiz kesim\nDüşük ısı etkisi\nPortatif tasarım\nOtomatik ark başlatma', 1, 25000.00, 1, 'active', 3),
('Freze Makinesi FM-3', 'freze-makinesi-fm-3', 'Dikey freze makinesi', 'Hassas işleme operasyonları için dikey freze makinesi. Güçlü motor ve sağlam yapısı ile uzun ömürlü kullanım sağlar.', 'Masa: 1000x250mm\nKafa Hareketi: 400mm\nMotor: 3kW\nDevir: 50-2000 rpm', 'Dijital gösterge\nOtomatik ilerleme\nSoğutma sistemi\nHassas kılavuzlar', 4, 85000.00, 0, 'active', 4),
('Kaynak Makinesi KM-200', 'kaynak-makinesi-km-200', '200A inverter kaynak makinesi', 'Profesyonel kaynak işlemleri için 200 amper inverter kaynak makinesi. Elektrot ve TIG kaynak özelliği.', 'Akım: 200A\nGiriş: 380V\nÇıkış: 10-200A\nAğırlık: 15kg', 'Inverter teknoloji\nTIG/MMA kaynak\nHot start\nAnti-stick', 2, 8500.00, 0, 'active', 5),
('Taşlama Makinesi TM-200', 'taslama-makinesi-tm-200', 'Yüzey taşlama makinesi', 'Hassas yüzey işleme için yüzey taşlama makinesi. Otomatik ilerleme ve dijital ölçüm sistemi.', 'Masa: 200x500mm\nTaş Çapı: 200mm\nMotor: 2.2kW\nHassasiyet: 0.001mm', 'Otomatik ilerleme\nDijital ölçüm\nManyetik tabla\nSoğutma sistemi', 6, 65000.00, 0, 'active', 6);

-- Admin kullanıcısı (şifre: admin123)
INSERT INTO `admin_users` (`username`, `email`, `password`, `full_name`, `role`, `status`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sistem Yöneticisi', 'admin', 'active');

-- Blog makaleleri
INSERT INTO `articles` (`title`, `slug`, `excerpt`, `content`, `author`, `featured`, `status`, `published_at`) VALUES
('Endüstriyel Makinelerde Bakım İpuçları', 'endustriyel-makinelerde-bakim-ipuclari', 'Makinelerinizin uzun ömürlü olması için önemli bakım ipuçları', 'Endüstriyel makinelerin düzenli bakımı, üretim verimliliğini artırır ve maliyetleri düşürür. Bu yazımızda temel bakım ipuçlarını paylaşıyoruz...', 'GNG Makine', 1, 'published', NOW()),
('CNC Torna Seçimi Nasıl Yapılır?', 'cnc-torna-secimi-nasil-yapilir', 'İhtiyacınıza uygun CNC torna seçimi için dikkat edilmesi gereken faktörler', 'CNC torna seçimi yaparken göz önünde bulundurulması gereken teknik özellikler ve kriterler hakkında detaylı bilgi...', 'GNG Makine', 1, 'published', NOW()),
('Kaynak Güvenliği ve İş Sağlığı', 'kaynak-guvenligi-ve-is-sagligi', 'Kaynak işlemlerinde güvenlik önlemleri ve iş sağlığı kuralları', 'Kaynak işlemlerinde çalışan güvenliği için alınması gereken önlemler ve uyulması gereken kurallar...', 'GNG Makine', 0, 'published', NOW());

COMMIT;
