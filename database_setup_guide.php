<?php
require_once 'config/config.php';

echo "<h1>🗄️ GNG Makine Veritabanı Kurulum Rehberi</h1>";

// Mevcut durumu kontrol et
$db_status = [];
$tables_exist = false;

try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $tables_exist = !empty($tables);
    $db_status['connection'] = true;
    $db_status['tables'] = $tables;
} catch (PDOException $e) {
    $db_status['connection'] = false;
    $db_status['error'] = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Veritabanı Kurulum Rehberi - GNG Makine</title>
    <style>
        body { 
            font-family: 'Inter', <PERSON><PERSON>, sans-serif; 
            margin: 0; 
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #2c3e50;
            line-height: 1.6;
        }
        .container { 
            max-width: 900px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #F9B233 0%, #e67e22 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 { margin: 0; font-size: 2rem; }
        .content { padding: 30px; }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #F9B233;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .step h3 {
            color: #F9B233;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .step-number {
            background: #F9B233;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .status-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #F9B233;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .btn:hover {
            background: #e6a429;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(249, 178, 51, 0.3);
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .table-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .table-item {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            font-weight: 500;
        }
        .quick-actions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .quick-actions h3 {
            color: #F9B233;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ Veritabanı Kurulum Rehberi</h1>
            <p>GNG Makine projesi için veritabanını doğru şekilde kuralım</p>
        </div>
        
        <div class="content">
            <!-- Mevcut Durum -->
            <h2>📊 Mevcut Durum</h2>
            <?php if ($db_status['connection']): ?>
                <div class="status status-success">
                    ✅ <strong>Veritabanı bağlantısı başarılı!</strong>
                    <?php if ($tables_exist): ?>
                        <br>📋 Toplam <?php echo count($db_status['tables']); ?> tablo bulundu.
                    <?php else: ?>
                        <br>⚠️ Ancak hiç tablo yok - import gerekli.
                    <?php endif; ?>
                </div>
                
                <?php if (!empty($db_status['tables'])): ?>
                    <h4>Mevcut Tablolar:</h4>
                    <div class="table-list">
                        <?php foreach ($db_status['tables'] as $table): ?>
                            <div class="table-item"><?php echo $table; ?></div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="status status-error">
                    ❌ <strong>Veritabanı bağlantı hatası!</strong><br>
                    Hata: <?php echo htmlspecialchars($db_status['error']); ?>
                </div>
            <?php endif; ?>

            <!-- Adım Adım Rehber -->
            <h2>🚀 Kurulum Adımları</h2>
            
            <div class="step">
                <h3><span class="step-number">1</span> phpMyAdmin'e Giriş</h3>
                <p>Tarayıcınızda phpMyAdmin'i açın:</p>
                <div class="code-block">http://localhost/phpmyadmin</div>
                <a href="http://localhost/phpmyadmin" target="_blank" class="btn">phpMyAdmin'i Aç</a>
            </div>

            <div class="step">
                <h3><span class="step-number">2</span> Veritabanını Seç/Oluştur</h3>
                <p><strong>'gng_makine'</strong> veritabanını seçin. Eğer yoksa:</p>
                <ul>
                    <li>"Yeni" butonuna tıklayın</li>
                    <li>Veritabanı adı: <code>gng_makine</code></li>
                    <li>Karakter seti: <code>utf8mb4_unicode_ci</code></li>
                    <li>"Oluştur" butonuna tıklayın</li>
                </ul>
            </div>

            <div class="step">
                <h3><span class="step-number">3</span> Mevcut Tabloları Temizle</h3>
                <p>Eğer tablolar varsa, önce hepsini silin:</p>
                <ul>
                    <li>"Yapı" sekmesine gidin</li>
                    <li>"Tümünü seç" kutusunu işaretleyin</li>
                    <li>"Seçilenleri:" dropdown'dan "Bırak" seçin</li>
                    <li>Onaylayın</li>
                </ul>
                <div class="status status-warning">
                    ⚠️ <strong>Dikkat:</strong> Bu işlem tüm verileri silecektir!
                </div>
            </div>

            <div class="step">
                <h3><span class="step-number">4</span> SQL Dosyasını Import Et</h3>
                <p>Şimdi SQL dosyasını import edin:</p>
                <ul>
                    <li>"İçe Aktar" (Import) sekmesine gidin</li>
                    <li>"Dosya seç" butonuna tıklayın</li>
                    <li><code>database/gng_makine.sql</code> dosyasını seçin</li>
                    <li>"Git" butonuna tıklayın</li>
                </ul>
                <div class="status status-success">
                    ✅ Import başarılı olursa "Import başarıyla tamamlandı" mesajı görünecek.
                </div>
            </div>

            <div class="step">
                <h3><span class="step-number">5</span> Admin Kullanıcısını Oluştur</h3>
                <p>Import tamamlandıktan sonra admin kullanıcısını oluşturun:</p>
                <a href="database/create_admin.php" class="btn">Admin Kullanıcısı Oluştur</a>
            </div>

            <div class="step">
                <h3><span class="step-number">6</span> Sistemi Test Et</h3>
                <p>Her şey hazır! Şimdi sistemi test edin:</p>
                <a href="simple_login.php" class="btn">Login Testi</a>
                <a href="admin/login.php" class="btn btn-secondary">Admin Paneli</a>
                <a href="index.php" class="btn btn-secondary">Ana Sayfa</a>
            </div>

            <!-- Hızlı İşlemler -->
            <div class="quick-actions">
                <h3>⚡ Hızlı İşlemler</h3>
                <p>Sorun yaşıyorsanız bu araçları kullanın:</p>
                <a href="fix_database.php" class="btn">Veritabanı Onarım</a>
                <a href="debug_login.php" class="btn">Login Debug</a>
                <a href="test_db.php" class="btn">Veritabanı Test</a>
            </div>

            <!-- Beklenen Sonuç -->
            <h2>🎯 Beklenen Sonuç</h2>
            <div class="status status-success">
                <strong>Import başarılı olduğunda şu tablolar oluşacak:</strong>
                <div class="table-list" style="margin-top: 15px;">
                    <div class="table-item">admin_users</div>
                    <div class="table-item">articles</div>
                    <div class="table-item">categories</div>
                    <div class="table-item">contact_messages</div>
                    <div class="table-item">media</div>
                    <div class="table-item">products</div>
                    <div class="table-item">site_settings</div>
                </div>
                <p style="margin-top: 15px;">
                    <strong>Örnek veriler:</strong> 6 kategori, 6 ürün, 3 makale, 1 admin kullanıcısı ve site ayarları otomatik olarak eklenecek.
                </p>
            </div>

            <!-- Sorun Giderme -->
            <h2>🔧 Sorun Giderme</h2>
            <div class="step">
                <h4>❌ "Table already exists" hatası</h4>
                <p>Önce mevcut tabloları silin (Adım 3) sonra tekrar import edin.</p>
            </div>
            
            <div class="step">
                <h4>❌ "Unknown column 'author'" hatası</h4>
                <p>Tablolar eksik import edilmiş. Tüm tabloları silip tekrar import edin.</p>
            </div>
            
            <div class="step">
                <h4>❌ "Access denied" hatası</h4>
                <p>MySQL servisi çalışmıyor olabilir. XAMPP Control Panel'den MySQL'i başlatın.</p>
            </div>
        </div>
    </div>
</body>
</html>
