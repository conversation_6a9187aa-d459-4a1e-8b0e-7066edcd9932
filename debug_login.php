<?php
require_once 'config/config.php';

echo "<h2>Login Debug Sayfası</h2>";

// Test edilecek kullanıcı bilgileri
$test_username = 'gngadmin';
$test_password = 'GNG2024!';

echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>Test Edilecek Bilgiler:</h3>";
echo "<strong>Kullanıcı Adı:</strong> " . $test_username . "<br>";
echo "<strong>Şifre:</strong> " . $test_password . "<br>";
echo "</div>";

try {
    // 1. Veritabanı bağlantısını test et
    echo "<h3>1. Veritabanı Bağlantısı:</h3>";
    echo "<p>✅ Veritabanı bağlantısı başarılı!</p>";
    
    // 2. Admin_users tablosunu kontrol et
    echo "<h3>2. Admin Users Tablosu:</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM admin_users");
    $user_count = $stmt->fetchColumn();
    echo "<p>Toplam kullanıcı sayısı: <strong>" . $user_count . "</strong></p>";
    
    // 3. Tüm kullanıcıları listele
    echo "<h3>3. Tüm Kullanıcılar:</h3>";
    $stmt = $pdo->query("SELECT id, username, email, full_name, role, status, created_at FROM admin_users");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<p>❌ Hiç kullanıcı bulunamadı!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f2f2f2;'><th>ID</th><th>Kullanıcı Adı</th><th>E-posta</th><th>Ad Soyad</th><th>Rol</th><th>Durum</th><th>Oluşturulma</th></tr>";
        foreach ($users as $user) {
            $row_color = ($user['username'] === $test_username) ? 'background: #d4edda;' : '';
            echo "<tr style='" . $row_color . "'>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td><strong>" . htmlspecialchars($user['username']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "<td>" . htmlspecialchars($user['status']) . "</td>";
            echo "<td>" . $user['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 4. Belirli kullanıcıyı ara
    echo "<h3>4. Test Kullanıcısı Kontrolü:</h3>";
    $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE username = ?");
    $stmt->execute([$test_username]);
    $test_user = $stmt->fetch();
    
    if (!$test_user) {
        echo "<p>❌ '" . $test_username . "' kullanıcısı bulunamadı!</p>";
        echo "<p><strong>Çözüm:</strong> <a href='database/create_admin.php'>Yeni admin kullanıcısı oluştur</a></p>";
    } else {
        echo "<p>✅ Kullanıcı bulundu!</p>";
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>ID:</strong> " . $test_user['id'] . "<br>";
        echo "<strong>Kullanıcı Adı:</strong> " . $test_user['username'] . "<br>";
        echo "<strong>E-posta:</strong> " . $test_user['email'] . "<br>";
        echo "<strong>Ad Soyad:</strong> " . $test_user['full_name'] . "<br>";
        echo "<strong>Rol:</strong> " . $test_user['role'] . "<br>";
        echo "<strong>Durum:</strong> " . $test_user['status'] . "<br>";
        echo "</div>";
        
        // 5. Şifre kontrolü
        echo "<h3>5. Şifre Kontrolü:</h3>";
        if (password_verify($test_password, $test_user['password'])) {
            echo "<p>✅ Şifre doğru!</p>";
            echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>Giriş Bilgileri Doğru:</strong><br>";
            echo "Kullanıcı Adı: <code>" . $test_username . "</code><br>";
            echo "Şifre: <code>" . $test_password . "</code><br>";
            echo "<br><a href='admin/login.php' style='background: #F9B233; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Admin Paneline Git</a>";
            echo "</div>";
        } else {
            echo "<p>❌ Şifre yanlış!</p>";
            echo "<p><strong>Stored Hash:</strong> " . $test_user['password'] . "</p>";
            echo "<p><strong>Test Password:</strong> " . $test_password . "</p>";
            echo "<p><strong>Çözüm:</strong> Şifreyi sıfırlamak için <a href='database/create_admin.php'>admin oluşturma sayfasını</a> tekrar çalıştırın.</p>";
        }
        
        // 6. Status kontrolü
        if ($test_user['status'] !== 'active') {
            echo "<h3>⚠️ Uyarı:</h3>";
            echo "<p>Kullanıcı durumu 'active' değil: <strong>" . $test_user['status'] . "</strong></p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Veritabanı Hatası: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Hızlı Çözümler:</h3>";
echo "<ul>";
echo "<li><a href='database/create_admin.php'>Yeni admin kullanıcısı oluştur/güncelle</a></li>";
echo "<li><a href='admin/login.php'>Admin giriş sayfasına git</a></li>";
echo "<li><a href='test_db.php'>Veritabanı test sayfası</a></li>";
echo "</ul>";
?>

<style>
body { 
    font-family: 'Inter', Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
    color: #2c3e50;
    line-height: 1.6;
}
h2, h3 { color: #F9B233; }
table { width: 100%; margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; font-weight: 600; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
a { color: #F9B233; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
