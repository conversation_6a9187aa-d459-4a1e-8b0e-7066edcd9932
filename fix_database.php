<?php
require_once 'config/config.php';

echo "<h2>Veritabanı Düzeltme Aracı</h2>";

try {
    // 1. Mevcut tabloları kontrol et
    echo "<h3>1. Mevcut Tablolar:</h3>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>✅ " . $table . "</li>";
    }
    echo "</ul>";
    
    // 2. Articles tablosunun yapısını kontrol et
    echo "<h3>2. Articles Tablosu Yapısı:</h3>";
    try {
        $stmt = $pdo->query("DESCRIBE articles");
        $columns = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f2f2f2;'><th>Sütun</th><th>Tip</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        $has_author = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'author') {
                $has_author = true;
            }
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if (!$has_author) {
            echo "<p style='color: red;'>❌ 'author' sütunu eksik!</p>";
            
            // Author sütununu ekle
            echo "<h3>3. Author Sütunu Ekleniyor...</h3>";
            $alter_sql = "ALTER TABLE articles ADD COLUMN author VARCHAR(100) DEFAULT NULL AFTER content";
            $pdo->exec($alter_sql);
            echo "<p style='color: green;'>✅ Author sütunu başarıyla eklendi!</p>";
            
            // Tekrar kontrol et
            echo "<h4>Güncellenmiş Tablo Yapısı:</h4>";
            $stmt = $pdo->query("DESCRIBE articles");
            $columns = $stmt->fetchAll();
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f2f2f2;'><th>Sütun</th><th>Tip</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            foreach ($columns as $column) {
                $highlight = ($column['Field'] === 'author') ? 'background: #d4edda;' : '';
                echo "<tr style='" . $highlight . "'>";
                echo "<td>" . $column['Field'] . "</td>";
                echo "<td>" . $column['Type'] . "</td>";
                echo "<td>" . $column['Null'] . "</td>";
                echo "<td>" . $column['Key'] . "</td>";
                echo "<td>" . $column['Default'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: green;'>✅ 'author' sütunu mevcut!</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Articles tablosu bulunamadı: " . $e->getMessage() . "</p>";
        
        echo "<h3>3. Tüm Tabloları Yeniden Oluştur:</h3>";
        echo "<p>Veritabanını tamamen yeniden oluşturmak için aşağıdaki adımları izleyin:</p>";
        echo "<ol>";
        echo "<li><a href='http://localhost/phpmyadmin' target='_blank'>phpMyAdmin'e git</a></li>";
        echo "<li>'gng_makine' veritabanını seç</li>";
        echo "<li>Tüm tabloları sil (Drop)</li>";
        echo "<li>'database/gng_makine.sql' dosyasını import et</li>";
        echo "<li><a href='database/create_admin.php'>Admin kullanıcısını yeniden oluştur</a></li>";
        echo "</ol>";
    }
    
    // 4. Diğer kritik tabloları kontrol et
    echo "<h3>4. Diğer Tablolar Kontrolü:</h3>";
    $required_tables = ['admin_users', 'categories', 'products', 'contact_messages'];
    
    foreach ($required_tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM " . $table);
            $count = $stmt->fetchColumn();
            echo "<p>✅ " . $table . " tablosu mevcut (" . $count . " kayıt)</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ " . $table . " tablosu eksik!</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Genel Veritabanı Hatası: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Hızlı Çözümler:</h3>";
echo "<ul>";
echo "<li><a href='simple_login.php'>Login Testini Tekrar Dene</a></li>";
echo "<li><a href='admin/login.php'>Normal Admin Girişi</a></li>";
echo "<li><a href='database/create_admin.php'>Admin Kullanıcısını Yeniden Oluştur</a></li>";
echo "<li><a href='http://localhost/phpmyadmin' target='_blank'>phpMyAdmin'e Git</a></li>";
echo "</ul>";
?>

<style>
body { 
    font-family: 'Inter', Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
    color: #2c3e50;
    line-height: 1.6;
}
h2, h3, h4 { color: #F9B233; }
table { width: 100%; margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; font-weight: 600; }
a { color: #F9B233; text-decoration: none; }
a:hover { text-decoration: underline; }
ol, ul { margin: 10px 0 10px 20px; }
li { margin: 5px 0; }
</style>
