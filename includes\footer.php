<?php
/**
 * GNG Makine - Modern Footer
 * Optimize edilmiş ve profesyonel footer bileşeni
 */

// Footer için gerekli ayarları yükle
if (!defined('SITE_URL')) {
    require_once __DIR__ . '/../config/config.php';
}

// Güvenlik fonksiyonları
if (!function_exists('escape_html')) {
    function escape_html($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('generate_csrf_token')) {
    function generate_csrf_token() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}

// Sosyal medya linkleri
$social_links = [
    'facebook' => [
        'url' => 'https://facebook.com/gngmakine',
        'icon' => 'fab fa-facebook-f',
        'title' => 'Facebook\'ta Takip Edin'
    ],
    'linkedin' => [
        'url' => 'https://linkedin.com/company/gngmakine',
        'icon' => 'fab fa-linkedin-in',
        'title' => 'LinkedIn\'de Bağlantı Kurun'
    ],
    'instagram' => [
        'url' => 'https://instagram.com/gngmakine',
        'icon' => 'fab fa-instagram',
        'title' => 'Instagram\'da Takip Edin'
    ],
    'youtube' => [
        'url' => 'https://youtube.com/@gngmakine',
        'icon' => 'fab fa-youtube',
        'title' => 'YouTube Kanalımız'
    ],
    'twitter' => [
        'url' => 'https://twitter.com/gngmakine',
        'icon' => 'fab fa-twitter',
        'title' => 'Twitter\'da Takip Edin'
    ]
];

// Hızlı linkler
$quick_links = [
    ['title' => 'Ana Sayfa', 'url' => BASE_URL],
    ['title' => 'Hakkımızda', 'url' => BASE_URL . '/about.php'],
    ['title' => 'Ürünlerimiz', 'url' => BASE_URL . '/products.php'],
    ['title' => 'Hizmetlerimiz', 'url' => BASE_URL . '/services.php'],
    ['title' => 'AEM Akademi', 'url' => BASE_URL . '/academy.php'],
    ['title' => 'İletişim', 'url' => BASE_URL . '/contact.php']
];

// Ürün kategorileri
$product_categories = [
    ['title' => 'CNC Torna', 'url' => BASE_URL . '/products.php?category=cnc-torna'],
    ['title' => 'Freze Makineleri', 'url' => BASE_URL . '/products.php?category=freze'],
    ['title' => 'Kaynak Makineleri', 'url' => BASE_URL . '/products.php?category=kaynak'],
    ['title' => 'Pres Makineleri', 'url' => BASE_URL . '/products.php?category=pres'],
    ['title' => 'Kesim Makineleri', 'url' => BASE_URL . '/products.php?category=kesim'],
    ['title' => 'Taşlama Makineleri', 'url' => BASE_URL . '/products.php?category=taslama']
];

// Hizmetler & Destek
$support_links = [
    ['title' => 'Teknik Servis', 'url' => BASE_URL . '/services.php#technical'],
    ['title' => 'Yedek Parça', 'url' => BASE_URL . '/services.php#spare-parts'],
    ['title' => 'Bakım Onarım', 'url' => BASE_URL . '/services.php#maintenance'],
    ['title' => 'Belgelerimiz', 'url' => BASE_URL . '/certificates.php'],
    ['title' => 'Basında Biz', 'url' => BASE_URL . '/press.php'],
    ['title' => 'SSS', 'url' => BASE_URL . '/faq.php']
];

// Yasal linkler
$legal_links = [
    ['title' => 'Gizlilik Politikası', 'url' => BASE_URL . '/privacy-policy.php'],
    ['title' => 'Kullanım Şartları', 'url' => BASE_URL . '/terms-of-service.php'],
    ['title' => 'Çerez Politikası', 'url' => BASE_URL . '/cookie-policy.php'],
    ['title' => 'Site Haritası', 'url' => BASE_URL . '/sitemap.php']
];
?>

</main>

<!-- Modern Footer -->
<footer class="main-footer">
    <!-- Footer Top Section -->
    <div class="footer-top">
        <div class="container">
            <div class="footer-grid">
                
                <!-- Company Information -->
                <div class="footer-column footer-company">
                    <div class="footer-logo">
                        <div class="logo-icon">
                            <i class="fas fa-industry"></i>
                        </div>
                        <div class="logo-text">
                            <h3>GNG Makine</h3>
                            <span>Makine Çözümleri</span>
                        </div>
                    </div>
                    
                    <p class="footer-description">
                        <?php echo escape_html($site_settings['site_description'] ?? 'Profesyonel endüstriyel makine çözümleri ile sektörde lider konumumuzu sürdürüyoruz.'); ?>
                    </p>
                    
                    <div class="footer-contact">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-info">
                                <span class="contact-label">Adres</span>
                                <span class="contact-value">
                                    <?php echo escape_html($site_settings['company_address'] ?? 'Organize Sanayi Bölgesi, 1. Cadde No:15, İstanbul'); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-info">
                                <span class="contact-label">Telefon</span>
                                <a href="tel:<?php echo str_replace([' ', '(', ')', '-'], '', $site_settings['company_phone'] ?? '+90 ************'); ?>" 
                                   class="contact-value">
                                    <?php echo escape_html($site_settings['company_phone'] ?? '+90 ************'); ?>
                                </a>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-info">
                                <span class="contact-label">E-posta</span>
                                <a href="mailto:<?php echo escape_html($site_settings['company_email'] ?? '<EMAIL>'); ?>" 
                                   class="contact-value">
                                    <?php echo escape_html($site_settings['company_email'] ?? '<EMAIL>'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Social Media Links -->
                    <div class="footer-social">
                        <h4 class="social-title">Bizi Takip Edin</h4>
                        <div class="social-links">
                            <?php foreach ($social_links as $platform => $data): ?>
                            <a href="<?php echo $data['url']; ?>" 
                               class="social-link" 
                               title="<?php echo $data['title']; ?>" 
                               target="_blank" 
                               rel="noopener noreferrer">
                                <i class="<?php echo $data['icon']; ?>"></i>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div class="footer-column">
                    <h3 class="footer-title">Hızlı Linkler</h3>
                    <ul class="footer-links">
                        <?php foreach ($quick_links as $link): ?>
                        <li>
                            <a href="<?php echo $link['url']; ?>">
                                <i class="fas fa-chevron-right"></i>
                                <?php echo $link['title']; ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- Product Categories -->
                <div class="footer-column">
                    <h3 class="footer-title">Ürün Kategorileri</h3>
                    <ul class="footer-links">
                        <?php foreach ($product_categories as $category): ?>
                        <li>
                            <a href="<?php echo $category['url']; ?>">
                                <i class="fas fa-chevron-right"></i>
                                <?php echo $category['title']; ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- Services & Support -->
                <div class="footer-column">
                    <h3 class="footer-title">Hizmetler & Destek</h3>
                    <ul class="footer-links">
                        <?php foreach ($support_links as $link): ?>
                        <li>
                            <a href="<?php echo $link['url']; ?>">
                                <i class="fas fa-chevron-right"></i>
                                <?php echo $link['title']; ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
            </div>
        </div>
    </div>
    
    <!-- Newsletter Section -->
    <div class="footer-newsletter">
        <div class="container">
            <div class="newsletter-wrapper">
                <div class="newsletter-info">
                    <div class="newsletter-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="newsletter-content">
                        <h3>Haberlerden Haberdar Olun</h3>
                        <p>Yeni ürünler, kampanyalar ve sektörel gelişmelerden ilk siz haberdar olun.</p>
                    </div>
                </div>
                
                <form class="newsletter-form" id="newsletter-form" method="POST" action="<?php echo BASE_URL; ?>/ajax/newsletter.php">
                    <div class="form-group">
                        <input type="email" 
                               name="email" 
                               class="newsletter-input" 
                               placeholder="E-posta adresinizi girin" 
                               required>
                        <button type="submit" class="newsletter-btn">
                            <i class="fas fa-paper-plane"></i>
                            <span>Abone Ol</span>
                        </button>
                    </div>
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                </form>
            </div>
        </div>
    </div>
    
    <!-- Footer Bottom -->
    <div class="footer-bottom">
        <div class="container">
            <div class="footer-bottom-content">
                <div class="footer-copyright">
                    <p>
                        © <?php echo date('Y'); ?> 
                        <strong><?php echo escape_html($site_settings['company_name'] ?? 'GNG Makine'); ?></strong>
                        - Tüm hakları saklıdır.
                    </p>
                </div>
                
                <div class="footer-legal">
                    <ul class="legal-links">
                        <?php foreach ($legal_links as $link): ?>
                        <li>
                            <a href="<?php echo $link['url']; ?>">
                                <?php echo $link['title']; ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <div class="footer-badges">
                    <div class="badge-item" title="SSL Güvenliği">
                        <i class="fas fa-shield-alt"></i>
                        <span>SSL Güvenliği</span>
                    </div>
                    <div class="badge-item" title="ISO 9001 Sertifikalı">
                        <i class="fas fa-award"></i>
                        <span>ISO 9001</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Scroll to Top Button -->
<button class="scroll-to-top" id="scroll-to-top" aria-label="Sayfanın Başına Dön">
    <i class="fas fa-chevron-up"></i>
    <span class="scroll-text">Yukarı</span>
</button>

<!-- Cookie Notice -->
<div class="cookie-notice" id="cookie-notice">
    <div class="cookie-content">
        <div class="cookie-icon">
            <i class="fas fa-cookie-bite"></i>
        </div>
        <div class="cookie-text">
            <h4>Çerez Bildirimi</h4>
            <p>
                Bu web sitesi, size en iyi deneyimi sunabilmek için çerezleri kullanmaktadır. 
                Siteyi kullanmaya devam ederek çerez kullanımını kabul etmiş sayılırsınız.
            </p>
        </div>
        <div class="cookie-actions">
            <button class="btn btn-outline" id="cookie-settings">
                <i class="fas fa-cog"></i>
                Ayarlar
            </button>
            <button class="btn btn-primary" id="cookie-accept">
                <i class="fas fa-check"></i>
                Kabul Et
            </button>
        </div>
    </div>
</div>

<!-- WhatsApp Float Button -->
<?php if (!empty($site_settings['whatsapp_number'])): ?>
<a href="https://wa.me/<?php echo str_replace(['+', ' ', '(', ')', '-'], '', $site_settings['whatsapp_number']); ?>?text=Merhaba, GNG Makine hakkında bilgi almak istiyorum." 
   class="whatsapp-float" 
   target="_blank" 
   rel="noopener noreferrer"
   aria-label="WhatsApp ile İletişim">
    <div class="whatsapp-icon">
        <i class="fab fa-whatsapp"></i>
    </div>
    <div class="whatsapp-text">WhatsApp</div>
</a>
<?php endif; ?>

<!-- Footer Styles -->
<style>
/* Modern Footer Styles */
.main-footer {
    background: linear-gradient(135deg, var(--dark, #2C3E50) 0%, #34495e 100%);
    color: var(--white, #FFFFFF);
    margin-top: 80px;
    position: relative;
    overflow: hidden;
}

.main-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footerGrid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23footerGrid)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.footer-top {
    padding: 80px 0 60px;
    position: relative;
    z-index: 2;
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 50px;
    align-items: start;
}

.footer-company {
    max-width: 400px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background: var(--primary, #F9B233);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--white);
}

.logo-text h3 {
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 5px 0;
    color: var(--white);
}

.logo-text span {
    font-size: 14px;
    color: var(--primary, #F9B233);
    font-weight: 500;
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    margin-bottom: 30px;
    font-size: 15px;
}

.footer-contact {
    margin-bottom: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.contact-icon {
    width: 40px;
    height: 40px;
    background: rgba(249, 178, 51, 0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary, #F9B233);
    font-size: 16px;
    flex-shrink: 0;
}

.contact-info {
    flex: 1;
}

.contact-label {
    display: block;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 2px;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.contact-value {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s ease;
}

.contact-value:hover {
    color: var(--primary, #F9B233);
}

.footer-social {
    margin-top: 20px;
}

.social-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--white);
}

.social-links {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.social-link {
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link:hover {
    background: var(--primary, #F9B233);
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(249, 178, 51, 0.4);
}

.footer-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 25px;
    color: var(--white);
    position: relative;
    padding-bottom: 12px;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--primary, #F9B233);
    border-radius: 2px;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-links a i {
    font-size: 10px;
    color: var(--primary, #F9B233);
    transition: transform 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary, #F9B233);
    transform: translateX(5px);
}

.footer-links a:hover i {
    transform: translateX(3px);
}

/* Newsletter Section */
.footer-newsletter {
    background: rgba(0, 0, 0, 0.2);
    padding: 40px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 2;
}

.newsletter-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.newsletter-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.newsletter-icon {
    width: 60px;
    height: 60px;
    background: var(--primary, #F9B233);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--white);
    flex-shrink: 0;
}

.newsletter-content h3 {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: var(--white);
}

.newsletter-content p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    line-height: 1.5;
}

.newsletter-form .form-group {
    display: flex;
    gap: 10px;
    max-width: 400px;
    margin-left: auto;
}

.newsletter-input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.newsletter-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.newsletter-input:focus {
    outline: none;
    border-color: var(--primary, #F9B233);
    background: rgba(255, 255, 255, 0.15);
}

.newsletter-btn {
    padding: 15px 25px;
    background: var(--primary, #F9B233);
    border: none;
    border-radius: 50px;
    color: var(--white);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.newsletter-btn:hover {
    background: var(--primary-dark, #e6a02e);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(249, 178, 51, 0.4);
}

/* Footer Bottom */
.footer-bottom {
    padding: 30px 0;
    background: rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.footer-copyright p {
    margin: 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

.legal-links {
    display: flex;
    gap: 20px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.legal-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 13px;
    transition: color 0.3s ease;
}

.legal-links a:hover {
    color: var(--primary, #F9B233);
}

.footer-badges {
    display: flex;
    gap: 15px;
}

.badge-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.badge-item i {
    color: var(--primary, #F9B233);
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary, #F9B233);
    border: none;
    border-radius: 50%;
    color: var(--white);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    box-shadow: 0 4px 20px rgba(249, 178, 51, 0.3);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top:hover {
    background: var(--primary-dark, #e6a02e);
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(249, 178, 51, 0.5);
}

.scroll-text {
    position: absolute;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.scroll-to-top:hover .scroll-text {
    opacity: 1;
    visibility: visible;
}

/* Cookie Notice */
.cookie-notice {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--white, #FFFFFF);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    padding: 20px 0;
    z-index: 10000;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.cookie-notice.visible {
    transform: translateY(0);
}

.cookie-content {
    display: flex;
    align-items: center;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.cookie-icon {
    font-size: 24px;
    color: var(--primary, #F9B233);
    flex-shrink: 0;
}

.cookie-text {
    flex: 1;
}

.cookie-text h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    color: var(--text-dark, #2C3E50);
}

.cookie-text p {
    margin: 0;
    font-size: 14px;
    color: var(--text-light, #7F8C8D);
    line-height: 1.5;
}

.cookie-actions {
    display: flex;
    gap: 12px;
    flex-shrink: 0;
}

.btn {
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary, #F9B233);
    color: var(--white, #FFFFFF);
    border-color: var(--primary, #F9B233);
}

.btn-primary:hover {
    background: var(--primary-dark, #e6a02e);
    border-color: var(--primary-dark, #e6a02e);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(249, 178, 51, 0.3);
}

.btn-outline {
    background: transparent;
    color: var(--text-dark, #2C3E50);
    border-color: var(--text-light, #7F8C8D);
}

.btn-outline:hover {
    background: var(--text-dark, #2C3E50);
    color: var(--white, #FFFFFF);
    border-color: var(--text-dark, #2C3E50);
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    bottom: 100px;
    right: 30px;
    background: #25D366;
    color: var(--white, #FFFFFF);
    padding: 12px 20px;
    border-radius: 50px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
    transition: all 0.3s ease;
    z-index: 999;
    animation: float 3s ease-in-out infinite;
}

.whatsapp-float:hover {
    background: #128C7E;
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(37, 211, 102, 0.5);
    color: var(--white, #FFFFFF);
    text-decoration: none;
}

.whatsapp-icon {
    font-size: 20px;
}

.whatsapp-text {
    font-weight: 600;
    font-size: 14px;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .footer-grid {
        grid-template-columns: 1fr 1fr;
        gap: 40px;
    }
    
    .footer-company {
        grid-column: 1 / -1;
        max-width: none;
        margin-bottom: 20px;
    }
    
    .newsletter-wrapper {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }
    
    .newsletter-form .form-group {
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .footer-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .footer-top {
        padding: 60px 0 40px;
    }
    
    .footer-logo {
        justify-content: center;
        text-align: center;
    }
    
    .footer-contact,
    .footer-social {
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .newsletter-wrapper {
        text-align: center;
    }
    
    .newsletter-info {
        flex-direction: column;
        text-align: center;
    }
    
    .newsletter-form .form-group {
        flex-direction: column;
        max-width: 300px;
    }
    
    .newsletter-btn {
        justify-content: center;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .legal-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
    }
    
    .footer-badges {
        justify-content: center;
    }
    
    .cookie-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .cookie-actions {
        justify-content: center;
    }
    
    .whatsapp-float {
        bottom: 30px;
        right: 20px;
        padding: 10px 16px;
    }
    
    .whatsapp-text {
        display: none;
    }
    
    .scroll-to-top {
        bottom: 100px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 480px) {
    .footer-top {
        padding: 40px 0 30px;
    }
    
    .footer-newsletter {
        padding: 30px 0;
    }
    
    .newsletter-content h3 {
        font-size: 18px;
    }
    
    .newsletter-content p {
        font-size: 13px;
    }
    
    .footer-bottom {
        padding: 20px 0;
    }
    
    .footer-copyright p {
        font-size: 12px;
    }
    
    .legal-links a {
        font-size: 12px;
    }
}

/* Performance optimizations */
.footer-links a,
.social-link,
.btn {
    will-change: transform;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .scroll-to-top,
    .whatsapp-float,
    .social-link,
    .footer-links a,
    .btn {
        animation: none;
        transition: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .main-footer {
        background: #000000;
        color: #FFFFFF;
    }
    
    .footer-links a,
    .contact-value {
        color: #FFFFFF;
    }
    
    .footer-links a:hover,
    .contact-value:hover {
        color: #FFFF00;
    }
}

/* Print styles */
@media print {
    .main-footer,
    .scroll-to-top,
    .whatsapp-float,
    .cookie-notice {
        display: none;
    }
}
</style>

<!-- JavaScript for Footer Functionality -->
<script>
// Footer JavaScript Functionality
document.addEventListener('DOMContentLoaded', function() {
    
    // Scroll to Top Button
    const scrollToTopBtn = document.getElementById('scroll-to-top');
    
    function toggleScrollButton() {
        if (window.pageYOffset > 300) {
            scrollToTopBtn.classList.add('visible');
        } else {
            scrollToTopBtn.classList.remove('visible');
        }
    }
    
    // Show/hide scroll button on scroll
    window.addEventListener('scroll', throttle(toggleScrollButton, 100));
    
    // Scroll to top functionality
    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
        
        // Track scroll to top event
        if (typeof trackEvent === 'function') {
            trackEvent('click', 'scroll_to_top', 'footer');
        }
    });
    
    // Cookie Notice Functionality
    const cookieNotice = document.getElementById('cookie-notice');
    const cookieAccept = document.getElementById('cookie-accept');
    const cookieSettings = document.getElementById('cookie-settings');
    
    // Show cookie notice if not accepted
    if (!localStorage.getItem('cookiesAccepted')) {
        setTimeout(() => {
            cookieNotice.classList.add('visible');
        }, 2000);
    }
    
    // Accept cookies
    cookieAccept.addEventListener('click', function() {
        localStorage.setItem('cookiesAccepted', 'true');
        cookieNotice.classList.remove('visible');
        
        // Track cookie acceptance
        if (typeof trackEvent === 'function') {
            trackEvent('click', 'cookies_accepted', 'footer');
        }
    });
    
    // Cookie settings (placeholder for now)
    cookieSettings.addEventListener('click', function() {
        alert('Çerez ayarları sayfası yakında açılacak.');
        
        // Track cookie settings click
        if (typeof trackEvent === 'function') {
            trackEvent('click', 'cookie_settings', 'footer');
        }
    });
    
    // Newsletter Form Functionality
    const newsletterForm = document.getElementById('newsletter-form');
    
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('.newsletter-btn');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Gönderiliyor...</span>';
            submitBtn.disabled = true;
            
            try {
                const response = await fetch(this.action, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // Success state
                    submitBtn.innerHTML = '<i class="fas fa-check"></i> <span>Başarılı!</span>';
                    submitBtn.style.background = '#28a745';
                    
                    // Reset form
                    this.reset();
                    
                    // Show success message
                    showNotification('E-posta adresiniz başarıyla kaydedildi!', 'success');
                    
                    // Track newsletter subscription
                    if (typeof trackEvent === 'function') {
                        trackEvent('submit', 'newsletter_signup', 'footer');
                    }
                    
                    // Reset button after 3 seconds
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.style.background = '';
                        submitBtn.disabled = false;
                    }, 3000);
                } else {
                    throw new Error(result.message || 'Bir hata oluştu');
                }
            } catch (error) {
                // Error state
                submitBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> <span>Hata!</span>';
                submitBtn.style.background = '#dc3545';
                
                // Show error message
                showNotification(error.message || 'Bir hata oluştu, lütfen tekrar deneyin.', 'error');
                
                // Reset button after 3 seconds
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.style.background = '';
                    submitBtn.disabled = false;
                }, 3000);
            }
        });
    }
    
    // Social Media Link Tracking
    document.querySelectorAll('.social-link').forEach(link => {
        link.addEventListener('click', function() {
            const platform = this.href.includes('facebook') ? 'facebook' :
                           this.href.includes('linkedin') ? 'linkedin' :
                           this.href.includes('instagram') ? 'instagram' :
                           this.href.includes('youtube') ? 'youtube' :
                           this.href.includes('twitter') ? 'twitter' : 'unknown';
            
            if (typeof trackEvent === 'function') {
                trackEvent('click', 'social_media', platform);
            }
        });
    });
    
    // Contact Link Tracking
    document.querySelectorAll('.contact-value').forEach(link => {
        link.addEventListener('click', function() {
            const type = this.href.includes('tel:') ? 'phone' :
                        this.href.includes('mailto:') ? 'email' : 'unknown';
            
            if (typeof trackEvent === 'function') {
                trackEvent('click', 'contact', type);
            }
        });
    });
    
    // Footer Link Tracking
    document.querySelectorAll('.footer-links a').forEach(link => {
        link.addEventListener('click', function() {
            if (typeof trackEvent === 'function') {
                trackEvent('click', 'footer_link', this.textContent.trim());
            }
        });
    });
});

// Utility Functions
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    // Add notification styles if not already added
    if (!document.getElementById('notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                z-index: 10001;
                animation: slideInRight 0.3s ease;
            }
            .notification-success { border-left: 4px solid #28a745; }
            .notification-error { border-left: 4px solid #dc3545; }
            .notification-info { border-left: 4px solid #17a2b8; }
            .notification-content {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 15px 20px;
            }
            .notification-close {
                background: none;
                border: none;
                cursor: pointer;
                color: #666;
                margin-left: auto;
            }
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Initialize CSRF token for AJAX requests
window.csrfToken = '<?php echo generate_csrf_token(); ?>';

// Site configuration for JavaScript
window.siteConfig = {
    baseUrl: '<?php echo BASE_URL; ?>',
    assetsUrl: '<?php echo ASSETS_URL ?? BASE_URL . "/assets"; ?>',
    ajaxUrl: '<?php echo BASE_URL; ?>/ajax/',
    language: 'tr'
};
</script>

<!-- Page Specific Scripts -->
<?php if (isset($page_scripts) && !empty($page_scripts)): ?>
    <?php foreach ($page_scripts as $script): ?>
        <script src="<?php echo ASSETS_URL ?? BASE_URL . '/assets'; ?>/js/<?php echo escape_html($script); ?>"></script>
    <?php endforeach; ?>
<?php endif; ?>

<!-- Analytics and Tracking -->
<?php if (defined('GOOGLE_ANALYTICS_ID') && GOOGLE_ANALYTICS_ID): ?>
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo GOOGLE_ANALYTICS_ID; ?>"></script>
<script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', '<?php echo GOOGLE_ANALYTICS_ID; ?>', {
        page_title: '<?php echo escape_html($page_title ?? 'GNG Makine'); ?>',
        page_location: window.location.href
    });
</script>
<?php endif; ?>

<!-- Performance Monitoring -->
<script>
// Performance monitoring
window.addEventListener('load', function() {
    if ('performance' in window) {
        const perfData = performance.getEntriesByType('navigation')[0];
        
        // Track page load time
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_load_time', {
                'event_category': 'Performance',
                'event_label': window.location.pathname,
                'value': Math.round(perfData.loadEventEnd - perfData.loadEventStart)
            });
        }
        
        // Console log for development
        if (<?php echo DEBUG_MODE ? 'true' : 'false'; ?>) {
            console.log('Page Load Performance:', {
                'DNS Lookup': Math.round(perfData.domainLookupEnd - perfData.domainLookupStart) + 'ms',
                'TCP Connection': Math.round(perfData.connectEnd - perfData.connectStart) + 'ms',
                'Server Response': Math.round(perfData.responseEnd - perfData.responseStart) + 'ms',
                'DOM Processing': Math.round(perfData.domContentLoadedEventEnd - perfData.domLoading) + 'ms',
                'Total Load Time': Math.round(perfData.loadEventEnd - perfData.navigationStart) + 'ms'
            });
        }
    }
});
</script>

<?php if (DEBUG_MODE): ?>
<!-- Debug Information -->
<script>
    console.log('GNG Makine Website - Debug Mode Active');
    console.log('Current Page:', '<?php echo $current_page ?? 'unknown'; ?>');
    console.log('PHP Memory Usage:', '<?php echo memory_get_usage(true); ?> bytes');
    console.log('Database Queries:', '<?php echo $GLOBALS['query_count'] ?? 0; ?>');
</script>
<?php endif; ?>

</body>
</html>