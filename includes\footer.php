</main>
    
    <!-- Footer -->
    <footer class="main-footer">
        <!-- Footer Top -->
        <div class="footer-top">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-grid">
                        
                        <!-- Company Info -->
                        <div class="footer-column footer-company">
                            <div class="footer-logo">
                                <img src="<?php echo ASSETS_URL; ?>/images/logo/logo-white.png" 
                                     alt="<?php echo escape_html($site_settings['company_name'] ?? 'GNG Makine'); ?>" 
                                     class="footer-logo-img">
                                <span class="footer-logo-text">GNG Makine</span>
                            </div>
                            
                            <p class="footer-description">
                                <?php echo escape_html($site_settings['site_description'] ?? 'Profesyonel endüstriyel makine çözümleri ile sektörde lider konumumuzu sürdürüyoruz.'); ?>
                            </p>
                            
                            <div class="footer-contact-info">
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span><?php echo escape_html($site_settings['company_address'] ?? 'Gaziantep, Türkiye'); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <a href="tel:<?php echo str_replace([' ', '(', ')', '-'], '', $site_settings['company_phone'] ?? ''); ?>">
                                        <?php echo escape_html($site_settings['company_phone'] ?? ''); ?>
                                    </a>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <a href="mailto:<?php echo escape_html($site_settings['company_email'] ?? ''); ?>">
                                        <?php echo escape_html($site_settings['company_email'] ?? ''); ?>
                                    </a>
                                </div>
                            </div>
                            
                            <!-- Social Links -->
                            <div class="footer-social">
                                <h4 class="footer-social-title">Bizi Takip Edin</h4>
                                <?php echo render_social_links('footer'); ?>
                            </div>
                        </div>
                        
                        <!-- Quick Links -->
                        <div class="footer-column">
                            <h3 class="footer-column-title">Hızlı Linkler</h3>
                            <ul class="footer-links">
                                <li><a href="<?php echo BASE_URL; ?>">Ana Sayfa</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/about.php">Hakkımızda</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/products.php">Ürünlerimiz</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services.php">Hizmetlerimiz</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/academy.php">AEM Akademi</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/contact.php">İletişim</a></li>
                            </ul>
                        </div>
                        
                        <!-- Product Categories -->
                        <div class="footer-column">
                            <h3 class="footer-column-title">Ürün Kategorileri</h3>
                            <ul class="footer-links">
                                <?php
                                try {
                                    $db = Database::getInstance();
                                    $categories = $db->fetchAll("
                                        SELECT name, slug 
                                        FROM categories 
                                        WHERE status = 'active' AND parent_id IS NULL 
                                        ORDER BY sort_order ASC, name ASC 
                                        LIMIT 6
                                    ");
                                    
                                    foreach ($categories as $category):
                                ?>
                                <li>
                                    <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo escape_html($category['slug']); ?>">
                                        <?php echo escape_html($category['name']); ?>
                                    </a>
                                </li>
                                <?php 
                                    endforeach;
                                } catch (Exception $e) {
                                    // Varsayılan kategoriler
                                ?>
                                <li><a href="<?php echo BASE_URL; ?>/products.php?category=uretim">Üretim Makineleri</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/products.php?category=calisan">Çalışan Makineleri</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/products.php?category=alan">Alan Makineleri</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/products.php?category=musteri">Müşteri Odaklı</a></li>
                                <?php } ?>
                                <li><a href="<?php echo BASE_URL; ?>/products.php">Tüm Ürünler</a></li>
                            </ul>
                        </div>
                        
                        <!-- Services & Support -->
                        <div class="footer-column">
                            <h3 class="footer-column-title">Hizmetler & Destek</h3>
                            <ul class="footer-links">
                                <li><a href="<?php echo BASE_URL; ?>/services.php#technical">Teknik Servis</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services.php#spare-parts">Yedek Parça</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services.php#maintenance">Bakım Onarım</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/certificates.php">Belgelerimiz</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/press.php">Basında Biz</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/faq.php">Sık Sorulan Sorular</a></li>
                            </ul>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Newsletter Section -->
        <div class="footer-newsletter">
            <div class="container">
                <div class="newsletter-content">
                    <div class="newsletter-info">
                        <h3 class="newsletter-title">
                            <i class="fas fa-envelope"></i>
                            Haberlerden Haberdar Olun
                        </h3>
                        <p class="newsletter-description">
                            Yeni ürünler, kampanyalar ve haberlerden ilk siz haberdar olun.
                        </p>
                    </div>
                    
                    <form class="newsletter-form" id="newsletter-form" method="POST" action="<?php echo BASE_URL; ?>/ajax/newsletter.php">
                        <div class="newsletter-input-group">
                            <input type="email" 
                                   name="email" 
                                   class="newsletter-input" 
                                   placeholder="E-posta adresinizi girin" 
                                   required>
                            <button type="submit" class="newsletter-submit">
                                <i class="fas fa-paper-plane"></i>
                                Abone Ol
                            </button>
                        </div>
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Footer Bottom -->
        <div class="footer-bottom">
            <div class="container">
                <div class="footer-bottom-content">
                    <div class="footer-copyright">
                        <p>
                            © <?php echo date('Y'); ?> 
                            <strong><?php echo escape_html($site_settings['company_name'] ?? 'GNG Makine'); ?></strong>
                            - Tüm hakları saklıdır.
                        </p>
                    </div>
                    
                    <div class="footer-legal">
                        <ul class="legal-links">
                            <li><a href="<?php echo BASE_URL; ?>/privacy-policy.php">Gizlilik Politikası</a></li>
                            <li><a href="<?php echo BASE_URL; ?>/terms-of-service.php">Kullanım Şartları</a></li>
                            <li><a href="<?php echo BASE_URL; ?>/cookie-policy.php">Çerez Politikası</a></li>
                            <li><a href="<?php echo BASE_URL; ?>/sitemap.php">Site Haritası</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer-credentials">
                        <div class="credentials-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>SSL Güvenliği</span>
                        </div>
                        <div class="credentials-item">
                            <i class="fas fa-award"></i>
                            <span>ISO 9001</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Scroll to Top Button -->
    <button class="scroll-to-top" id="scroll-to-top" aria-label="Sayfanın Başına Dön">
        <i class="fas fa-chevron-up"></i>
    </button>
    
    <!-- Cookie Notice -->
    <div class="cookie-notice" id="cookie-notice">
        <div class="cookie-content">
            <div class="cookie-text">
                <p>
                    <i class="fas fa-cookie-bite"></i>
                    Bu web sitesi, size en iyi deneyimi sunabilmek için çerezleri kullanmaktadır. 
                    Siteyi kullanmaya devam ederek çerez kullanımını kabul etmiş sayılırsınız.
                </p>
            </div>
            <div class="cookie-actions">
                <button class="btn btn-secondary btn-sm" id="cookie-settings">
                    Ayarlar
                </button>
                <button class="btn btn-primary btn-sm" id="cookie-accept">
                    Kabul Et
                </button>
            </div>
        </div>
    </div>
    
    <!-- WhatsApp Float Button -->
    <?php if (!empty($site_settings['whatsapp_number'])): ?>
    <a href="https://wa.me/<?php echo str_replace(['+', ' ', '(', ')', '-'], '', $site_settings['whatsapp_number']); ?>?text=Merhaba, GNG Makine hakkında bilgi almak istiyorum." 
       class="whatsapp-float" 
       target="_blank" 
       rel="noopener"
       aria-label="WhatsApp'tan İletişim">
        <i class="fab fa-whatsapp"></i>
        <span class="whatsapp-text">WhatsApp</span>
    </a>
    <?php endif; ?>
    
    <!-- JavaScript Files -->
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/animations.js"></script>
    
    <!-- Page Specific Scripts -->
    <?php if (isset($page_scripts) && !empty($page_scripts)): ?>
        <?php foreach ($page_scripts as $script): ?>
            <script src="<?php echo ASSETS_URL; ?>/js/<?php echo escape_html($script); ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Inline Scripts -->
    <script>
        // CSRF Token for AJAX requests
        window.csrfToken = '<?php echo generate_csrf_token(); ?>';
        
        // Site Configuration
        window.siteConfig = {
            baseUrl: '<?php echo BASE_URL; ?>',
            assetsUrl: '<?php echo ASSETS_URL; ?>',
            ajaxUrl: '<?php echo BASE_URL; ?>/ajax/',
            language: 'tr'
        };
        
        // Initialize main JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize navigation
            if (typeof Navigation !== 'undefined') {
                Navigation.init();
            }
            
            // Initialize animations
            if (typeof Animations !== 'undefined') {
                Animations.init();
            }
            
            // Initialize cookie notice
            if (typeof CookieNotice !== 'undefined') {
                CookieNotice.init();
            }
            
            // Initialize newsletter form
            if (typeof Newsletter !== 'undefined') {
                Newsletter.init();
            }
            
            // Remove loading screen
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                setTimeout(() => {
                    loadingScreen.classList.add('fade-out');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 500);
                }, 1000);
            }
        });
        
        // Google Analytics Event Tracking
        function trackEvent(action, category, label, value) {
            if (typeof gtag !== 'undefined') {
                gtag('event', action, {
                    'event_category': category,
                    'event_label': label,
                    'value': value
                });
            }
        }
        
        // Track clicks on important elements
        document.addEventListener('click', function(e) {
            const target = e.target.closest('a');
            if (!target) return;
            
            // Track external links
            if (target.hostname && target.hostname !== window.location.hostname) {
                trackEvent('click', 'external_link', target.href);
            }
            
            // Track phone clicks
            if (target.href && target.href.startsWith('tel:')) {
                trackEvent('click', 'phone_call', target.href);
            }
            
            // Track email clicks
            if (target.href && target.href.startsWith('mailto:')) {
                trackEvent('click', 'email', target.href);
            }
            
            // Track social media clicks
            if (target.classList.contains('social-link')) {
                trackEvent('click', 'social_media', target.href);
            }
        });
    </script>
    
    <!-- Performance and SEO Scripts -->
    <script>
        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const lazyImages = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            lazyImages.forEach(img => imageObserver.observe(img));
        }
        
        // Preload critical resources
        const criticalResources = [
            '<?php echo ASSETS_URL; ?>/css/main.css',
            '<?php echo ASSETS_URL; ?>/js/main.js'
        ];
        
        criticalResources.forEach(url => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = url;
            link.as = url.endsWith('.css') ? 'style' : 'script';
            document.head.appendChild(link);
        });
    </script>
    
    <?php if (DEBUG_MODE): ?>
    <!-- Debug Information (only in debug mode) -->
    <script>
        console.log('GNG Makine Website - Debug Mode Active');
        console.log('Page Load Time:', performance.now() + 'ms');
        console.log('Current Page:', '<?php echo $current_page; ?>');
        console.log('PHP Memory Usage:', '<?php echo memory_get_usage(true); ?> bytes');
    </script>
    <?php endif; ?>
    
</body>
</html>