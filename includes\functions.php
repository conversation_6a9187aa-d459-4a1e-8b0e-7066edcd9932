<?php
/**
 * GNG Makine - Yardımcı Fonksiyonlar
 */

/**
 * Güvenli çıktı için HTML karakterlerini escape et
 */
function escape_html($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

/**
 * XSS koruması için input temizleme
 */
function clean_input($input) {
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return $input;
}

/**
 * CSRF token oluştur
 */
function generate_csrf_token() {
    if (empty($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * CSRF token doğrula
 */
function verify_csrf_token($token) {
    if (empty($_SESSION[CSRF_TOKEN_NAME])) {
        return false;
    }
    return hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Şifre hash'le
 */
function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Şifre doğrula
 */
function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Rastgele string oluştur
 */
function generate_random_string($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $string = '';
    for ($i = 0; $i < $length; $i++) {
        $string .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $string;
}

/**
 * Türkçe karakterli slug oluştur
 */
function create_slug($text) {
    $text = trim($text);
    $text = mb_strtolower($text, 'UTF-8');
    
    // Türkçe karakterleri değiştir
    $search = ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'İ', 'Ç', 'Ğ', 'Ö', 'Ş', 'Ü'];
    $replace = ['c', 'g', 'i', 'o', 's', 'u', 'i', 'c', 'g', 'o', 's', 'u'];
    $text = str_replace($search, $replace, $text);
    
    // Özel karakterleri temizle
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    $text = trim($text, '-');
    
    return $text;
}

/**
 * Dosya boyutunu human readable formata çevir
 */
function format_file_size($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * Tarih formatla
 */
function format_date($date, $format = 'd.m.Y H:i') {
    if (empty($date)) return '';
    
    $date_obj = new DateTime($date);
    return $date_obj->format($format);
}

/**
 * Tarih farkını hesapla (kaç gün önce)
 */
function time_ago($datetime, $full = false) {
    $now = new DateTime;
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    $diff->w = floor($diff->d / 7);
    $diff->d -= $diff->w * 7;

    $string = [
        'y' => 'yıl',
        'm' => 'ay',
        'w' => 'hafta',
        'd' => 'gün',
        'h' => 'saat',
        'i' => 'dakika',
        's' => 'saniye',
    ];
    
    foreach ($string as $k => &$v) {
        if ($diff->$k) {
            $v = $diff->$k . ' ' . $v;
        } else {
            unset($string[$k]);
        }
    }

    if (!$full) $string = array_slice($string, 0, 1);
    return $string ? implode(', ', $string) . ' önce' : 'şimdi';
}

/**
 * Email doğrula
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Telefon numarası doğrula
 */
function validate_phone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    return preg_match('/^[0-9]{10,11}$/', $phone);
}

/**
 * Redirect işlemi
 */
function redirect($url) {
    header("Location: " . $url);
    exit();
}

/**
 * JSON response gönder
 */
function send_json_response($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}

/**
 * Resim yeniden boyutlandır
 */
function resize_image($source, $destination, $width, $height, $quality = 85) {
    $info = getimagesize($source);
    if (!$info) return false;
    
    $src_width = $info[0];
    $src_height = $info[1];
    $mime = $info['mime'];
    
    // Kaynak resmi yükle
    switch ($mime) {
        case 'image/jpeg':
            $src_image = imagecreatefromjpeg($source);
            break;
        case 'image/png':
            $src_image = imagecreatefrompng($source);
            break;
        case 'image/gif':
            $src_image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    // Yeni boyutları hesapla (orantılı)
    $ratio = min($width / $src_width, $height / $src_height);
    $new_width = $src_width * $ratio;
    $new_height = $src_height * $ratio;
    
    // Yeni resim oluştur
    $dst_image = imagecreatetruecolor($new_width, $new_height);
    
    // PNG şeffaflığını koru
    if ($mime == 'image/png') {
        imagealphablending($dst_image, false);
        imagesavealpha($dst_image, true);
    }
    
    // Resmi yeniden boyutlandır
    imagecopyresampled($dst_image, $src_image, 0, 0, 0, 0, $new_width, $new_height, $src_width, $src_height);
    
    // Resmi kaydet
    switch ($mime) {
        case 'image/jpeg':
            imagejpeg($dst_image, $destination, $quality);
            break;
        case 'image/png':
            imagepng($dst_image, $destination);
            break;
        case 'image/gif':
            imagegif($dst_image, $destination);
            break;
    }
    
    // Hafızayı temizle
    imagedestroy($src_image);
    imagedestroy($dst_image);
    
    return true;
}

/**
 * Dosya yükleme işlemi
 */
function upload_file($file, $upload_dir, $allowed_types = [], $max_size = MAX_FILE_SIZE) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'Dosya seçilmedi'];
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'Dosya yükleme hatası'];
    }
    
    if ($file['size'] > $max_size) {
        return ['success' => false, 'message' => 'Dosya boyutu çok büyük'];
    }
    
    $file_info = pathinfo($file['name']);
    $extension = strtolower($file_info['extension']);
    
    if (!empty($allowed_types) && !in_array($extension, $allowed_types)) {
        return ['success' => false, 'message' => 'Geçersiz dosya tipi'];
    }
    
    // Benzersiz dosya adı oluştur
    $filename = time() . '_' . generate_random_string(8) . '.' . $extension;
    $destination = $upload_dir . '/' . $filename;
    
    // Dizini oluştur
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $destination)) {
        return [
            'success' => true, 
            'filename' => $filename,
            'path' => $destination,
            'size' => $file['size']
        ];
    } else {
        return ['success' => false, 'message' => 'Dosya kaydedilemedi'];
    }
}

/**
 * Breadcrumb oluştur
 */
function generate_breadcrumb($items) {
    $breadcrumb = '<nav class="breadcrumb">';
    $breadcrumb .= '<a href="/" class="breadcrumb-item">Ana Sayfa</a>';
    
    foreach ($items as $item) {
        if (isset($item['url'])) {
            $breadcrumb .= '<a href="' . $item['url'] . '" class="breadcrumb-item">' . $item['title'] . '</a>';
        } else {
            $breadcrumb .= '<span class="breadcrumb-item active">' . $item['title'] . '</span>';
        }
    }
    
    $breadcrumb .= '</nav>';
    return $breadcrumb;
}

/**
 * Sayfalama oluştur
 */
function generate_pagination($current_page, $total_pages, $base_url, $params = []) {
    if ($total_pages <= 1) return '';
    
    $pagination = '<div class="pagination">';
    
    // Önceki sayfa
    if ($current_page > 1) {
        $prev_params = array_merge($params, ['page' => $current_page - 1]);
        $pagination .= '<a href="' . $base_url . '?' . http_build_query($prev_params) . '" class="pagination-btn prev">Önceki</a>';
    }
    
    // Sayfa numaraları
    $start = max(1, $current_page - 2);
    $end = min($total_pages, $current_page + 2);
    
    if ($start > 1) {
        $first_params = array_merge($params, ['page' => 1]);
        $pagination .= '<a href="' . $base_url . '?' . http_build_query($first_params) . '" class="pagination-number">1</a>';
        if ($start > 2) {
            $pagination .= '<span class="pagination-dots">...</span>';
        }
    }
    
    for ($i = $start; $i <= $end; $i++) {
        if ($i == $current_page) {
            $pagination .= '<span class="pagination-number active">' . $i . '</span>';
        } else {
            $page_params = array_merge($params, ['page' => $i]);
            $pagination .= '<a href="' . $base_url . '?' . http_build_query($page_params) . '" class="pagination-number">' . $i . '</a>';
        }
    }
    
    if ($end < $total_pages) {
        if ($end < $total_pages - 1) {
            $pagination .= '<span class="pagination-dots">...</span>';
        }
        $last_params = array_merge($params, ['page' => $total_pages]);
        $pagination .= '<a href="' . $base_url . '?' . http_build_query($last_params) . '" class="pagination-number">' . $total_pages . '</a>';
    }
    
    // Sonraki sayfa
    if ($current_page < $total_pages) {
        $next_params = array_merge($params, ['page' => $current_page + 1]);
        $pagination .= '<a href="' . $base_url . '?' . http_build_query($next_params) . '" class="pagination-btn next">Sonraki</a>';
    }
    
    $pagination .= '</div>';
    return $pagination;
}

/**
 * Meta etiketleri oluştur
 */
function generate_meta_tags($title = '', $description = '', $keywords = '', $image = '') {
    $meta_title = !empty($title) ? $title : DEFAULT_META_TITLE;
    $meta_description = !empty($description) ? $description : DEFAULT_META_DESCRIPTION;
    $meta_keywords = !empty($keywords) ? $keywords : DEFAULT_META_KEYWORDS;
    $meta_image = !empty($image) ? $image : ASSETS_URL . '/images/default-og-image.jpg';
    
    $meta_tags = '';
    $meta_tags .= '<title>' . escape_html($meta_title) . '</title>' . "\n";
    $meta_tags .= '<meta name="description" content="' . escape_html($meta_description) . '">' . "\n";
    $meta_tags .= '<meta name="keywords" content="' . escape_html($meta_keywords) . '">' . "\n";
    $meta_tags .= '<meta property="og:title" content="' . escape_html($meta_title) . '">' . "\n";
    $meta_tags .= '<meta property="og:description" content="' . escape_html($meta_description) . '">' . "\n";
    $meta_tags .= '<meta property="og:image" content="' . escape_html($meta_image) . '">' . "\n";
    $meta_tags .= '<meta property="og:type" content="website">' . "\n";
    $meta_tags .= '<meta name="twitter:card" content="summary_large_image">' . "\n";
    $meta_tags .= '<meta name="twitter:title" content="' . escape_html($meta_title) . '">' . "\n";
    $meta_tags .= '<meta name="twitter:description" content="' . escape_html($meta_description) . '">' . "\n";
    $meta_tags .= '<meta name="twitter:image" content="' . escape_html($meta_image) . '">' . "\n";
    
    return $meta_tags;
}

/**
 * Log yazma fonksiyonu
 */
function write_log($message, $level = 'INFO') {
    $log_file = ROOT_PATH . 'logs/app.log';
    $log_dir = dirname($log_file);
    
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

/**
 * IP adresi al
 */
function get_client_ip() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
        return $_SERVER['REMOTE_ADDR'];
    }
    return 'unknown';
}

/**
 * User agent al
 */
function get_user_agent() {
    return $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
}
?>