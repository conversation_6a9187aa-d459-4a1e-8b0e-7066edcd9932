<?php
// includes/functions.php - Yardımcı Fonksiyonlar

/**
 * Debug için var_dump'ı güzel görüntüleme
 */
function dd($var) {
    echo '<pre style="background: #f4f4f4; padding: 10px; border: 1px solid #ddd; border-radius: 5px; margin: 10px 0;">';
    var_dump($var);
    echo '</pre>';
    die();
}

/**
 * Debug için print_r'ı güzel görüntüleme
 */
function pr($var) {
    echo '<pre style="background: #f4f4f4; padding: 10px; border: 1px solid #ddd; border-radius: 5px; margin: 10px 0;">';
    print_r($var);
    echo '</pre>';
}

/**
 * Email gönderme fonksiyonu
 */
function sendEmail($to, $subject, $message, $from = null) {
    $from = $from ?: FROM_EMAIL;
    $headers = [
        'From: ' . FROM_NAME . ' <' . $from . '>',
        'Reply-To: ' . $from,
        'Content-Type: text/html; charset=UTF-8',
        'MIME-Version: 1.0'
    ];
    
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

/**
 * Dosya yükleme fonksiyonu
 */
function uploadFile($file, $directory = 'general', $allowed_types = null) {
    if (!isset($file['error']) || $file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'error' => 'Dosya yükleme hatası'];
    }
    
    // Allowed types kontrolü
    $allowed_types = $allowed_types ?: ALLOWED_IMAGE_TYPES;
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'error' => 'Bu dosya türü desteklenmiyor'];
    }
    
    // Dosya boyutu kontrolü
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'error' => 'Dosya boyutu çok büyük'];
    }
    
    // Klasör oluştur
    $upload_dir = UPLOAD_PATH . $directory . '/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Benzersiz dosya adı oluştur
    $filename = uniqid() . '_' . time() . '.' . $file_extension;
    $filepath = $upload_dir . $filename;
    
    // Dosyayı taşı
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return [
            'success' => true,
            'filename' => $filename,
            'filepath' => $filepath,
            'url' => upload_url($directory . '/' . $filename)
        ];
    }
    
    return ['success' => false, 'error' => 'Dosya kaydedilemedi'];
}

/**
 * Resim boyutlandırma fonksiyonu
 */
function resizeImage($source, $destination, $max_width, $max_height, $quality = 80) {
    $image_info = getimagesize($source);
    if (!$image_info) {
        return false;
    }
    
    list($orig_width, $orig_height, $image_type) = $image_info;
    
    // Yeni boyutları hesapla
    $ratio = min($max_width / $orig_width, $max_height / $orig_height);
    $new_width = intval($orig_width * $ratio);
    $new_height = intval($orig_height * $ratio);
    
    // Kaynak resmi oluştur
    switch ($image_type) {
        case IMAGETYPE_JPEG:
            $source_image = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $source_image = imagecreatefrompng($source);
            break;
        case IMAGETYPE_GIF:
            $source_image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    // Yeni resim oluştur
    $new_image = imagecreatetruecolor($new_width, $new_height);
    
    // PNG ve GIF için şeffaflık koruması
    if ($image_type == IMAGETYPE_PNG || $image_type == IMAGETYPE_GIF) {
        imagealphablending($new_image, false);
        imagesavealpha($new_image, true);
        $transparent = imagecolorallocatealpha($new_image, 255, 255, 255, 127);
        imagefilledrectangle($new_image, 0, 0, $new_width, $new_height, $transparent);
    }
    
    // Resmi yeniden boyutlandır
    imagecopyresampled($new_image, $source_image, 0, 0, 0, 0, $new_width, $new_height, $orig_width, $orig_height);
    
    // Kaydet
    $result = false;
    switch ($image_type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($new_image, $destination, $quality);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($new_image, $destination, 9);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($new_image, $destination);
            break;
    }
    
    // Belleği temizle
    imagedestroy($source_image);
    imagedestroy($new_image);
    
    return $result;
}

/**
 * Sayfalama oluşturma fonksiyonu
 */
function createPagination($current_page, $total_pages, $base_url, $params = []) {
    if ($total_pages <= 1) {
        return '';
    }
    
    $html = '<nav aria-label="Sayfa navigasyonu"><ul class="pagination justify-content-center">';
    
    // Önceki sayfa
    if ($current_page > 1) {
        $prev_params = array_merge($params, ['page' => $current_page - 1]);
        $prev_url = $base_url . '?' . http_build_query($prev_params);
        $html .= '<li class="page-item"><a class="page-link" href="' . $prev_url . '">Önceki</a></li>';
    }
    
    // Sayfa numaraları
    $start = max(1, $current_page - 2);
    $end = min($total_pages, $current_page + 2);
    
    if ($start > 1) {
        $first_params = array_merge($params, ['page' => 1]);
        $first_url = $base_url . '?' . http_build_query($first_params);
        $html .= '<li class="page-item"><a class="page-link" href="' . $first_url . '">1</a></li>';
        if ($start > 2) {
            $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    for ($i = $start; $i <= $end; $i++) {
        if ($i == $current_page) {
            $html .= '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
        } else {
            $page_params = array_merge($params, ['page' => $i]);
            $page_url = $base_url . '?' . http_build_query($page_params);
            $html .= '<li class="page-item"><a class="page-link" href="' . $page_url . '">' . $i . '</a></li>';
        }
    }
    
    if ($end < $total_pages) {
        if ($end < $total_pages - 1) {
            $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        $last_params = array_merge($params, ['page' => $total_pages]);
        $last_url = $base_url . '?' . http_build_query($last_params);
        $html .= '<li class="page-item"><a class="page-link" href="' . $last_url . '">' . $total_pages . '</a></li>';
    }
    
    // Sonraki sayfa
    if ($current_page < $total_pages) {
        $next_params = array_merge($params, ['page' => $current_page + 1]);
        $next_url = $base_url . '?' . http_build_query($next_params);
        $html .= '<li class="page-item"><a class="page-link" href="' . $next_url . '">Sonraki</a></li>';
    }
    
    $html .= '</ul></nav>';
    return $html;
}

/**
 * Meta tag oluşturma fonksiyonu
 */
function generateMetaTags($title, $description, $keywords = '', $image = '', $url = '') {
    $site_name = SITE_NAME;
    $full_title = $title . ' - ' . $site_name;
    $current_url = $url ?: (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    
    $meta = '';
    $meta .= '<title>' . escape($full_title) . '</title>' . "\n";
    $meta .= '<meta name="description" content="' . escape($description) . '">' . "\n";
    
    if ($keywords) {
        $meta .= '<meta name="keywords" content="' . escape($keywords) . '">' . "\n";
    }
    
    // Open Graph tags
    $meta .= '<meta property="og:title" content="' . escape($title) . '">' . "\n";
    $meta .= '<meta property="og:description" content="' . escape($description) . '">' . "\n";
    $meta .= '<meta property="og:url" content="' . escape($current_url) . '">' . "\n";
    $meta .= '<meta property="og:site_name" content="' . escape($site_name) . '">' . "\n";
    $meta .= '<meta property="og:type" content="website">' . "\n";
    
    if ($image) {
        $meta .= '<meta property="og:image" content="' . escape($image) . '">' . "\n";
    }
    
    // Twitter Card tags
    $meta .= '<meta name="twitter:card" content="summary_large_image">' . "\n";
    $meta .= '<meta name="twitter:title" content="' . escape($title) . '">' . "\n";
    $meta .= '<meta name="twitter:description" content="' . escape($description) . '">' . "\n";
    
    if ($image) {
        $meta .= '<meta name="twitter:image" content="' . escape($image) . '">' . "\n";
    }
    
    return $meta;
}

/**
 * Form validation fonksiyonu
 */
function validateForm($data, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $rule_string) {
        $rules_array = explode('|', $rule_string);
        $value = isset($data[$field]) ? trim($data[$field]) : '';
        
        foreach ($rules_array as $rule) {
            $rule_parts = explode(':', $rule);
            $rule_name = $rule_parts[0];
            $rule_param = isset($rule_parts[1]) ? $rule_parts[1] : null;
            
            switch ($rule_name) {
                case 'required':
                    if (empty($value)) {
                        $errors[$field] = ucfirst($field) . ' alanı zorunludur';
                    }
                    break;
                    
                case 'email':
                    if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[$field] = 'Geçerli bir e-posta adresi girin';
                    }
                    break;
                    
                case 'min':
                    if (!empty($value) && strlen($value) < $rule_param) {
                        $errors[$field] = ucfirst($field) . ' en az ' . $rule_param . ' karakter olmalıdır';
                    }
                    break;
                    
                case 'max':
                    if (!empty($value) && strlen($value) > $rule_param) {
                        $errors[$field] = ucfirst($field) . ' en fazla ' . $rule_param . ' karakter olmalıdır';
                    }
                    break;
                    
                case 'numeric':
                    if (!empty($value) && !is_numeric($value)) {
                        $errors[$field] = ucfirst($field) . ' sadece sayı olmalıdır';
                    }
                    break;
            }
            
            // İlk hata bulunduğunda bu alan için durabilir
            if (isset($errors[$field])) {
                break;
            }
        }
    }
    
    return $errors;
}

/**
 * Log yazma fonksiyonu
 */
function writeLog($message, $type = 'info', $file = 'app.log') {
    $log_dir = $_SERVER['DOCUMENT_ROOT'] . '/logs/';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] [{$type}] {$message}" . PHP_EOL;
    
    file_put_contents($log_dir . $file, $log_message, FILE_APPEND | LOCK_EX);
}

/**
 * Cache fonksiyonları
 */
function setCache($key, $data, $expiration = 3600) {
    if (!CACHE_ENABLED) {
        return false;
    }
    
    $cache_dir = $_SERVER['DOCUMENT_ROOT'] . '/cache/';
    if (!is_dir($cache_dir)) {
        mkdir($cache_dir, 0755, true);
    }
    
    $cache_file = $cache_dir . md5($key) . '.cache';
    $cache_data = [
        'expiration' => time() + $expiration,
        'data' => $data
    ];
    
    return file_put_contents($cache_file, serialize($cache_data)) !== false;
}

function getCache($key) {
    if (!CACHE_ENABLED) {
        return false;
    }
    
    $cache_dir = $_SERVER['DOCUMENT_ROOT'] . '/cache/';
    $cache_file = $cache_dir . md5($key) . '.cache';
    
    if (!file_exists($cache_file)) {
        return false;
    }
    
    $cache_data = unserialize(file_get_contents($cache_file));
    
    if ($cache_data['expiration'] < time()) {
        unlink($cache_file);
        return false;
    }
    
    return $cache_data['data'];
}

function clearCache($key = null) {
    $cache_dir = $_SERVER['DOCUMENT_ROOT'] . '/cache/';
    
    if ($key) {
        $cache_file = $cache_dir . md5($key) . '.cache';
        if (file_exists($cache_file)) {
            unlink($cache_file);
        }
    } else {
        $files = glob($cache_dir . '*.cache');
        foreach ($files as $file) {
            unlink($file);
        }
    }
}

/**
 * String kısaltma fonksiyonu
 */
function truncateText($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length) . $suffix;
}

/**
 * URL'den dosya indirme
 */
function downloadFile($url, $destination) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $data = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode == 200 && $data !== false) {
        return file_put_contents($destination, $data) !== false;
    }
    
    return false;
}

/**
 * IP adresi alma
 */
function getClientIP() {
    $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * User Agent alma
 */
function getUserAgent() {
    return $_SERVER['HTTP_USER_AGENT'] ?? '';
}

/**
 * Random string oluşturma
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    
    return $randomString;
}

/**
 * Array'i CSV'ye çevirme
 */
function arrayToCSV($array, $filename = 'export.csv') {
    header('Content-Type: text/csv; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    // UTF-8 BOM ekle
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    if (!empty($array)) {
        // Header satırı
        fputcsv($output, array_keys($array[0]));
        
        // Veri satırları
        foreach ($array as $row) {
            fputcsv($output, $row);
        }
    }
    
    fclose($output);
    exit;
}

/**
 * Password hash oluşturma
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
}

/**
 * Password doğrulama
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Admin login kontrolü ve yönlendirme
 */
function requireLogin($redirect_url = '/admin/login.php') {
    if (!isLoggedIn()) {
        redirect($redirect_url);
    }
}

/**
 * Admin permission kontrolü
 */
function requirePermission($permission, $redirect_url = '/admin/') {
    requireLogin();
    
    if (!hasPermission($permission)) {
        addErrorMessage('Bu işlem için yetkiniz bulunmuyor.');
        redirect($redirect_url);
    }
}

/**
 * Form hatalarını gösterme
 */
function displayFormErrors($errors) {
    if (empty($errors)) {
        return '';
    }
    
    $html = '<div class="alert alert-danger"><ul class="mb-0">';
    foreach ($errors as $error) {
        $html .= '<li>' . escape($error) . '</li>';
    }
    $html .= '</ul></div>';
    
    return $html;
}

/**
 * Success/Error mesajlarını gösterme
 */
function displayMessages() {
    $messages = getMessages();
    $html = '';
    
    if (!empty($messages['success'])) {
        foreach ($messages['success'] as $message) {
            $html .= '<div class="alert alert-success alert-dismissible fade show" role="alert">';
            $html .= escape($message);
            $html .= '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            $html .= '</div>';
        }
    }
    
    if (!empty($messages['error'])) {
        foreach ($messages['error'] as $message) {
            $html .= '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
            $html .= escape($message);
            $html .= '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            $html .= '</div>';
        }
    }
    
    return $html;
}

/**
 * Güvenli dosya silme
 */
function deleteFile($filepath) {
    if (file_exists($filepath) && is_file($filepath)) {
        return unlink($filepath);
    }
    return false;
}

/**
 * Klasör silme (recursive)
 */
function deleteDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    $files = array_diff(scandir($dir), ['.', '..']);
    
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        if (is_dir($path)) {
            deleteDirectory($path);
        } else {
            unlink($path);
        }
    }
    
    return rmdir($dir);
}

/**
 * Müşteri bildirimi gönderme
 */
function sendCustomerNotification($email, $subject, $message, $template = 'default') {
    $email_template = getEmailTemplate($template);
    $email_content = str_replace(
        ['{{subject}}', '{{message}}', '{{site_name}}', '{{site_url}}'],
        [$subject, $message, SITE_NAME, SITE_URL],
        $email_template
    );
    
    return sendEmail($email, $subject, $email_content);
}

/**
 * Email template alma
 */
function getEmailTemplate($template_name) {
    $template_path = $_SERVER['DOCUMENT_ROOT'] . '/templates/email/' . $template_name . '.html';
    
    if (file_exists($template_path)) {
        return file_get_contents($template_path);
    }
    
    // Varsayılan template
    return '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>{{subject}}</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #f39c12;">{{site_name}}</h2>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 5px;">
                {{message}}
            </div>
            <footer style="margin-top: 20px; text-align: center; color: #666;">
                <p>{{site_name}} - <a href="{{site_url}}">{{site_url}}</a></p>
            </footer>
        </div>
    </body>
    </html>';
}
?>