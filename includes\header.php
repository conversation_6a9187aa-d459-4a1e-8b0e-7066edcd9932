<?php
/**
 * GNG Makine - Site Header
 */

// <PERSON><PERSON><PERSON> config dahil edilmemişse dahil et
if (!defined('SITE_NAME')) {
    require_once __DIR__ . '/../config/config.php';
}

// Mevcut sayfa için meta bilgilerini al
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$page_title = $page_title ?? DEFAULT_META_TITLE;
$page_description = $page_description ?? DEFAULT_META_DESCRIPTION;
$page_keywords = $page_keywords ?? DEFAULT_META_KEYWORDS;
$page_image = $page_image ?? '';

// Canonical URL oluştur
$canonical_url = SITE_URL . $_SERVER['REQUEST_URI'];
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Meta Tags -->
    <?php echo generate_meta_tags($page_title, $page_description, $page_keywords, $page_image); ?>

    <!-- Canonical URL -->
    <link rel="canonical" href="<?php echo escape_html($canonical_url); ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo ASSETS_URL; ?>/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo ASSETS_URL; ?>/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo ASSETS_URL; ?>/images/favicon-16x16.png">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "<?php echo escape_html($site_settings['company_name'] ?? 'GNG Makine'); ?>",
        "url": "<?php echo SITE_URL; ?>",
        "description": "<?php echo escape_html($site_settings['site_description'] ?? 'Profesyonel makine çözümleri'); ?>",
        "email": "<?php echo escape_html($site_settings['company_email'] ?? ''); ?>",
        "telephone": "<?php echo escape_html($site_settings['company_phone'] ?? ''); ?>",
        "address": {
            "@type": "PostalAddress",
            "addressLocality": "Gaziantep",
            "addressCountry": "TR"
        },
        "logo": "<?php echo ASSETS_URL; ?>/images/logo/logo.png",
        "sameAs": [
            "<?php echo FACEBOOK_URL; ?>",
            "<?php echo INSTAGRAM_URL; ?>",
            "<?php echo LINKEDIN_URL; ?>"
        ]
    }
    </script>

    <!-- Google Analytics -->
    <?php if (defined('GA_TRACKING_ID') && !empty(GA_TRACKING_ID)): ?>
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo GA_TRACKING_ID; ?>"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '<?php echo GA_TRACKING_ID; ?>');
    </script>
    <?php endif; ?>

    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">

    <style>
    /* ============================================================================
       HEADER & NAVIGATION CSS
       ============================================================================ */

    /* CSS Reset & Variables */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    :root {
        --primary-color: #F9B233;
        --secondary-color: #8A8C8F;
        --accent-color: #e67e22;
        --text-dark: #2c3e50;
        --text-light: #7f8c8d;
        --text-muted: #95a5a6;
        --bg-light: #f8f9fa;
        --bg-dark: #2c3e50;
        --white: #ffffff;
        --black: #000000;

        --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
        --gradient-overlay: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.6));

        --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --transition-fast: 0.15s ease-in-out;
        --transition-normal: 0.3s ease-in-out;
        --transition-slow: 0.5s ease-in-out;

        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

        --z-dropdown: 1000;
        --z-sticky: 1020;
        --z-modal: 1050;
    }

    /* Base Styles */
    body {
        font-family: var(--font-family);
        font-size: 16px;
        line-height: 1.6;
        color: var(--text-dark);
        background: var(--white);
        overflow-x: hidden;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    /* ============================================================================
       LOADING SCREEN
       ============================================================================ */

    .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--white);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
    }

    .loading-screen.fade-out {
        opacity: 0;
    }

    .loading-spinner {
        text-align: center;
    }

    .spinner {
        width: 50px;
        height: 50px;
        border: 4px solid var(--bg-light);
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* ============================================================================
       TOP BAR
       ============================================================================ */
    
    .top-bar {
        background: var(--bg-dark);
        color: var(--white);
        padding: 10px 0;
        font-size: 14px;
    }
    
    .top-bar-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .contact-info {
        display: flex;
        gap: 25px;
        flex-wrap: wrap;
    }
    
    .contact-item {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .contact-item a {
        color: var(--white);
        text-decoration: none;
        transition: color var(--transition-fast);
    }
    
    .contact-item a:hover {
        color: var(--primary-color);
    }
    
    .social-links {
        display: flex;
        gap: 15px;
    }
    
    .social-links a {
        color: var(--white);
        font-size: 16px;
        transition: color var(--transition-fast);
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all var(--transition-normal);
    }
    
    .social-links a:hover {
        color: var(--primary-color);
        background: rgba(249, 178, 51, 0.1);
        transform: translateY(-2px);
    }
    
    /* ============================================================================
       MAIN HEADER
       ============================================================================ */
    
    .main-header {
        background: var(--white);
        box-shadow: var(--shadow-md);
        position: sticky;
        top: 0;
        z-index: var(--z-sticky);
        transition: background var(--transition-normal), box-shadow var(--transition-normal);
    }
    
    .main-header.scrolled {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: var(--shadow-lg);
    }
    
    .header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 0;
        min-height: 80px;
    }
    
    /* ============================================================================
       LOGO
       ============================================================================ */
    
    .logo {
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .logo a {
        display: flex;
        align-items: center;
        gap: 15px;
        text-decoration: none;
        color: var(--text-dark);
        transition: transform var(--transition-normal);
    }
    
    .logo a:hover {
        transform: scale(1.05);
    }
    
    .logo-img {
        height: 50px;
        width: auto;
        transition: transform var(--transition-normal);
    }
    
    .logo-text {
        font-size: 24px;
        font-weight: 700;
        color: var(--primary-color);
        letter-spacing: -0.5px;
    }
    
    /* ============================================================================
       MAIN NAVIGATION
       ============================================================================ */
    
    .main-nav {
        display: none;
    }
    
    .nav-menu {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 30px;
    }
    
    .nav-item {
        position: relative;
    }
    
    .nav-link {
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 12px 0;
        font-weight: 500;
        color: var(--text-dark);
        text-decoration: none;
        transition: color var(--transition-fast);
        position: relative;
    }
    
    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 3px;
        background: var(--gradient-primary);
        transition: width var(--transition-normal);
        border-radius: 2px;
    }
    
    .nav-link:hover::after,
    .nav-link.active::after {
        width: 100%;
    }
    
    .nav-link:hover,
    .nav-link.active {
        color: var(--primary-color);
    }
    
    .dropdown-icon {
        font-size: 12px;
        transition: transform var(--transition-normal);
    }
    
    .nav-item:hover .dropdown-icon {
        transform: rotate(180deg);
    }
    
    /* ============================================================================
       DROPDOWN MENU
       ============================================================================ */
    
    .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        background: var(--white);
        box-shadow: var(--shadow-xl);
        border-radius: 12px;
        padding: 20px 0;
        min-width: 250px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-20px);
        transition: all var(--transition-normal);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .nav-item:hover .dropdown-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
    
    .dropdown-link {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 25px;
        color: var(--text-dark);
        text-decoration: none;
        transition: all var(--transition-fast);
        font-size: 15px;
    }
    
    .dropdown-link:hover {
        background: linear-gradient(90deg, var(--primary-color), transparent);
        color: var(--white);
        padding-left: 30px;
    }
    
    .dropdown-link i {
        width: 18px;
        text-align: center;
        opacity: 0.7;
    }
    
    .dropdown-divider {
        margin: 15px 0;
        border: none;
        border-top: 1px solid var(--bg-light);
    }
    
    /* ============================================================================
       HEADER ACTIONS
       ============================================================================ */
    
    .header-actions {
        display: flex;
        align-items: center;
        gap: 20px;
    }
    
    .action-btn {
        background: none;
        border: none;
        color: var(--text-dark);
        font-size: 18px;
        cursor: pointer;
        padding: 12px;
        border-radius: 50%;
        transition: all var(--transition-normal);
        position: relative;
    }
    
    .action-btn:hover {
        background: var(--bg-light);
        color: var(--primary-color);
        transform: scale(1.1);
    }
    
    .action-btn:active {
        transform: scale(0.95);
    }
    
    /* ============================================================================
       BUTTONS
       ============================================================================ */
    
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 12px 24px;
        font-size: 16px;
        font-weight: 500;
        text-decoration: none;
        border: 2px solid transparent;
        border-radius: 8px;
        cursor: pointer;
        transition: all var(--transition-normal);
        white-space: nowrap;
        text-align: center;
    }
    
    .btn-primary {
        color: var(--white);
        background: var(--gradient-primary);
        border-color: var(--primary-color);
        box-shadow: 0 4px 12px rgba(249, 178, 51, 0.3);
    }
    
    .btn-primary:hover {
        background: var(--accent-color);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(249, 178, 51, 0.4);
    }
    
    .btn-outline {
        color: var(--primary-color);
        background: transparent;
        border-color: var(--primary-color);
    }
    
    .btn-outline:hover {
        color: var(--white);
        background: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(249, 178, 51, 0.3);
    }
    
    .btn i {
        font-size: 14px;
    }
    
    /* ============================================================================
       MOBILE MENU TOGGLE
       ============================================================================ */
    
    .mobile-menu-toggle {
        display: flex;
        flex-direction: column;
        gap: 4px;
        background: none;
        border: none;
        cursor: pointer;
        padding: 8px;
        border-radius: 6px;
        transition: background var(--transition-normal);
    }
    
    .mobile-menu-toggle:hover {
        background: var(--bg-light);
    }
    
    .hamburger-line {
        width: 25px;
        height: 3px;
        background: var(--text-dark);
        transition: all var(--transition-normal);
        border-radius: 2px;
    }
    
    .mobile-menu-toggle.active .hamburger-line:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }
    
    .mobile-menu-toggle.active .hamburger-line:nth-child(2) {
        opacity: 0;
    }
    
    .mobile-menu-toggle.active .hamburger-line:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    /* ============================================================================
       SEARCH OVERLAY
       ============================================================================ */
    
    .search-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(10px);
        z-index: var(--z-modal);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }
    
    .search-overlay.active {
        opacity: 1;
        visibility: visible;
    }
    
    .search-container {
        position: relative;
        width: 90%;
        max-width: 600px;
    }
    
    .search-form {
        position: relative;
    }
    
    .search-input {
        width: 100%;
        padding: 20px 60px 20px 20px;
        font-size: 24px;
        border: none;
        border-radius: 15px;
        background: var(--white);
        color: var(--text-dark);
        box-shadow: var(--shadow-xl);
    }
    
    .search-input:focus {
        outline: none;
        box-shadow: 0 0 0 4px rgba(249, 178, 51, 0.3);
    }
    
    .search-submit {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: var(--gradient-primary);
        border: none;
        width: 50px;
        height: 50px;
        border-radius: 10px;
        color: var(--white);
        font-size: 18px;
        cursor: pointer;
        transition: all var(--transition-normal);
    }
    
    .search-submit:hover {
        transform: translateY(-50%) scale(1.1);
    }
    
    .search-close {
        position: absolute;
        top: -60px;
        right: 0;
        background: none;
        border: none;
        color: var(--white);
        font-size: 30px;
        cursor: pointer;
        padding: 10px;
        transition: all var(--transition-normal);
    }
    
    .search-close:hover {
        color: var(--primary-color);
        transform: scale(1.2);
    }
    
    /* ============================================================================
       MOBILE NAVIGATION
       ============================================================================ */
    
    .mobile-nav-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: var(--z-modal);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }
    
    .mobile-nav-overlay.active {
        opacity: 1;
        visibility: visible;
    }
    
    .mobile-nav {
        position: fixed;
        top: 0;
        right: -350px;
        width: 350px;
        height: 100%;
        background: var(--white);
        box-shadow: var(--shadow-xl);
        z-index: var(--z-modal);
        transition: right var(--transition-normal);
        overflow-y: auto;
    }
    
    .mobile-nav-overlay.active .mobile-nav {
        right: 0;
    }
    
    .mobile-nav-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid var(--bg-light);
    }
    
    .mobile-logo {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .mobile-logo-img {
        height: 40px;
    }
    
    .mobile-logo-text {
        font-size: 20px;
        font-weight: 700;
        color: var(--primary-color);
    }
    
    .mobile-nav-close {
        background: none;
        border: none;
        font-size: 24px;
        color: var(--text-muted);
        cursor: pointer;
        padding: 8px;
        border-radius: 6px;
        transition: all var(--transition-normal);
    }
    
    .mobile-nav-close:hover {
        background: var(--bg-light);
        color: var(--text-dark);
    }
    
    .mobile-nav-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .mobile-nav-item {
        border-bottom: 1px solid var(--bg-light);
    }
    
    .mobile-nav-link {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 18px 20px;
        color: var(--text-dark);
        text-decoration: none;
        font-weight: 500;
        transition: all var(--transition-normal);
    }
    
    .mobile-nav-link:hover {
        background: var(--bg-light);
        color: var(--primary-color);
        padding-left: 25px;
    }
    
    .mobile-nav-arrow {
        font-size: 14px;
        transition: transform var(--transition-normal);
    }
    
    .mobile-nav-item.has-children.active .mobile-nav-arrow {
        transform: rotate(90deg);
    }
    
    .mobile-submenu {
        list-style: none;
        padding: 0;
        margin: 0;
        background: var(--bg-light);
        max-height: 0;
        overflow: hidden;
        transition: max-height var(--transition-normal);
    }
    
    .mobile-nav-item.has-children.active .mobile-submenu {
        max-height: 300px;
    }
    
    .mobile-submenu-link {
        display: block;
        padding: 15px 40px;
        color: var(--text-light);
        text-decoration: none;
        font-size: 14px;
        transition: all var(--transition-normal);
    }
    
    .mobile-submenu-link:hover {
        background: var(--white);
        color: var(--primary-color);
        padding-left: 45px;
    }
    
    .mobile-nav-footer {
        padding: 30px 20px;
        border-top: 1px solid var(--bg-light);
        margin-top: 20px;
    }
    
    .mobile-contact-info {
        margin-bottom: 20px;
    }
    
    .mobile-contact-item {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
        color: var(--text-dark);
        text-decoration: none;
        font-size: 14px;
        transition: color var(--transition-fast);
    }
    
    .mobile-contact-item:hover {
        color: var(--primary-color);
    }
    
    .mobile-contact-item i {
        width: 20px;
        text-align: center;
        color: var(--primary-color);
    }
    
    .mobile-social-links {
        display: flex;
        gap: 15px;
        justify-content: center;
    }
    
    .mobile-social-links a {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: var(--bg-light);
        color: var(--text-muted);
        border-radius: 10px;
        text-decoration: none;
        transition: all var(--transition-normal);
    }
    
    .mobile-social-links a:hover {
        background: var(--primary-color);
        color: var(--white);
        transform: translateY(-3px);
    }
    
    /* ============================================================================
       RESPONSIVE DESIGN
       ============================================================================ */
    
    @media (min-width: 769px) {
        .main-nav {
            display: block;
        }
        
        .mobile-menu-toggle {
            display: none;
        }
        
        .action-btn.search-toggle {
            display: block;
        }
    }
    
    @media (max-width: 768px) {
        .top-bar-content {
            justify-content: center;
            text-align: center;
        }
        
        .contact-info {
            gap: 15px;
            font-size: 13px;
        }
        
        .social-links {
            display: none;
        }
        
        .header-content {
            padding: 10px 0;
        }
        
        .logo-img {
            height: 40px;
        }
        
        .logo-text {
            font-size: 20px;
        }
        
        .action-btn.search-toggle {
            display: none;
        }
        
        .btn {
            padding: 10px 20px;
            font-size: 14px;
        }
        
        .mobile-nav {
            width: 300px;
            right: -300px;
        }
        
        .search-input {
            font-size: 18px;
            padding: 15px 50px 15px 15px;
        }
        
        .search-submit {
            width: 40px;
            height: 40px;
            right: 10px;
        }
    }
    
    @media (max-width: 480px) {
        .container {
            padding: 0 15px;
        }
        
        .top-bar {
            padding: 8px 0;
            font-size: 12px;
        }
        
        .contact-info {
            gap: 10px;
            flex-direction: column;
        }
        
        .header-content {
            gap: 15px;
        }
        
        .logo-text {
            display: none;
        }
        
        .mobile-nav {
            width: 280px;
            right: -280px;
        }
        
        .mobile-nav-link {
            padding: 15px;
            font-size: 14px;
        }
        
        .mobile-submenu-link {
            padding: 12px 30px;
            font-size: 13px;
        }
    }
    
    /* ============================================================================
       ACCESSIBILITY & HIGH CONTRAST
       ============================================================================ */
    
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
        
        .spinner {
            animation: none;
        }
    }
    
    /* Focus styles */
    .nav-link:focus,
    .btn:focus,
    .action-btn:focus,
    .mobile-menu-toggle:focus,
    .search-input:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }
    
    /* High contrast mode */
    @media (prefers-contrast: high) {
        :root {
            --primary-color: #FF6B00;
            --text-dark: #000000;
            --text-light: #333333;
            --bg-light: #F5F5F5;
        }
        
        .main-header {
            border-bottom: 2px solid #000;
        }
        
        .dropdown-menu {
            border: 2px solid #000;
        }
    }
    </style>
</head>
<body class="<?php echo $current_page; ?>-page">
    
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Yükleniyor...</p>
        </div>
    </div>
    
    <!-- Top Bar -->
    <div class="top-bar">
        <div class="container">
            <div class="top-bar-content">
                <div class="contact-info">
                    <span class="contact-item">
                        <i class="fas fa-phone"></i>
                        <a href="tel:<?php echo str_replace([' ', '(', ')', '-'], '', $site_settings['company_phone'] ?? ''); ?>">
                            <?php echo escape_html($site_settings['company_phone'] ?? ''); ?>
                        </a>
                    </span>
                    <span class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <a href="mailto:<?php echo escape_html($site_settings['company_email'] ?? ''); ?>">
                            <?php echo escape_html($site_settings['company_email'] ?? ''); ?>
                        </a>
                    </span>
                    <span class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <?php echo escape_html($site_settings['company_address'] ?? ''); ?>
                    </span>
                </div>
                
                <div class="social-links">
                    <a href="<?php echo FACEBOOK_URL; ?>" target="_blank" rel="noopener" aria-label="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="<?php echo INSTAGRAM_URL; ?>" target="_blank" rel="noopener" aria-label="Instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="<?php echo LINKEDIN_URL; ?>" target="_blank" rel="noopener" aria-label="LinkedIn">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="<?php echo TWITTER_URL; ?>" target="_blank" rel="noopener" aria-label="Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Header -->
    <header class="main-header" id="main-header">
        <div class="container">
            <div class="header-content">
                <!-- Logo -->
                <div class="logo">
                    <a href="<?php echo BASE_URL; ?>" aria-label="GNG Makine Ana Sayfa">
                        <img src="<?php echo ASSETS_URL; ?>/images/logo/logo.png" 
                             alt="<?php echo escape_html($site_settings['company_name'] ?? 'GNG Makine'); ?>" 
                             class="logo-img">
                        <span class="logo-text">GNG Makine</span>
                    </a>
                </div>
                
                <!-- Navigation -->
                <nav class="main-nav" id="main-nav">
                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>" class="nav-link <?php echo $current_page == 'index' ? 'active' : ''; ?>">
                                Ana Sayfa
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>/about.php" class="nav-link <?php echo $current_page == 'about' ? 'active' : ''; ?>">
                                Hakkımızda
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a href="<?php echo BASE_URL; ?>/products.php" class="nav-link <?php echo in_array($current_page, ['products', 'product-detail']) ? 'active' : ''; ?>">
                                Ürünlerimiz
                                <i class="fas fa-chevron-down dropdown-icon"></i>
                            </a>
                            <div class="dropdown-menu">
                                <?php
                                // Kategorileri çek
                                try {
                                    $db = Database::getInstance();
                                    if ($db->getConnection()) {
                                        $categories = $db->fetchAll("
                                            SELECT id, name, slug 
                                            FROM categories 
                                            WHERE status = 'active' AND parent_id IS NULL 
                                            ORDER BY sort_order ASC, name ASC
                                        ");
                                        
                                        foreach ($categories as $category):
                                ?>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo escape_html($category['slug']); ?>" 
                                   class="dropdown-link">
                                    <i class="fas fa-cog"></i>
                                    <?php echo escape_html($category['name']); ?>
                                </a>
                                <?php 
                                        endforeach;
                                    }
                                } catch (Exception $e) {
                                    // Hata durumunda varsayılan kategoriler
                                ?>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=uretim" class="dropdown-link">
                                    <i class="fas fa-industry"></i>
                                    Üretim Makineleri
                                </a>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=calisan" class="dropdown-link">
                                    <i class="fas fa-users"></i>
                                    Çalışan Makineleri
                                </a>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=alan" class="dropdown-link">
                                    <i class="fas fa-map"></i>
                                    Alan Makineleri
                                </a>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=musteri" class="dropdown-link">
                                    <i class="fas fa-handshake"></i>
                                    Müşteri Odaklı
                                </a>
                                <?php } ?>
                                <hr class="dropdown-divider">
                                <a href="<?php echo BASE_URL; ?>/products.php" class="dropdown-link">
                                    <i class="fas fa-th-large"></i>
                                    Tüm Ürünler
                                </a>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>/services.php" class="nav-link <?php echo $current_page == 'services' ? 'active' : ''; ?>">
                                Hizmetlerimiz
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>/academy.php" class="nav-link <?php echo $current_page == 'academy' ? 'active' : ''; ?>">
                                AEM Akademi
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a href="#" class="nav-link <?php echo in_array($current_page, ['press', 'certificates']) ? 'active' : ''; ?>">
                                Kurumsal
                                <i class="fas fa-chevron-down dropdown-icon"></i>
                            </a>
                            <div class="dropdown-menu">
                                <a href="<?php echo BASE_URL; ?>/press.php" class="dropdown-link">
                                    <i class="fas fa-newspaper"></i>
                                    Basında Biz
                                </a>
                                <a href="<?php echo BASE_URL; ?>/certificates.php" class="dropdown-link">
                                    <i class="fas fa-certificate"></i>
                                    Belgelerimiz
                                </a>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>/contact.php" class="nav-link <?php echo $current_page == 'contact' ? 'active' : ''; ?>">
                                İletişim
                            </a>
                        </li>
                    </ul>
                </nav>
                
                <!-- Header Actions -->
                <div class="header-actions">
                    <!-- Search Toggle -->
                    <button class="action-btn search-toggle" id="search-toggle" aria-label="Arama">
                        <i class="fas fa-search"></i>
                    </button>
                    
                    <!-- Contact Button -->
                    <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-primary">
                        <i class="fas fa-phone"></i>
                        İletişim
                    </a>
                    
                    <!-- Mobile Menu Toggle -->
                    <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Menü">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Search Overlay -->
        <div class="search-overlay" id="search-overlay">
            <div class="search-container">
                <form class="search-form" action="<?php echo BASE_URL; ?>/search.php" method="GET">
                    <input type="text" name="q" class="search-input" placeholder="Ürün, hizmet veya konu arayın..." 
                           value="<?php echo escape_html($_GET['q'] ?? ''); ?>" autocomplete="off">
                    <button type="submit" class="search-submit" aria-label="Ara">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
                <button class="search-close" id="search-close" aria-label="Aramayı Kapat">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </header>
    
    <!-- Mobile Navigation Overlay -->
    <div class="mobile-nav-overlay" id="mobile-nav-overlay">
        <nav class="mobile-nav">
            <div class="mobile-nav-header">
                <div class="mobile-logo">
                    <img src="<?php echo ASSETS_URL; ?>/images/logo/logo.png" alt="GNG Makine" class="mobile-logo-img">
                    <span class="mobile-logo-text">GNG Makine</span>
                </div>
                <button class="mobile-nav-close" id="mobile-nav-close" aria-label="Menüyü Kapat">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <ul class="mobile-nav-menu">
                <li class="mobile-nav-item">
                    <a href="<?php echo BASE_URL; ?>" class="mobile-nav-link">Ana Sayfa</a>
                </li>
                <li class="mobile-nav-item">
                    <a href="<?php echo BASE_URL; ?>/about.php" class="mobile-nav-link">Hakkımızda</a>
                </li>
                <li class="mobile-nav-item has-children">
                    <a href="<?php echo BASE_URL; ?>/products.php" class="mobile-nav-link">
                        Ürünlerimiz
                        <i class="fas fa-chevron-right mobile-nav-arrow"></i>
                    </a>
                    <ul class="mobile-submenu">
                        <?php
                        // Mobil menü için kategoriler
                        try {
                            if (isset($db) && $db->getConnection()) {
                                $categories = $db->fetchAll("
                                    SELECT id, name, slug 
                                    FROM categories 
                                    WHERE status = 'active' AND parent_id IS NULL 
                                    ORDER BY sort_order ASC, name ASC
                                ");
                                
                                foreach ($categories as $category):
                        ?>
                        <li>
                            <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo escape_html($category['slug']); ?>" 
                               class="mobile-submenu-link">
                                <?php echo escape_html($category['name']); ?>
                            </a>
                        </li>
                        <?php 
                                endforeach;
                            }
                        } catch (Exception $e) {
                            // Varsayılan kategoriler
                        ?>
                        <li><a href="<?php echo BASE_URL; ?>/products.php?category=uretim" class="mobile-submenu-link">Üretim Makineleri</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/products.php?category=calisan" class="mobile-submenu-link">Çalışan Makineleri</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/products.php?category=alan" class="mobile-submenu-link">Alan Makineleri</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/products.php?category=musteri" class="mobile-submenu-link">Müşteri Odaklı</a></li>
                        <?php } ?>
                        <li><a href="<?php echo BASE_URL; ?>/products.php" class="mobile-submenu-link">Tüm Ürünler</a></li>
                    </ul>
                </li>
                <li class="mobile-nav-item">
                    <a href="<?php echo BASE_URL; ?>/services.php" class="mobile-nav-link">Hizmetlerimiz</a>
                </li>
                <li class="mobile-nav-item">
                    <a href="<?php echo BASE_URL; ?>/academy.php" class="mobile-nav-link">AEM Akademi</a>
                </li>
                <li class="mobile-nav-item has-children">
                    <a href="#" class="mobile-nav-link">
                        Kurumsal
                        <i class="fas fa-chevron-right mobile-nav-arrow"></i>
                    </a>
                    <ul class="mobile-submenu">
                        <li><a href="<?php echo BASE_URL; ?>/press.php" class="mobile-submenu-link">Basında Biz</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/certificates.php" class="mobile-submenu-link">Belgelerimiz</a></li>
                    </ul>
                </li>
                <li class="mobile-nav-item">
                    <a href="<?php echo BASE_URL; ?>/contact.php" class="mobile-nav-link">İletişim</a>
                </li>
            </ul>
            
            <div class="mobile-nav-footer">
                <div class="mobile-contact-info">
                    <a href="tel:<?php echo str_replace([' ', '(', ')', '-'], '', $site_settings['company_phone'] ?? ''); ?>" class="mobile-contact-item">
                        <i class="fas fa-phone"></i>
                        <?php echo escape_html($site_settings['company_phone'] ?? ''); ?>
                    </a>
                    <a href="mailto:<?php echo escape_html($site_settings['company_email'] ?? ''); ?>" class="mobile-contact-item">
                        <i class="fas fa-envelope"></i>
                        <?php echo escape_html($site_settings['company_email'] ?? ''); ?>
                    </a>
                </div>
                
                <div class="mobile-social-links">
                    <a href="<?php echo FACEBOOK_URL; ?>" target="_blank" rel="noopener">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="<?php echo INSTAGRAM_URL; ?>" target="_blank" rel="noopener">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="<?php echo LINKEDIN_URL; ?>" target="_blank" rel="noopener">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="<?php echo TWITTER_URL; ?>" target="_blank" rel="noopener">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
            </div>
        </nav>
    </div>
    
    <!-- Main Content Wrapper -->
    <main class="main-content" id="main-content">
    
    <script>
    // Header JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        // Header scroll effect
        const header = document.getElementById('main-header');
        let lastScrollY = window.scrollY;
        
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });
        
        // Search overlay toggle
        const searchToggle = document.getElementById('search-toggle');
        const searchOverlay = document.getElementById('search-overlay');
        const searchClose = document.getElementById('search-close');
        const searchInput = document.querySelector('.search-input');
        
        if (searchToggle && searchOverlay) {
            searchToggle.addEventListener('click', () => {
                searchOverlay.classList.add('active');
                document.body.style.overflow = 'hidden';
                setTimeout(() => searchInput?.focus(), 300);
            });
            
            searchClose?.addEventListener('click', () => {
                searchOverlay.classList.remove('active');
                document.body.style.overflow = '';
            });
            
            searchOverlay.addEventListener('click', (e) => {
                if (e.target === searchOverlay) {
                    searchOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        }
        
        // Mobile menu toggle
        const mobileToggle = document.getElementById('mobile-menu-toggle');
        const mobileOverlay = document.getElementById('mobile-nav-overlay');
        const mobileClose = document.getElementById('mobile-nav-close');
        
        if (mobileToggle && mobileOverlay) {
            mobileToggle.addEventListener('click', () => {
                mobileToggle.classList.add('active');
                mobileOverlay.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
            
            mobileClose?.addEventListener('click', () => {
                mobileToggle.classList.remove('active');
                mobileOverlay.classList.remove('active');
                document.body.style.overflow = '';
            });
            
            mobileOverlay.addEventListener('click', (e) => {
                if (e.target === mobileOverlay) {
                    mobileToggle.classList.remove('active');
                    mobileOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        }
        
        // Mobile submenu toggle
        const mobileSubmenus = document.querySelectorAll('.mobile-nav-item.has-children');
        mobileSubmenus.forEach(item => {
            const link = item.querySelector('.mobile-nav-link');
            link?.addEventListener('click', (e) => {
                e.preventDefault();
                item.classList.toggle('active');
            });
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // Close search overlay
                if (searchOverlay?.classList.contains('active')) {
                    searchOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                }
                
                // Close mobile menu
                if (mobileOverlay?.classList.contains('active')) {
                    mobileToggle?.classList.remove('active');
                    mobileOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                }
            }
        });
        
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    e.preventDefault();
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Loading screen removal - Sayfa tamamen yüklendiğinde kaldır
        function removeLoadingScreen() {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.classList.add('fade-out');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 300);
            }
        }

        // Sayfa tamamen yüklendiğinde loading screen'i kaldır
        if (document.readyState === 'complete') {
            removeLoadingScreen();
        } else {
            window.addEventListener('load', removeLoadingScreen);
        }
    });
    </script>
</head>
<body class="<?php echo $current_page; ?>-page">
    
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Yükleniyor...</p>
        </div>
    </div>
    
    <!-- Top Bar -->
    <div class="top-bar">
        <div class="container">
            <div class="top-bar-content">
                <div class="contact-info">
                    <span class="contact-item">
                        <i class="fas fa-phone"></i>
                        <a href="tel:<?php echo str_replace([' ', '(', ')', '-'], '', $site_settings['company_phone'] ?? ''); ?>">
                            <?php echo escape_html($site_settings['company_phone'] ?? ''); ?>
                        </a>
                    </span>
                    <span class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <a href="mailto:<?php echo escape_html($site_settings['company_email'] ?? ''); ?>">
                            <?php echo escape_html($site_settings['company_email'] ?? ''); ?>
                        </a>
                    </span>
                    <span class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <?php echo escape_html($site_settings['company_address'] ?? ''); ?>
                    </span>
                </div>
                
                <div class="social-links">
                    <a href="<?php echo FACEBOOK_URL; ?>" target="_blank" rel="noopener" aria-label="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="<?php echo INSTAGRAM_URL; ?>" target="_blank" rel="noopener" aria-label="Instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="<?php echo LINKEDIN_URL; ?>" target="_blank" rel="noopener" aria-label="LinkedIn">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="<?php echo TWITTER_URL; ?>" target="_blank" rel="noopener" aria-label="Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Header -->
    <header class="main-header" id="main-header">
        <div class="container">
            <div class="header-content">
                <!-- Logo -->
                <div class="logo">
                    <a href="<?php echo BASE_URL; ?>" aria-label="GNG Makine Ana Sayfa">
                        <img src="<?php echo ASSETS_URL; ?>/images/logo/logo.png" 
                             alt="<?php echo escape_html($site_settings['company_name'] ?? 'GNG Makine'); ?>" 
                             class="logo-img">
                        <span class="logo-text">GNG Makine</span>
                    </a>
                </div>
                
                <!-- Navigation -->
                <nav class="main-nav" id="main-nav">
                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>" class="nav-link <?php echo $current_page == 'index' ? 'active' : ''; ?>">
                                Ana Sayfa
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>/about.php" class="nav-link <?php echo $current_page == 'about' ? 'active' : ''; ?>">
                                Hakkımızda
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a href="<?php echo BASE_URL; ?>/products.php" class="nav-link <?php echo in_array($current_page, ['products', 'product-detail']) ? 'active' : ''; ?>">
                                Ürünlerimiz
                                <i class="fas fa-chevron-down dropdown-icon"></i>
                            </a>
                            <div class="dropdown-menu">
                                <?php
                                // Kategorileri çek
                                try {
                                    $db = Database::getInstance();
                                    $categories = $db->fetchAll("
                                        SELECT id, name, slug 
                                        FROM categories 
                                        WHERE status = 'active' AND parent_id IS NULL 
                                        ORDER BY sort_order ASC, name ASC
                                    ");
                                    
                                    foreach ($categories as $category):
                                ?>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo escape_html($category['slug']); ?>" 
                                   class="dropdown-link">
                                    <?php echo escape_html($category['name']); ?>
                                </a>
                                <?php 
                                    endforeach;
                                } catch (Exception $e) {
                                    // Hata durumunda varsayılan kategoriler
                                ?>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=uretim" class="dropdown-link">Üretim Makineleri</a>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=calisan" class="dropdown-link">Çalışan Makineleri</a>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=alan" class="dropdown-link">Alan Makineleri</a>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=musteri" class="dropdown-link">Müşteri Odaklı</a>
                                <?php } ?>
                                <hr class="dropdown-divider">
                                <a href="<?php echo BASE_URL; ?>/products.php" class="dropdown-link">
                                    <i class="fas fa-th-large"></i>
                                    Tüm Ürünler
                                </a>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>/services.php" class="nav-link <?php echo $current_page == 'services' ? 'active' : ''; ?>">
                                Hizmetlerimiz
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>/academy.php" class="nav-link <?php echo $current_page == 'academy' ? 'active' : ''; ?>">
                                AEM Akademi
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a href="#" class="nav-link <?php echo in_array($current_page, ['press', 'certificates']) ? 'active' : ''; ?>">
                                Kurumsal
                                <i class="fas fa-chevron-down dropdown-icon"></i>
                            </a>
                            <div class="dropdown-menu">
                                <a href="<?php echo BASE_URL; ?>/press.php" class="dropdown-link">
                                    <i class="fas fa-newspaper"></i>
                                    Basında Biz
                                </a>
                                <a href="<?php echo BASE_URL; ?>/certificates.php" class="dropdown-link">
                                    <i class="fas fa-certificate"></i>
                                    Belgelerimiz
                                </a>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>/contact.php" class="nav-link <?php echo $current_page == 'contact' ? 'active' : ''; ?>">
                                İletişim
                            </a>
                        </li>
                    </ul>
                </nav>
                
                <!-- Header Actions -->
                <div class="header-actions">
                    <!-- Search Toggle -->
                    <button class="action-btn search-toggle" id="search-toggle" aria-label="Arama">
                        <i class="fas fa-search"></i>
                    </button>
                    
                    <!-- Contact Button -->
                    <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-primary">
                        <i class="fas fa-phone"></i>
                        İletişim
                    </a>
                    
                    <!-- Mobile Menu Toggle -->
                    <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Menü">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Search Overlay -->
        <div class="search-overlay" id="search-overlay">
            <div class="search-container">
                <form class="search-form" action="<?php echo BASE_URL; ?>/search.php" method="GET">
                    <input type="text" name="q" class="search-input" placeholder="Ürün, hizmet veya konu arayın..." 
                           value="<?php echo escape_html($_GET['q'] ?? ''); ?>" autocomplete="off">
                    <button type="submit" class="search-submit" aria-label="Ara">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
                <button class="search-close" id="search-close" aria-label="Aramayı Kapat">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </header>
    
    <!-- Mobile Navigation Overlay -->
    <div class="mobile-nav-overlay" id="mobile-nav-overlay">
        <nav class="mobile-nav">
            <div class="mobile-nav-header">
                <div class="mobile-logo">
                    <img src="<?php echo ASSETS_URL; ?>/images/logo/logo.png" alt="GNG Makine" class="mobile-logo-img">
                    <span class="mobile-logo-text">GNG Makine</span>
                </div>
                <button class="mobile-nav-close" id="mobile-nav-close" aria-label="Menüyü Kapat">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <ul class="mobile-nav-menu">
                <li class="mobile-nav-item">
                    <a href="<?php echo BASE_URL; ?>" class="mobile-nav-link">Ana Sayfa</a>
                </li>
                <li class="mobile-nav-item">
                    <a href="<?php echo BASE_URL; ?>/about.php" class="mobile-nav-link">Hakkımızda</a>
                </li>
                <li class="mobile-nav-item has-children">
                    <a href="<?php echo BASE_URL; ?>/products.php" class="mobile-nav-link">
                        Ürünlerimiz
                        <i class="fas fa-chevron-right mobile-nav-arrow"></i>
                    </a>
                    <ul class="mobile-submenu">
                        <?php
                        // Mobil menü için kategoriler
                        try {
                            $categories = $db->fetchAll("
                                SELECT id, name, slug 
                                FROM categories 
                                WHERE status = 'active' AND parent_id IS NULL 
                                ORDER BY sort_order ASC, name ASC
                            ");
                            
                            foreach ($categories as $category):
                        ?>
                        <li>
                            <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo escape_html($category['slug']); ?>" 
                               class="mobile-submenu-link">
                                <?php echo escape_html($category['name']); ?>
                            </a>
                        </li>
                        <?php 
                            endforeach;
                        } catch (Exception $e) {
                            // Varsayılan kategoriler
                        ?>
                        <li><a href="<?php echo BASE_URL; ?>/products.php?category=uretim" class="mobile-submenu-link">Üretim Makineleri</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/products.php?category=calisan" class="mobile-submenu-link">Çalışan Makineleri</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/products.php?category=alan" class="mobile-submenu-link">Alan Makineleri</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/products.php?category=musteri" class="mobile-submenu-link">Müşteri Odaklı</a></li>
                        <?php } ?>
                        <li><a href="<?php echo BASE_URL; ?>/products.php" class="mobile-submenu-link">Tüm Ürünler</a></li>
                    </ul>
                </li>
                <li class="mobile-nav-item">
                    <a href="<?php echo BASE_URL; ?>/services.php" class="mobile-nav-link">Hizmetlerimiz</a>
                </li>
                <li class="mobile-nav-item">
                    <a href="<?php echo BASE_URL; ?>/academy.php" class="mobile-nav-link">AEM Akademi</a>
                </li>
                <li class="mobile-nav-item has-children">
                    <a href="#" class="mobile-nav-link">
                        Kurumsal
                        <i class="fas fa-chevron-right mobile-nav-arrow"></i>
                    </a>
                    <ul class="mobile-submenu">
                        <li><a href="<?php echo BASE_URL; ?>/press.php" class="mobile-submenu-link">Basında Biz</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/certificates.php" class="mobile-submenu-link">Belgelerimiz</a></li>
                    </ul>
                </li>
                <li class="mobile-nav-item">
                    <a href="<?php echo BASE_URL; ?>/contact.php" class="mobile-nav-link">İletişim</a>
                </li>
            </ul>
            
            <div class="mobile-nav-footer">
                <div class="mobile-contact-info">
                    <a href="tel:<?php echo str_replace([' ', '(', ')', '-'], '', $site_settings['company_phone'] ?? ''); ?>" class="mobile-contact-item">
                        <i class="fas fa-phone"></i>
                        <?php echo escape_html($site_settings['company_phone'] ?? ''); ?>
                    </a>
                    <a href="mailto:<?php echo escape_html($site_settings['company_email'] ?? ''); ?>" class="mobile-contact-item">
                        <i class="fas fa-envelope"></i>
                        <?php echo escape_html($site_settings['company_email'] ?? ''); ?>
                    </a>
                </div>
                
                <div class="mobile-social-links">
                    <a href="<?php echo FACEBOOK_URL; ?>" target="_blank" rel="noopener">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="<?php echo INSTAGRAM_URL; ?>" target="_blank" rel="noopener">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="<?php echo LINKEDIN_URL; ?>" target="_blank" rel="noopener">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="<?php echo TWITTER_URL; ?>" target="_blank" rel="noopener">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
            </div>
        </nav>
    </div>
    
    <!-- Main Content Wrapper -->
    <main class="main-content" id="main-content">