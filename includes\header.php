<?php
// includes/header.php - Site Header
// <PERSON>u dosya her sayfanın başında include edilir

// Site ayarlarını al
if (isset($pdo)) {
    try {
        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM site_settings WHERE setting_key IN ('site_title', 'logo_path', 'company_phone', 'company_email', 'company_address')");
        $stmt->execute();
        $settings_result = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        $site_settings = array_merge([
            'site_title' => 'GNG Makine',
            'logo_path' => '/assets/images/logo/logo.png',
            'company_phone' => '+90 555 123 45 67',
            'company_email' => '<EMAIL>',
            'company_address' => 'Gaziantep, Türkiye'
        ], $settings_result);
    } catch (PDOException $e) {
        $site_settings = [
            'site_title' => 'GNG Makine',
            'logo_path' => '/assets/images/logo/logo.png',
            'company_phone' => '+90 555 123 45 67',
            'company_email' => '<EMAIL>',
            'company_address' => 'Gaziantep, Türkiye'
        ];
    }
} else {
    $site_settings = [
        'site_title' => 'GNG Makine',
        'logo_path' => '/assets/images/logo/logo.png',
        'company_phone' => '+90 555 123 45 67',
        'company_email' => '<EMAIL>',
        'company_address' => 'Gaziantep, Türkiye'
    ];
}
?>

<!-- Top Header Bar -->
<div class="top-header">
    <div class="container">
        <div class="top-header-content">
            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <a href="tel:<?php echo str_replace(' ', '', $site_settings['company_phone']); ?>">
                        <?php echo htmlspecialchars($site_settings['company_phone']); ?>
                    </a>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <a href="mailto:<?php echo htmlspecialchars($site_settings['company_email']); ?>">
                        <?php echo htmlspecialchars($site_settings['company_email']); ?>
                    </a>
                </div>
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span><?php echo htmlspecialchars($site_settings['company_address']); ?></span>
                </div>
            </div>
            
            <div class="header-actions">
                <div class="language-selector">
                    <select id="language-select">
                        <option value="tr" selected>Türkçe</option>
                        <option value="en">English</option>
                    </select>
                </div>
                
                <div class="social-links">
                    <a href="#" class="social-link" title="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="social-link" title="LinkedIn">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="#" class="social-link" title="Instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="social-link" title="YouTube">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
                
                <a href="/admin" class="admin-link" title="Admin Panel">
                    <i class="fas fa-user-cog"></i>
                    <span>Admin</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Main Header -->
<header class="main-header">
    <div class="container">
        <div class="header-content">
            <!-- Logo -->
            <div class="logo">
                <a href="/" class="logo-link">
                    <div class="logo-icon">
                        <span class="logo-text">W</span>
                    </div>
                    <div class="logo-info">
                        <h1 class="logo-title"><?php echo htmlspecialchars($site_settings['site_title']); ?></h1>
                        <span class="logo-subtitle">Makine Çözümleri</span>
                    </div>
                </a>
            </div>

            <!-- Header Actions -->
            <div class="header-right">
                <!-- Search Box -->
                <div class="search-box">
                    <form action="/products.php" method="GET" class="search-form">
                        <input type="text" name="search" placeholder="Ürün ara..." class="search-input">
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <a href="/products.php" class="action-btn" title="Tüm Ürünler">
                        <i class="fas fa-th-large"></i>
                        <span>Ürünler</span>
                    </a>
                    
                    <a href="/contact.php" class="action-btn contact-btn" title="İletişim">
                        <i class="fas fa-phone-alt"></i>
                        <span>İletişim</span>
                    </a>
                </div>

                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
            </div>
        </div>
    </div>
</header>

<style>
/* Header Styles */
.top-header {
    background: var(--secondary-color);
    color: var(--white);
    padding: 8px 0;
    font-size: 0.85rem;
    border-bottom: 2px solid var(--primary-color);
    width: 100%;
}

.top-header .container {
    max-width: 100%;
    width: 100%;
    margin: 0;
    padding: 0 20px;
}

.top-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.contact-info {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #bdc3c7;
}

.contact-item i {
    color: var(--primary-color);
    font-size: 0.9rem;
}

.contact-item a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-item a:hover {
    color: var(--primary-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.language-selector select {
    background: transparent;
    color: var(--white);
    border: 1px solid #34495e;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.social-links {
    display: flex;
    gap: 12px;
}

.social-link {
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.admin-link {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #bdc3c7;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.admin-link:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

/* Main Header */
.main-header {
    background: var(--white);
    padding: 15px 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    width: 100%;
}

.main-header .container {
    max-width: 100%;
    width: 100%;
    margin: 0;
    padding: 0 20px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 30px;
}

/* Logo */
.logo-link {
    display: flex;
    align-items: center;
    gap: 15px;
    text-decoration: none;
    color: var(--secondary-color);
}

.logo-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
    transform: rotate(45deg);
    animation: logoShine 3s ease-in-out infinite;
}

@keyframes logoShine {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.logo-text {
    font-size: 2rem;
    font-weight: 900;
    color: var(--white);
    letter-spacing: -1px;
    position: relative;
    z-index: 2;
}

.logo-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.logo-title {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--secondary-color);
    margin: 0;
    line-height: 1;
}

.logo-subtitle {
    font-size: 0.85rem;
    color: var(--primary-color);
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Header Right */
.header-right {
    display: flex;
    align-items: center;
    gap: 25px;
}

/* Search Box */
.search-box {
    position: relative;
}

.search-form {
    display: flex;
    align-items: center;
    background: var(--bg-light);
    border-radius: 25px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.search-form:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.2);
}

.search-input {
    width: 250px;
    padding: 12px 20px;
    border: none;
    background: transparent;
    font-size: 0.9rem;
    outline: none;
    color: var(--text-dark);
}

.search-input::placeholder {
    color: var(--text-light);
}

.search-btn {
    padding: 12px 15px;
    background: var(--primary-color);
    border: none;
    color: var(--white);
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: var(--accent-color);
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 15px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: 2px solid var(--bg-light);
    color: var(--text-dark);
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.2);
}

.contact-btn {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.contact-btn:hover {
    background: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--white);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.hamburger-line {
    width: 25px;
    height: 3px;
    background: var(--secondary-color);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(8px, -8px);
}

/* Responsive Design */
@media (max-width: 992px) {
    .search-input {
        width: 200px;
    }
    
    .quick-actions .action-btn span {
        display: none;
    }
    
    .quick-actions .action-btn {
        padding: 12px;
        border-radius: 50%;
        width: 45px;
        height: 45px;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .top-header {
        padding: 10px 0;
    }
    
    .top-header-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .contact-info {
        justify-content: center;
        gap: 20px;
    }
    
    .header-actions {
        gap: 15px;
    }
    
    .main-header {
        padding: 10px 0;
    }
    
    .header-content {
        gap: 15px;
    }
    
    .logo-info .logo-title {
        font-size: 1.4rem;
    }
    
    .logo-icon {
        width: 45px;
        height: 45px;
    }
    
    .logo-text {
        font-size: 1.5rem;
    }
    
    .search-box {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .quick-actions {
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .contact-info {
        flex-direction: column;
        gap: 8px;
    }
    
    .header-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .social-links {
        gap: 8px;
    }
    
    .logo-info .logo-subtitle {
        font-size: 0.7rem;
    }
    
    .action-btn {
        padding: 10px;
        width: 40px;
        height: 40px;
    }
}
</style>

<script>
// Header JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileToggle = document.getElementById('mobileMenuToggle');
    if (mobileToggle) {
        mobileToggle.addEventListener('click', function() {
            this.classList.toggle('active');
            // Bu navbar.php'de mobile menu ile entegre edilecek
        });
    }

    // Search functionality
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            const searchInput = this.querySelector('.search-input');
            if (searchInput.value.trim() === '') {
                e.preventDefault();
                searchInput.focus();
            }
        });
    }

    // Header scroll effect
    let lastScrollTop = 0;
    const header = document.querySelector('.main-header');
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.backdropFilter = 'blur(10px)';
        } else {
            header.style.background = 'var(--white)';
            header.style.backdropFilter = 'none';
        }
        
        lastScrollTop = scrollTop;
    });

    // Language selector
    const languageSelect = document.getElementById('language-select');
    if (languageSelect) {
        languageSelect.addEventListener('change', function() {
            // Dil değiştirme fonksiyonu buraya eklenecek
            console.log('Language changed to:', this.value);
        });
    }
});
</script>