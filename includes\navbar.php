<?php
// includes/navbar.php - Navigasyon Menüsü

// Aktif sayfa kontrolü
$current_page = basename($_SERVER['PHP_SELF'], '.php');

// Menü öğeleri
$menu_items = [
    'index' => ['title' => 'Ana Sayfa', 'url' => '/', 'icon' => 'fas fa-home'],
    'about' => ['title' => 'Hakkımızda', 'url' => '/about.php', 'icon' => 'fas fa-info-circle'],
    'services' => ['title' => 'Hizmetlerimiz', 'url' => '/services.php', 'icon' => 'fas fa-tools'],
    'products' => ['title' => 'Ürünler', 'url' => '/products.php', 'icon' => 'fas fa-cogs'],
    'press' => ['title' => 'Basında Biz', 'url' => '/press.php', 'icon' => 'fas fa-newspaper'],
    'academy' => ['title' => 'AEM Akademi', 'url' => '/academy.php', 'icon' => 'fas fa-graduation-cap'],
    'certificates' => ['title' => 'Belgelerimiz', 'url' => '/certificates.php', 'icon' => 'fas fa-certificate'],
    'contact' => ['title' => 'İletişim', 'url' => '/contact.php', 'icon' => 'fas fa-phone']
];

// Kategorileri getir (ürünler dropdown için)
$categories = [];
if (isset($pdo)) {
    try {
        $stmt = $pdo->prepare("SELECT id, name, slug FROM categories WHERE status = 'active' ORDER BY sort_order, name LIMIT 10");
        $stmt->execute();
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $categories = [];
    }
}
?>

<!-- Navigation -->
<nav class="main-navbar">
    <div class="container">
        <div class="navbar-content">
            <!-- Main Menu -->
            <ul class="navbar-nav">
                <?php foreach ($menu_items as $page => $item): ?>
                    <?php if ($page === 'products'): ?>
                        <!-- Ürünler Dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo ($current_page === 'products' || $current_page === 'product-detail') ? 'active' : ''; ?>" 
                               href="<?php echo htmlspecialchars($item['url']); ?>" data-dropdown="products">
                                <i class="<?php echo $item['icon']; ?>"></i>
                                <span><?php echo htmlspecialchars($item['title']); ?></span>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </a>
                            <div class="dropdown-menu">
                                <div class="dropdown-content">
                                    <div class="dropdown-section">
                                        <h6 class="dropdown-header">
                                            <i class="fas fa-list"></i>
                                            Tüm Ürünler
                                        </h6>
                                        <a class="dropdown-item" href="<?php echo htmlspecialchars($item['url']); ?>">
                                            <i class="fas fa-th-large"></i>
                                            <span>Ürün Kataloğu</span>
                                            <small>Tüm ürünlerimizi görüntüleyin</small>
                                        </a>
                                    </div>
                                    
                                    <?php if (!empty($categories)): ?>
                                        <div class="dropdown-divider"></div>
                                        <div class="dropdown-section">
                                            <h6 class="dropdown-header">
                                                <i class="fas fa-tags"></i>
                                                Kategoriler
                                            </h6>
                                            <div class="categories-grid">
                                                <?php foreach ($categories as $category): ?>
                                                    <a class="dropdown-item category-item" 
                                                       href="/products.php?category=<?php echo urlencode($category['slug']); ?>">
                                                        <i class="fas fa-angle-right"></i>
                                                        <span><?php echo htmlspecialchars($category['name']); ?></span>
                                                    </a>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="dropdown-divider"></div>
                                    <div class="dropdown-footer">
                                        <a href="/products.php" class="dropdown-cta">
                                            <i class="fas fa-search"></i>
                                            Tüm Ürünleri Keşfet
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </li>
                    <?php else: ?>
                        <!-- Normal Menu Item -->
                        <li class="nav-item">
                            <a class="nav-link <?php echo $current_page === $page ? 'active' : ''; ?>" 
                               href="<?php echo htmlspecialchars($item['url']); ?>">
                                <i class="<?php echo $item['icon']; ?>"></i>
                                <span><?php echo htmlspecialchars($item['title']); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>

            <!-- Right Side Actions -->
            <div class="navbar-actions">
                <!-- Quick Contact -->
                <a href="tel:<?php echo str_replace(' ', '', $site_settings['company_phone'] ?? '+905551234567'); ?>" 
                   class="action-btn phone-btn" title="Hemen Arayın">
                    <i class="fas fa-phone"></i>
                    <span>Hemen Ara</span>
                </a>

                <!-- Quote Request Button -->
                <a href="/contact.php?type=quote" class="action-btn quote-btn" title="Fiyat Teklifi">
                    <i class="fas fa-calculator"></i>
                    <span>Fiyat Teklifi</span>
                </a>

                <!-- Search Toggle (Mobile) -->
                <button class="action-btn search-toggle" id="searchToggle" title="Arama">
                    <i class="fas fa-search"></i>
                </button>

                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
            </div>
        </div>

        <!-- Mobile Search Box -->
        <div class="mobile-search" id="mobileSearch">
            <form class="search-form" action="/products.php" method="GET">
                <div class="search-input-group">
                    <input type="text" name="search" placeholder="Ürün ara..." class="search-input">
                    <button type="submit" class="search-submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>

        <!-- Mobile Menu -->
        <div class="mobile-menu" id="mobileMenu">
            <div class="mobile-menu-content">
                <div class="mobile-menu-header">
                    <div class="mobile-logo">
                        <div class="logo-icon">
                            <span class="logo-text">W</span>
                        </div>
                        <span>GNG Makine</span>
                    </div>
                    <button class="mobile-menu-close" id="mobileMenuClose">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="mobile-menu-body">
                    <ul class="mobile-nav">
                        <?php foreach ($menu_items as $page => $item): ?>
                            <?php if ($page === 'products'): ?>
                                <!-- Mobile Products with Submenu -->
                                <li class="mobile-nav-item has-submenu">
                                    <a class="mobile-nav-link <?php echo ($current_page === 'products' || $current_page === 'product-detail') ? 'active' : ''; ?>" 
                                       href="<?php echo htmlspecialchars($item['url']); ?>">
                                        <i class="<?php echo $item['icon']; ?>"></i>
                                        <span><?php echo htmlspecialchars($item['title']); ?></span>
                                        <i class="fas fa-chevron-right submenu-arrow"></i>
                                    </a>
                                    <?php if (!empty($categories)): ?>
                                        <ul class="mobile-submenu">
                                            <li>
                                                <a href="<?php echo htmlspecialchars($item['url']); ?>" class="mobile-submenu-link">
                                                    <i class="fas fa-th-large"></i>
                                                    Tüm Ürünler
                                                </a>
                                            </li>
                                            <?php foreach ($categories as $category): ?>
                                                <li>
                                                    <a href="/products.php?category=<?php echo urlencode($category['slug']); ?>" 
                                                       class="mobile-submenu-link">
                                                        <i class="fas fa-angle-right"></i>
                                                        <?php echo htmlspecialchars($category['name']); ?>
                                                    </a>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php endif; ?>
                                </li>
                            <?php else: ?>
                                <!-- Normal Mobile Menu Item -->
                                <li class="mobile-nav-item">
                                    <a class="mobile-nav-link <?php echo $current_page === $page ? 'active' : ''; ?>" 
                                       href="<?php echo htmlspecialchars($item['url']); ?>">
                                        <i class="<?php echo $item['icon']; ?>"></i>
                                        <span><?php echo htmlspecialchars($item['title']); ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ul>

                    <div class="mobile-menu-footer">
                        <div class="mobile-contact">
                            <a href="tel:<?php echo str_replace(' ', '', $site_settings['company_phone'] ?? '+905551234567'); ?>" 
                               class="mobile-contact-btn">
                                <i class="fas fa-phone"></i>
                                <span><?php echo htmlspecialchars($site_settings['company_phone'] ?? '+90 555 123 45 67'); ?></span>
                            </a>
                            <a href="mailto:<?php echo htmlspecialchars($site_settings['company_email'] ?? '<EMAIL>'); ?>" 
                               class="mobile-contact-btn">
                                <i class="fas fa-envelope"></i>
                                <span><?php echo htmlspecialchars($site_settings['company_email'] ?? '<EMAIL>'); ?></span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Menu Overlay -->
        <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>
    </div>
</nav>

<!-- Breadcrumb -->
<?php if (isset($breadcrumb) && !empty($breadcrumb) && $current_page !== 'index'): ?>
<nav class="breadcrumb-nav">
    <div class="container">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/"><i class="fas fa-home"></i> Ana Sayfa</a>
            </li>
            <?php foreach ($breadcrumb as $item): ?>
                <?php if (isset($item['url'])): ?>
                    <li class="breadcrumb-item">
                        <a href="<?php echo htmlspecialchars($item['url']); ?>">
                            <?php echo htmlspecialchars($item['title']); ?>
                        </a>
                    </li>
                <?php else: ?>
                    <li class="breadcrumb-item active">
                        <?php echo htmlspecialchars($item['title']); ?>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </div>
</nav>
<?php endif; ?>

<style>
/* Navbar Styles */
.main-navbar {
    background: var(--white);
    border-bottom: 1px solid #e9ecef;
    position: relative;
    z-index: 999;
    width: 100%;
}

.main-navbar .container {
    max-width: 100%;
    width: 100%;
    margin: 0;
    padding: 0 20px;
}

.navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    min-height: 70px;
}

/* Main Navigation */
.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 10px;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 15px 20px;
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    font-size: 0.95rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background: rgba(243, 156, 18, 0.1);
}

.nav-link i {
    font-size: 1rem;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    cursor: pointer;
}

.dropdown-arrow {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.dropdown:hover .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    min-width: 350px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-content {
    padding: 20px;
}

.dropdown-section {
    margin-bottom: 20px;
}

.dropdown-section:last-child {
    margin-bottom: 0;
}

.dropdown-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dropdown-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 15px;
    text-decoration: none;
    color: var(--text-dark);
    border-radius: 8px;
    transition: all 0.3s ease;
    margin-bottom: 5px;
}

.dropdown-item:hover {
    background: var(--bg-light);
    color: var(--primary-color);
    transform: translateX(5px);
}

.dropdown-item i {
    color: var(--primary-color);
    font-size: 1rem;
    margin-top: 2px;
}

.dropdown-item span {
    font-weight: 500;
}

.dropdown-item small {
    display: block;
    color: var(--text-light);
    font-size: 0.8rem;
    margin-top: 2px;
}

.categories-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5px;
}

.category-item {
    margin-bottom: 5px;
}

.dropdown-divider {
    height: 1px;
    background: #e9ecef;
    margin: 15px 0;
}

.dropdown-footer {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.dropdown-cta {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    background: var(--primary-color);
    color: var(--white);
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.dropdown-cta:hover {
    background: var(--accent-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Navbar Actions */
.navbar-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 18px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.phone-btn {
    color: var(--text-dark);
    border-color: #e9ecef;
}

.phone-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: rgba(243, 156, 18, 0.1);
}

.quote-btn {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.quote-btn:hover {
    background: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--white);
    transform: translateY(-2px);
}

.search-toggle {
    display: none;
    background: transparent;
    border: 2px solid #e9ecef;
    color: var(--text-dark);
    width: 45px;
    height: 45px;
    border-radius: 50%;
    justify-content: center;
    cursor: pointer;
}

.search-toggle:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.hamburger-line {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.mobile-menu-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(8px, -8px);
}

/* Mobile Search */
.mobile-search {
    display: none;
    padding: 15px 0;
    border-top: 1px solid #e9ecef;
}

.mobile-search.active {
    display: block;
}

.search-input-group {
    display: flex;
    background: var(--bg-light);
    border-radius: 25px;
    overflow: hidden;
}

.search-input {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    outline: none;
    color: var(--text-dark);
}

.search-submit {
    padding: 12px 20px;
    background: var(--primary-color);
    border: none;
    color: var(--white);
    cursor: pointer;
}

/* Mobile Menu */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 300px;
    height: 100vh;
    background: var(--white);
    z-index: 2000;
    transition: right 0.3s ease;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.mobile-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.mobile-logo .logo-icon {
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-logo .logo-text {
    font-size: 1.2rem;
    font-weight: 900;
    color: var(--white);
}

.mobile-logo span {
    font-weight: 600;
    color: var(--secondary-color);
}

.mobile-menu-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-dark);
    cursor: pointer;
    padding: 5px;
}

.mobile-menu-body {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
}

.mobile-nav {
    list-style: none;
    margin: 0;
    padding: 0;
}

.mobile-nav-item {
    margin-bottom: 5px;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: all 0.3s ease;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
    color: var(--primary-color);
    background: rgba(243, 156, 18, 0.1);
}

.mobile-nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.submenu-arrow {
    margin-left: auto;
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.has-submenu.active .submenu-arrow {
    transform: rotate(90deg);
}

.mobile-submenu {
    display: none;
    list-style: none;
    margin: 0;
    padding: 0;
    background: var(--bg-light);
}

.mobile-submenu.active {
    display: block;
}

.mobile-submenu-link {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 20px 12px 60px;
    text-decoration: none;
    color: var(--text-light);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.mobile-submenu-link:hover {
    color: var(--primary-color);
    background: rgba(243, 156, 18, 0.1);
}

.mobile-menu-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
}

.mobile-contact {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.mobile-contact-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    background: var(--bg-light);
    color: var(--text-dark);
    text-decoration: none;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.mobile-contact-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1500;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Breadcrumb */
.breadcrumb-nav {
    background: var(--bg-light);
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.breadcrumb {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
    list-style: none;
    font-size: 0.9rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: '/';
    margin: 0 10px;
    color: var(--text-light);
}

.breadcrumb-item a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-dark);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .action-btn span {
        display: none;
    }
    
    .action-btn {
        width: 45px;
        height: 45px;
        justify-content: center;
        border-radius: 50%;
    }
}

@media (max-width: 992px) {
    .navbar-nav {
        display: none;
    }
    
    .phone-btn {
        display: none;
    }
    
    .search-toggle {
        display: flex;
    }
    
    .mobile-menu-btn {
        display: flex;
    }
}

@media (max-width: 768px) {
    .navbar-content {
        min-height: 60px;
    }
    
    .quote-btn span {
        display: none;
    }
    
    .quote-btn {
        width: 45px;
        height: 45px;
        justify-content: center;
        border-radius: 50%;
    }
}
</style>

<script>
// Navbar JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenu = document.getElementById('mobileMenu');
    const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
    const mobileMenuClose = document.getElementById('mobileMenuClose');

    function toggleMobileMenu() {
        mobileMenuBtn.classList.toggle('active');
        mobileMenu.classList.toggle('active');
        mobileMenuOverlay.classList.toggle('active');
        document.body.style.overflow = mobileMenu.classList.contains('active') ? 'hidden' : '';
    }

    function closeMobileMenu() {
        mobileMenuBtn.classList.remove('active');
        mobileMenu.classList.remove('active');
        mobileMenuOverlay.classList.remove('active');
        document.body.style.overflow = '';
    }

    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }

    if (mobileMenuClose) {
        mobileMenuClose.addEventListener('click', closeMobileMenu);
    }

    if (mobileMenuOverlay) {
        mobileMenuOverlay.addEventListener('click', closeMobileMenu);
    }

    // Mobile search toggle
    const searchToggle = document.getElementById('searchToggle');
    const mobileSearch = document.getElementById('mobileSearch');

    if (searchToggle && mobileSearch) {
        searchToggle.addEventListener('click', function() {
            mobileSearch.classList.toggle('active');
            if (mobileSearch.classList.contains('active')) {
                const searchInput = mobileSearch.querySelector('.search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });
    }

    // Mobile submenu toggle
    document.querySelectorAll('.has-submenu > .mobile-nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const submenu = this.parentElement.querySelector('.mobile-submenu');
            const hasSubmenu = this.parentElement;
            
            if (submenu) {
                hasSubmenu.classList.toggle('active');
                submenu.classList.toggle('active');
            }
        });
    });

    // Close mobile menu on window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 992) {
            closeMobileMenu();
        }
    });

    // Navbar scroll effect
    let lastScrollTop = 0;
    const navbar = document.querySelector('.main-navbar');
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > 100) {
            navbar.classList.add('navbar-scrolled');
        } else {
            navbar.classList.remove('navbar-scrolled');
        }
        
        lastScrollTop = scrollTop;
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        const dropdowns = document.querySelectorAll('.dropdown');
        dropdowns.forEach(dropdown => {
            if (!dropdown.contains(e.target)) {
                dropdown.classList.remove('active');
            }
        });
    });

    // Dropdown hover effects (desktop only)
    if (window.innerWidth >= 992) {
        document.querySelectorAll('.dropdown').forEach(dropdown => {
            dropdown.addEventListener('mouseenter', function() {
                this.classList.add('active');
            });
            
            dropdown.addEventListener('mouseleave', function() {
                this.classList.remove('active');
            });
        });
    }

    // Search form validation
    document.querySelectorAll('.search-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const searchInput = this.querySelector('input[name="search"]');
            if (searchInput && searchInput.value.trim() === '') {
                e.preventDefault();
                searchInput.focus();
                searchInput.setAttribute('placeholder', 'Lütfen arama terimi girin...');
                
                setTimeout(() => {
                    searchInput.setAttribute('placeholder', 'Ürün ara...');
                }, 2000);
            }
        });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                closeMobileMenu();
            }
        });
    });

    // Active menu item highlight based on scroll position
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');

    function highlightActiveSection() {
        let current = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (pageYOffset >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
    }

    window.addEventListener('scroll', highlightActiveSection);

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        // ESC key closes mobile menu
        if (e.key === 'Escape') {
            closeMobileMenu();
            mobileSearch.classList.remove('active');
        }
        
        // Ctrl/Cmd + K opens search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            if (window.innerWidth <= 992) {
                mobileSearch.classList.add('active');
                const searchInput = mobileSearch.querySelector('.search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        }
    });

    // Initialize page
    highlightActiveSection();
});
</script>