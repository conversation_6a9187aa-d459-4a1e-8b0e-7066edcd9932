<?php
/**
 * GNG Makine - Navigasyon Menüsü
 * Bu dosya artık header.php içinde dahil edildiği için ayrı bir navbar dosyasına ihtiyaç yok
 * Ancak özel navigasyon widget'ları için kullanılabilir
 */

/**
 * Ana navigasyon menüsünü oluştur
 */
function generate_main_navigation($current_page = '') {
    $menu_items = [
        'index' => [
            'title' => 'Ana Sayfa',
            'url' => BASE_URL,
            'icon' => 'fas fa-home'
        ],
        'about' => [
            'title' => 'Hakkımızda',
            'url' => BASE_URL . '/about.php',
            'icon' => 'fas fa-info-circle'
        ],
        'products' => [
            'title' => 'Ürünlerimiz',
            'url' => BASE_URL . '/products.php',
            'icon' => 'fas fa-cogs',
            'has_dropdown' => true,
            'dropdown_items' => get_product_categories()
        ],
        'services' => [
            'title' => 'Hizmetlerimiz',
            'url' => BASE_URL . '/services.php',
            'icon' => 'fas fa-tools'
        ],
        'academy' => [
            'title' => 'AEM Akademi',
            'url' => BASE_URL . '/academy.php',
            'icon' => 'fas fa-graduation-cap'
        ],
        'corporate' => [
            'title' => 'Kurumsal',
            'url' => '#',
            'icon' => 'fas fa-building',
            'has_dropdown' => true,
            'dropdown_items' => [
                [
                    'title' => 'Basında Biz',
                    'url' => BASE_URL . '/press.php',
                    'icon' => 'fas fa-newspaper'
                ],
                [
                    'title' => 'Belgelerimiz',
                    'url' => BASE_URL . '/certificates.php',
                    'icon' => 'fas fa-certificate'
                ]
            ]
        ],
        'contact' => [
            'title' => 'İletişim',
            'url' => BASE_URL . '/contact.php',
            'icon' => 'fas fa-phone'
        ]
    ];
    
    return $menu_items;
}

/**
 * Ürün kategorilerini çek
 */
function get_product_categories() {
    try {
        $db = Database::getInstance();
        $categories = $db->fetchAll("
            SELECT id, name, slug 
            FROM categories 
            WHERE status = 'active' AND parent_id IS NULL 
            ORDER BY sort_order ASC, name ASC
            LIMIT 10
        ");
        
        $category_items = [];
        foreach ($categories as $category) {
            $category_items[] = [
                'title' => $category['name'],
                'url' => BASE_URL . '/products.php?category=' . $category['slug'],
                'icon' => 'fas fa-tag'
            ];
        }
        
        // Tüm ürünler linkini ekle
        $category_items[] = [
            'title' => 'Tüm Ürünler',
            'url' => BASE_URL . '/products.php',
            'icon' => 'fas fa-th-large',
            'separator' => true
        ];
        
        return $category_items;
        
    } catch (Exception $e) {
        // Hata durumunda varsayılan kategoriler
        return [
            [
                'title' => 'Üretim Makineleri',
                'url' => BASE_URL . '/products.php?category=uretim',
                'icon' => 'fas fa-industry'
            ],
            [
                'title' => 'Çalışan Makineleri',
                'url' => BASE_URL . '/products.php?category=calisan',
                'icon' => 'fas fa-users'
            ],
            [
                'title' => 'Alan Makineleri',
                'url' => BASE_URL . '/products.php?category=alan',
                'icon' => 'fas fa-map'
            ],
            [
                'title' => 'Müşteri Odaklı',
                'url' => BASE_URL . '/products.php?category=musteri',
                'icon' => 'fas fa-handshake'
            ],
            [
                'title' => 'Tüm Ürünler',
                'url' => BASE_URL . '/products.php',
                'icon' => 'fas fa-th-large',
                'separator' => true
            ]
        ];
    }
}

/**
 * Breadcrumb navigasyonu oluştur
 */
function render_breadcrumb($items = []) {
    if (empty($items)) return '';
    
    $html = '<nav class="breadcrumb-nav" aria-label="Breadcrumb">';
    $html .= '<div class="container">';
    $html .= '<ol class="breadcrumb">';
    
    // Ana sayfa
    $html .= '<li class="breadcrumb-item">';
    $html .= '<a href="' . BASE_URL . '"><i class="fas fa-home"></i> Ana Sayfa</a>';
    $html .= '</li>';
    
    foreach ($items as $index => $item) {
        $is_last = ($index === count($items) - 1);
        
        $html .= '<li class="breadcrumb-item' . ($is_last ? ' active' : '') . '"';
        if ($is_last) {
            $html .= ' aria-current="page"';
        }
        $html .= '>';
        
        if ($is_last || empty($item['url'])) {
            $html .= '<span>' . escape_html($item['title']) . '</span>';
        } else {
            $html .= '<a href="' . escape_html($item['url']) . '">' . escape_html($item['title']) . '</a>';
        }
        
        $html .= '</li>';
    }
    
    $html .= '</ol>';
    $html .= '</div>';
    $html .= '</nav>';
    
    return $html;
}

/**
 * Sayfa başlığı oluştur
 */
function render_page_header($title, $subtitle = '', $background_image = '', $breadcrumb_items = []) {
    $html = '<section class="page-header"';
    
    if (!empty($background_image)) {
        $html .= ' style="background-image: url(' . escape_html($background_image) . ')"';
    }
    
    $html .= '>';
    $html .= '<div class="page-header-overlay"></div>';
    $html .= '<div class="container">';
    $html .= '<div class="page-header-content">';
    
    if (!empty($breadcrumb_items)) {
        $html .= '<div class="breadcrumb-wrapper">';
        $html .= render_breadcrumb($breadcrumb_items);
        $html .= '</div>';
    }
    
    $html .= '<h1 class="page-title">' . escape_html($title) . '</h1>';
    
    if (!empty($subtitle)) {
        $html .= '<p class="page-subtitle">' . escape_html($subtitle) . '</p>';
    }
    
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</section>';
    
    return $html;
}

/**
 * Sidebar navigasyonu oluştur
 */
function render_sidebar_navigation($current_section = '', $items = []) {
    if (empty($items)) return '';
    
    $html = '<nav class="sidebar-nav">';
    $html .= '<h3 class="sidebar-nav-title">Menü</h3>';
    $html .= '<ul class="sidebar-nav-list">';
    
    foreach ($items as $item) {
        $is_active = ($current_section === $item['slug']);
        
        $html .= '<li class="sidebar-nav-item' . ($is_active ? ' active' : '') . '">';
        $html .= '<a href="' . escape_html($item['url']) . '" class="sidebar-nav-link">';
        
        if (!empty($item['icon'])) {
            $html .= '<i class="' . escape_html($item['icon']) . '"></i>';
        }
        
        $html .= '<span>' . escape_html($item['title']) . '</span>';
        
        if (!empty($item['count'])) {
            $html .= '<span class="sidebar-nav-count">' . $item['count'] . '</span>';
        }
        
        $html .= '</a>';
        $html .= '</li>';
    }
    
    $html .= '</ul>';
    $html .= '</nav>';
    
    return $html;
}

/**
 * Ürün kategorileri sidebar'ı
 */
function render_product_categories_sidebar($current_category = '') {
    try {
        $db = Database::getInstance();
        
        // Ana kategorileri çek
        $categories = $db->fetchAll("
            SELECT c.*, 
                   (SELECT COUNT(*) FROM products p WHERE p.category_id = c.id AND p.status = 'active') as product_count
            FROM categories c 
            WHERE c.status = 'active' AND c.parent_id IS NULL 
            ORDER BY c.sort_order ASC, c.name ASC
        ");
        
        $items = [];
        
        // Tüm ürünler
        $total_products = $db->fetchRow("SELECT COUNT(*) as total FROM products WHERE status = 'active'");
        $items[] = [
            'title' => 'Tüm Ürünler',
            'url' => BASE_URL . '/products.php',
            'slug' => '',
            'icon' => 'fas fa-th-large',
            'count' => $total_products['total'] ?? 0
        ];
        
        foreach ($categories as $category) {
            $items[] = [
                'title' => $category['name'],
                'url' => BASE_URL . '/products.php?category=' . $category['slug'],
                'slug' => $category['slug'],
                'icon' => 'fas fa-tag',
                'count' => $category['product_count']
            ];
        }
        
        return render_sidebar_navigation($current_category, $items);
        
    } catch (Exception $e) {
        return '';
    }
}

/**
 * Footer navigasyonu
 */
function render_footer_navigation() {
    $nav_columns = [
        'company' => [
            'title' => 'Kurumsal',
            'items' => [
                ['title' => 'Hakkımızda', 'url' => BASE_URL . '/about.php'],
                ['title' => 'Misyon & Vizyon', 'url' => BASE_URL . '/about.php#mission'],
                ['title' => 'Belgelerimiz', 'url' => BASE_URL . '/certificates.php'],
                ['title' => 'Basında Biz', 'url' => BASE_URL . '/press.php']
            ]
        ],
        'products' => [
            'title' => 'Ürünlerimiz',
            'items' => get_product_categories()
        ],
        'services' => [
            'title' => 'Hizmetlerimiz',
            'items' => [
                ['title' => 'Teknik Servis', 'url' => BASE_URL . '/services.php#technical'],
                ['title' => 'Yedek Parça', 'url' => BASE_URL . '/services.php#spare-parts'],
                ['title' => 'Bakım Onarım', 'url' => BASE_URL . '/services.php#maintenance'],
                ['title' => 'Eğitim', 'url' => BASE_URL . '/academy.php']
            ]
        ],
        'support' => [
            'title' => 'Destek',
            'items' => [
                ['title' => 'İletişim', 'url' => BASE_URL . '/contact.php'],
                ['title' => 'AEM Akademi', 'url' => BASE_URL . '/academy.php'],
                ['title' => 'SSS', 'url' => BASE_URL . '/faq.php'],
                ['title' => 'Teknik Dökümanlar', 'url' => BASE_URL . '/documents.php']
            ]
        ]
    ];
    
    return $nav_columns;
}

/**
 * Aktif sayfa kontrolü
 */
function is_active_page($page_slug, $current_page = '') {
    if (empty($current_page)) {
        $current_page = basename($_SERVER['PHP_SELF'], '.php');
    }
    
    return $current_page === $page_slug;
}

/**
 * Mobil menü oluşturucu
 */
function render_mobile_menu($current_page = '') {
    $menu_items = generate_main_navigation($current_page);
    
    $html = '<div class="mobile-menu-wrapper">';
    $html .= '<ul class="mobile-menu-list">';
    
    foreach ($menu_items as $slug => $item) {
        $is_active = is_active_page($slug, $current_page);
        
        $html .= '<li class="mobile-menu-item' . ($is_active ? ' active' : '') . '">';
        
        if (!empty($item['has_dropdown'])) {
            $html .= '<div class="mobile-menu-toggle">';
            $html .= '<a href="' . escape_html($item['url']) . '" class="mobile-menu-link">';
            $html .= '<i class="' . escape_html($item['icon']) . '"></i>';
            $html .= '<span>' . escape_html($item['title']) . '</span>';
            $html .= '</a>';
            $html .= '<button class="mobile-submenu-toggle" type="button">';
            $html .= '<i class="fas fa-chevron-down"></i>';
            $html .= '</button>';
            $html .= '</div>';
            
            if (!empty($item['dropdown_items'])) {
                $html .= '<ul class="mobile-submenu">';
                foreach ($item['dropdown_items'] as $sub_item) {
                    if (!empty($sub_item['separator'])) {
                        $html .= '<li class="mobile-submenu-separator"></li>';
                    }
                    
                    $html .= '<li class="mobile-submenu-item">';
                    $html .= '<a href="' . escape_html($sub_item['url']) . '" class="mobile-submenu-link">';
                    $html .= '<i class="' . escape_html($sub_item['icon']) . '"></i>';
                    $html .= '<span>' . escape_html($sub_item['title']) . '</span>';
                    $html .= '</a>';
                    $html .= '</li>';
                }
                $html .= '</ul>';
            }
        } else {
            $html .= '<a href="' . escape_html($item['url']) . '" class="mobile-menu-link">';
            $html .= '<i class="' . escape_html($item['icon']) . '"></i>';
            $html .= '<span>' . escape_html($item['title']) . '</span>';
            $html .= '</a>';
        }
        
        $html .= '</li>';
    }
    
    $html .= '</ul>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Hızlı linkler widget'ı
 */
function render_quick_links() {
    $quick_links = [
        [
            'title' => 'Ürün Kataloğu',
            'url' => BASE_URL . '/products.php',
            'icon' => 'fas fa-book',
            'description' => 'Tüm ürünlerimizi inceleyin'
        ],
        [
            'title' => 'Teknik Destek',
            'url' => BASE_URL . '/contact.php?type=support',
            'icon' => 'fas fa-headset',
            'description' => '7/24 teknik destek'
        ],
        [
            'title' => 'Fiyat Teklifi',
            'url' => BASE_URL . '/contact.php?type=quote',
            'icon' => 'fas fa-calculator',
            'description' => 'Hemen teklif alın'
        ],
        [
            'title' => 'Showroom',
            'url' => BASE_URL . '/contact.php#location',
            'icon' => 'fas fa-map-marker-alt',
            'description' => 'Showroom\'umuzu ziyaret edin'
        ]
    ];
    
    $html = '<div class="quick-links-widget">';
    $html .= '<h3 class="widget-title">Hızlı Erişim</h3>';
    $html .= '<div class="quick-links-grid">';
    
    foreach ($quick_links as $link) {
        $html .= '<a href="' . escape_html($link['url']) . '" class="quick-link-item">';
        $html .= '<div class="quick-link-icon">';
        $html .= '<i class="' . escape_html($link['icon']) . '"></i>';
        $html .= '</div>';
        $html .= '<div class="quick-link-content">';
        $html .= '<h4 class="quick-link-title">' . escape_html($link['title']) . '</h4>';
        $html .= '<p class="quick-link-description">' . escape_html($link['description']) . '</p>';
        $html .= '</div>';
        $html .= '</a>';
    }
    
    $html .= '</div>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Kategori filter navigasyonu
 */
function render_category_filter($current_category = '', $show_count = true) {
    try {
        $db = Database::getInstance();
        
        $categories = $db->fetchAll("
            SELECT c.*, 
                   (SELECT COUNT(*) FROM products p WHERE p.category_id = c.id AND p.status = 'active') as product_count
            FROM categories c 
            WHERE c.status = 'active' AND c.parent_id IS NULL 
            ORDER BY c.sort_order ASC, c.name ASC
        ");
        
        $html = '<div class="category-filter">';
        $html .= '<div class="filter-tabs">';
        
        // Tümü butonu
        $total_products = $db->fetchRow("SELECT COUNT(*) as total FROM products WHERE status = 'active'");
        $all_active = empty($current_category) ? ' active' : '';
        
        $html .= '<a href="' . BASE_URL . '/products.php" class="filter-tab' . $all_active . '">';
        $html .= '<span class="tab-text">Tümü</span>';
        if ($show_count) {
            $html .= '<span class="tab-count">(' . ($total_products['total'] ?? 0) . ')</span>';
        }
        $html .= '</a>';
        
        // Kategori butonları
        foreach ($categories as $category) {
            $is_active = ($current_category === $category['slug']) ? ' active' : '';
            
            $html .= '<a href="' . BASE_URL . '/products.php?category=' . escape_html($category['slug']) . '" class="filter-tab' . $is_active . '">';
            $html .= '<span class="tab-text">' . escape_html($category['name']) . '</span>';
            if ($show_count) {
                $html .= '<span class="tab-count">(' . $category['product_count'] . ')</span>';
            }
            $html .= '</a>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
        
    } catch (Exception $e) {
        return '';
    }
}

/**
 * Sosyal medya linklerini render et
 */
function render_social_links($style = 'default') {
    $social_links = [
        'facebook' => [
            'url' => FACEBOOK_URL,
            'icon' => 'fab fa-facebook-f',
            'title' => 'Facebook\'ta takip edin',
            'color' => '#1877f2'
        ],
        'instagram' => [
            'url' => INSTAGRAM_URL,
            'icon' => 'fab fa-instagram',
            'title' => 'Instagram\'da takip edin',
            'color' => '#e4405f'
        ],
        'linkedin' => [
            'url' => LINKEDIN_URL,
            'icon' => 'fab fa-linkedin-in',
            'title' => 'LinkedIn\'de bağlantı kurun',
            'color' => '#0077b5'
        ],
        'twitter' => [
            'url' => TWITTER_URL,
            'icon' => 'fab fa-twitter',
            'title' => 'Twitter\'da takip edin',
            'color' => '#1da1f2'
        ]
    ];
    
    $html = '<div class="social-links social-links-' . escape_html($style) . '">';
    
    foreach ($social_links as $platform => $link) {
        if (!empty($link['url'])) {
            $html .= '<a href="' . escape_html($link['url']) . '" ';
            $html .= 'class="social-link social-link-' . $platform . '" ';
            $html .= 'target="_blank" rel="noopener" ';
            $html .= 'title="' . escape_html($link['title']) . '" ';
            $html .= 'aria-label="' . escape_html($link['title']) . '">';
            $html .= '<i class="' . escape_html($link['icon']) . '"></i>';
            $html .= '</a>';
        }
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Dil seçici (gelecekte çoklu dil desteği için)
 */
function render_language_selector($current_lang = 'tr') {
    $languages = [
        'tr' => [
            'name' => 'Türkçe',
            'flag' => 'tr.png',
            'code' => 'tr'
        ],
        'en' => [
            'name' => 'English',
            'flag' => 'en.png',
            'code' => 'en'
        ]
    ];
    
    if (count($languages) <= 1) return '';
    
    $html = '<div class="language-selector">';
    $html .= '<button class="lang-toggle" type="button">';
    $html .= '<img src="' . ASSETS_URL . '/images/flags/' . $languages[$current_lang]['flag'] . '" alt="' . $languages[$current_lang]['name'] . '">';
    $html .= '<span>' . $languages[$current_lang]['code'] . '</span>';
    $html .= '<i class="fas fa-chevron-down"></i>';
    $html .= '</button>';
    
    $html .= '<ul class="lang-dropdown">';
    foreach ($languages as $code => $lang) {
        if ($code !== $current_lang) {
            $html .= '<li>';
            $html .= '<a href="?lang=' . $code . '" class="lang-option">';
            $html .= '<img src="' . ASSETS_URL . '/images/flags/' . $lang['flag'] . '" alt="' . $lang['name'] . '">';
            $html .= '<span>' . $lang['name'] . '</span>';
            $html .= '</a>';
            $html .= '</li>';
        }
    }
    $html .= '</ul>';
    $html .= '</div>';
    
    return $html;
}
?>