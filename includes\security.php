<?php
/**
 * GNG Makine - Security Functions
 * Güvenlik fonksiyonları ve yardımcı işlevler
 */

// CSRF Token oluşturma ve doğrulama
class CSRFProtection {
    public static function generateToken() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = $token;
        return $token;
    }
    
    public static function validateToken($token) {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $token)) {
            return false;
        }
        
        // Token'ı kullandıktan sonra sil (one-time use)
        unset($_SESSION['csrf_token']);
        return true;
    }
    
    public static function getTokenField() {
        $token = self::generateToken();
        return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
    }
}

// Input sanitization functions
class InputSanitizer {
    /**
     * HTML içeriğini temizle
     */
    public static function sanitizeHtml($input) {
        return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * String'i temizle
     */
    public static function sanitizeString($input) {
        return filter_var(trim($input), FILTER_SANITIZE_STRING);
    }
    
    /**
     * Email'i doğrula ve temizle
     */
    public static function sanitizeEmail($email) {
        $email = filter_var(trim($email), FILTER_SANITIZE_EMAIL);
        return filter_var($email, FILTER_VALIDATE_EMAIL) ? $email : false;
    }
    
    /**
     * URL'yi doğrula ve temizle
     */
    public static function sanitizeUrl($url) {
        $url = filter_var(trim($url), FILTER_SANITIZE_URL);
        return filter_var($url, FILTER_VALIDATE_URL) ? $url : false;
    }
    
    /**
     * Integer'ı doğrula
     */
    public static function sanitizeInt($input) {
        return filter_var($input, FILTER_VALIDATE_INT);
    }
    
    /**
     * Float'ı doğrula
     */
    public static function sanitizeFloat($input) {
        return filter_var($input, FILTER_VALIDATE_FLOAT);
    }
    
    /**
     * Telefon numarasını temizle
     */
    public static function sanitizePhone($phone) {
        return preg_replace('/[^0-9+\-\(\)\s]/', '', $phone);
    }
    
    /**
     * Dosya adını güvenli hale getir
     */
    public static function sanitizeFilename($filename) {
        // Tehlikeli karakterleri kaldır
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        
        // Çoklu noktaları tek noktaya çevir
        $filename = preg_replace('/\.+/', '.', $filename);
        
        // Başında ve sonunda nokta olmasın
        $filename = trim($filename, '.');
        
        return $filename;
    }
}

// File upload security
class FileUploadSecurity {
    private static $allowedTypes = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'],
        'archive' => ['zip', 'rar']
    ];
    
    private static $maxFileSize = 10 * 1024 * 1024; // 10MB
    
    /**
     * Dosya yükleme güvenlik kontrolü
     */
    public static function validateUpload($file, $allowedCategory = 'image') {
        $errors = [];
        
        // Dosya var mı kontrol et
        if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'Dosya yükleme hatası.';
            return $errors;
        }
        
        // Dosya boyutu kontrolü
        if ($file['size'] > self::$maxFileSize) {
            $errors[] = 'Dosya boyutu çok büyük. Maksimum ' . (self::$maxFileSize / 1024 / 1024) . 'MB olabilir.';
        }
        
        // Dosya uzantısı kontrolü
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, self::$allowedTypes[$allowedCategory] ?? [])) {
            $errors[] = 'Geçersiz dosya türü.';
        }
        
        // MIME type kontrolü
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'txt' => 'text/plain',
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed'
        ];
        
        if (!isset($allowedMimes[$extension]) || $mimeType !== $allowedMimes[$extension]) {
            $errors[] = 'Dosya içeriği ile uzantısı uyuşmuyor.';
        }
        
        return $errors;
    }
    
    /**
     * Güvenli dosya adı oluştur
     */
    public static function generateSafeFilename($originalName) {
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        $basename = pathinfo($originalName, PATHINFO_FILENAME);
        
        // Türkçe karakterleri değiştir
        $basename = self::turkishToEnglish($basename);
        
        // Güvenli karakterlere çevir
        $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
        $basename = preg_replace('/_+/', '_', $basename);
        $basename = trim($basename, '_');
        
        // Benzersiz isim oluştur
        $uniqueId = uniqid();
        
        return $basename . '_' . $uniqueId . '.' . $extension;
    }
    
    /**
     * Türkçe karakterleri İngilizce'ye çevir
     */
    private static function turkishToEnglish($text) {
        $turkish = ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'Ç', 'Ğ', 'I', 'İ', 'Ö', 'Ş', 'Ü'];
        $english = ['c', 'g', 'i', 'o', 's', 'u', 'C', 'G', 'I', 'I', 'O', 'S', 'U'];
        
        return str_replace($turkish, $english, $text);
    }
}

// Rate limiting
class RateLimiter {
    private static $limits = [
        'contact_form' => ['requests' => 5, 'window' => 3600], // 5 requests per hour
        'login' => ['requests' => 10, 'window' => 900], // 10 attempts per 15 minutes
        'search' => ['requests' => 100, 'window' => 3600] // 100 searches per hour
    ];
    
    /**
     * Rate limit kontrolü
     */
    public static function checkLimit($action, $identifier = null) {
        if (!isset(self::$limits[$action])) {
            return true;
        }
        
        $identifier = $identifier ?: self::getClientIdentifier();
        $key = $action . '_' . $identifier;
        
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        $now = time();
        $window = self::$limits[$action]['window'];
        $maxRequests = self::$limits[$action]['requests'];
        
        // Session'dan geçmiş istekleri al
        if (!isset($_SESSION['rate_limits'][$key])) {
            $_SESSION['rate_limits'][$key] = [];
        }
        
        $requests = $_SESSION['rate_limits'][$key];
        
        // Eski istekleri temizle
        $requests = array_filter($requests, function($timestamp) use ($now, $window) {
            return ($now - $timestamp) < $window;
        });
        
        // Limit kontrolü
        if (count($requests) >= $maxRequests) {
            return false;
        }
        
        // Yeni isteği kaydet
        $requests[] = $now;
        $_SESSION['rate_limits'][$key] = $requests;
        
        return true;
    }
    
    /**
     * Client identifier oluştur
     */
    private static function getClientIdentifier() {
        return hash('sha256', $_SERVER['REMOTE_ADDR'] . ($_SERVER['HTTP_USER_AGENT'] ?? ''));
    }
}

// SQL Injection koruması için prepared statement helper
class DatabaseSecurity {
    /**
     * Güvenli SQL sorgusu çalıştır
     */
    public static function executeQuery($pdo, $sql, $params = []) {
        try {
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            // Log the error (don't expose to user)
            error_log('Database error: ' . $e->getMessage());
            throw new Exception('Veritabanı hatası oluştu.');
        }
    }
    
    /**
     * Güvenli INSERT işlemi
     */
    public static function insert($pdo, $table, $data) {
        $columns = array_keys($data);
        $placeholders = array_map(function($col) { return ':' . $col; }, $columns);
        
        $sql = "INSERT INTO {$table} (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        return self::executeQuery($pdo, $sql, $data);
    }
    
    /**
     * Güvenli UPDATE işlemi
     */
    public static function update($pdo, $table, $data, $where, $whereParams = []) {
        $setParts = array_map(function($col) { return $col . ' = :' . $col; }, array_keys($data));
        
        $sql = "UPDATE {$table} SET " . implode(', ', $setParts) . " WHERE {$where}";
        
        $params = array_merge($data, $whereParams);
        
        return self::executeQuery($pdo, $sql, $params);
    }
}

// Session güvenliği
class SessionSecurity {
    /**
     * Güvenli session başlat
     */
    public static function startSecureSession() {
        if (session_status() == PHP_SESSION_NONE) {
            // Session ayarları
            ini_set('session.cookie_httponly', 1);
            ini_set('session.use_only_cookies', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
            
            session_start();
            
            // Session hijacking koruması
            if (!isset($_SESSION['user_agent'])) {
                $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
            } elseif ($_SESSION['user_agent'] !== ($_SERVER['HTTP_USER_AGENT'] ?? '')) {
                session_destroy();
                session_start();
            }
            
            // Session regeneration
            if (!isset($_SESSION['last_regeneration'])) {
                $_SESSION['last_regeneration'] = time();
            } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 dakikada bir
                session_regenerate_id(true);
                $_SESSION['last_regeneration'] = time();
            }
        }
    }
}
?>
