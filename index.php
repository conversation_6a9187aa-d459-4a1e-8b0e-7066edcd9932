<?php
/**
 * GNG Makine - Modern Profesyonel Ana Sayfa
 * Minimalist, teknolojik ve etkileyici tasarım
 */

// Konfigürasyonu dahil et
require_once __DIR__ . '/config/config.php';

// Sayfa meta bilgileri
$page_title = "Ana Sayfa - " . SITE_NAME;
$page_description = "Gelişmiş endüstriyel makine teknolojileri ile üretimde yeni çağ. Hassasiyet, kalite ve güvenilirlik.";
$page_keywords = "endüstriyel makine, CNC teknoloji, hassas üretim, GNG Makine";
$current_page = "home";

// Veritabanından verileri çek
try {
    // Öne çıkan ürünler
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name, c.slug as category_slug, c.icon as category_icon
                          FROM products p
                          LEFT JOIN categories c ON p.category_id = c.id
                          WHERE p.status = 'active' AND p.featured = 1
                          ORDER BY p.sort_order ASC, p.created_at DESC LIMIT 6");
    $stmt->execute();
    $featured_products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Kategoriler
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE status = 'active' ORDER BY sort_order ASC LIMIT 8");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Teknoloji göstergeleri
    $tech_stats = [
        ['value' => '99.9%', 'label' => 'Hassasiyet', 'icon' => 'crosshairs', 'delay' => '0'],
        ['value' => '24/7', 'label' => 'Operasyon', 'icon' => 'clock', 'delay' => '0.2'],
        ['value' => '15+', 'label' => 'Yıl Tecrübe', 'icon' => 'award', 'delay' => '0.4'],
        ['value' => '500+', 'label' => 'Tamamlanan Proje', 'icon' => 'check-circle', 'delay' => '0.6']
    ];

} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    $featured_products = [];
    $categories = [];
}

// Hero slider içeriği (gelişmiş teknoloji odaklı)
$hero_slides = [
    [
        'title' => 'YENİ NESİL CNC',
        'subtitle' => 'TEKNOLOJİSİ',
        'description' => 'Dünyanın en prestijli tasarım ödüllerine sahip FIBERMAK Gen-5, ileri teknolojisi ve olağanüstü performansıyla benzersiz bir deneyim sunuyor.',
        'cta_text' => 'Şimdi İncele',
        'cta_link' => '/products.php?category=cnc-torna',
        'tech_features' => ['Mikron Hassasiyet', 'Otomatik Kalibrasyon', 'AI Destekli İşleme'],
        'awards' => ['Design Award 2024', 'Innovation Award 2024']
    ],
    [
        'title' => 'AKILLI ÜRETİM',
        'subtitle' => 'SİSTEMLERİ',
        'description' => 'Endüstri 4.0 teknolojileri ile donatılmış akıllı üretim sistemleri. IoT entegrasyonu ve gerçek zamanlı veri analizi.',
        'cta_text' => 'Teknoloji Keşfet',
        'cta_link' => '/products.php?category=akilli-sistemler',
        'tech_features' => ['IoT Entegrasyon', 'Gerçek Zamanlı İzleme', 'Predictive Maintenance'],
        'awards' => ['Tech Innovation 2024', 'Smart Manufacturing Award']
    ],
    [
        'title' => 'HASSAS LAZER',
        'subtitle' => 'KESIM SİSTEMİ',
        'description' => 'Fiber lazer teknolojisi ile nm seviyesinde hassas kesim. Gelişmiş optik sistemler ve termal kontrol.',
        'cta_text' => 'Detayları Gör',
        'cta_link' => '/products.php?category=lazer-kesim',
        'tech_features' => ['Fiber Lazer', 'Termal Kontrol', 'Otomatik Odaklama'],
        'awards' => ['Precision Award 2024', 'Laser Tech Innovation']
    ]
];

include 'includes/header.php';
?>

<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    
    <!-- Modern CSS Framework -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
    :root {
        --primary: #FF6B35;
        --primary-dark: #E55529;
        --secondary: #1A2238;
        --accent: #00D4FF;
        --dark: #0A0F1C;
        --dark-blue: #1A2238;
        --white: #FFFFFF;
        --light-gray: #F8FAFC;
        --medium-gray: #64748B;
        --text-dark: #0F172A;
        --text-light: #64748B;
        --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        --gradient-tech: linear-gradient(135deg, var(--dark) 0%, var(--dark-blue) 100%);
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        line-height: 1.6;
        color: var(--text-dark);
        overflow-x: hidden;
        background: var(--white);
        scroll-behavior: smooth;
    }

    /* Geometric Network Background */
    .geometric-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 1;
    }

    .geometric-bg::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: 
            radial-gradient(circle at 20% 50%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 80%, rgba(26, 34, 56, 0.05) 0%, transparent 50%);
        animation: networkPulse 15s ease-in-out infinite;
    }

    @keyframes networkPulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }

    .network-lines {
        position: absolute;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='network' width='100' height='100' patternUnits='userSpaceOnUse'%3E%3Cpath d='M10 10L50 25L90 10M20 40L60 55L80 40M30 70L70 85L90 70' stroke='%23FF6B35' stroke-width='0.5' fill='none' opacity='0.3'/%3E%3Ccircle cx='10' cy='10' r='1' fill='%23FF6B35' opacity='0.5'/%3E%3Ccircle cx='50' cy='25' r='1' fill='%2300D4FF' opacity='0.5'/%3E%3Ccircle cx='90' cy='10' r='1' fill='%23FF6B35' opacity='0.5'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23network)'/%3E%3C/svg%3E");
        opacity: 0.4;
        animation: networkMove 30s linear infinite;
    }

    @keyframes networkMove {
        0% { transform: translateX(0) translateY(0); }
        25% { transform: translateX(-10px) translateY(-10px); }
        50% { transform: translateX(10px) translateY(-5px); }
        75% { transform: translateX(-5px) translateY(10px); }
        100% { transform: translateX(0) translateY(0); }
    }

    /* Modern Hero Section */
    .hero {
        height: 100vh;
        min-height: 600px;
        background: var(--gradient-tech);
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        margin-top: -80px;
        padding-top: 80px;
    }

    .hero-slider {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .hero-slide {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transform: translateX(100%);
        transition: all 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .hero-slide.active {
        opacity: 1;
        transform: translateX(0);
    }

    .container {
        max-width: 1400px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
        position: relative;
        z-index: 10;
    }

    .hero-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: center;
        width: 100%;
        min-height: 500px;
    }

    @media (max-width: 768px) {
        .hero-content {
            grid-template-columns: 1fr;
            gap: 2rem;
            text-align: center;
        }
    }

    .hero-text {
        opacity: 0;
        transform: translateY(50px);
    }

    .hero-slide.active .hero-text {
        animation: slideUp 1s ease 0.3s forwards;
    }

    @keyframes slideUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .hero-subtitle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
    }

    .typing-text {
        font-family: 'JetBrains Mono', monospace;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--accent);
        letter-spacing: 0.1em;
        text-transform: uppercase;
    }

    .tech-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(0, 212, 255, 0.1);
        border: 1px solid rgba(0, 212, 255, 0.3);
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-size: 0.75rem;
        color: var(--accent);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .tech-dot {
        width: 8px;
        height: 8px;
        background: var(--accent);
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    .hero-title {
        font-size: clamp(3.5rem, 10vw, 6rem);
        font-weight: 900;
        line-height: 0.9;
        color: var(--white);
        margin-bottom: 1.5rem;
        position: relative;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .hero-title .title-line {
        display: block;
        margin-bottom: 0.2rem;
    }

    .hero-title .gradient-text {
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero-title::after {
        content: '';
        position: absolute;
        bottom: -0.5rem;
        left: 0;
        width: 0;
        height: 4px;
        background: var(--primary);
        animation: titleUnderline 1.5s ease 1.5s forwards;
    }

    @keyframes titleUnderline {
        to { width: 120px; }
    }

    .hero-description {
        font-size: 1.125rem;
        line-height: 1.7;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 2rem;
        max-width: 500px;
    }

    .tech-features {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .tech-feature {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-size: 0.875rem;
        color: var(--white);
        font-weight: 500;
    }

    .hero-cta {
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        background: var(--gradient-primary);
        color: var(--white);
        padding: 1rem 2rem;
        border-radius: 3rem;
        text-decoration: none;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .hero-cta::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s;
    }

    .hero-cta:hover::before {
        left: 100%;
    }

    .hero-cta:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
    }

    .hero-visual {
        position: relative;
        height: 600px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transform: scale(0.8);
    }

    .hero-slide.active .hero-visual {
        animation: scaleIn 1.2s ease 0.6s forwards;
    }

    @keyframes scaleIn {
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* Hero Image Styles */
    .hero-image-container {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        border-radius: 2rem;
    }

    .hero-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
        object-position: center;
        filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
        transition: transform 0.6s ease;
    }

    .hero-image:hover {
        transform: scale(1.05);
    }

    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        border-radius: 2rem;
        overflow: hidden;
    }

    .tech-grid-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="tech-grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(249,178,51,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23tech-grid)"/></svg>');
        opacity: 0.6;
    }

    .glow-effect {
        position: absolute;
        top: -50%;
        left: -50%;
        right: -50%;
        bottom: -50%;
        background: radial-gradient(circle, rgba(249, 178, 51, 0.1) 0%, transparent 70%);
        animation: glow-pulse 4s ease-in-out infinite;
    }

    @keyframes glow-pulse {
        0%, 100% { opacity: 0.3; transform: scale(1); }
        50% { opacity: 0.6; transform: scale(1.1); }
    }

    .product-showcase {
        position: relative;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    .product-showcase::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(from 0deg, transparent, var(--primary), var(--accent), transparent);
        animation: rotate 10s linear infinite;
        opacity: 0.1;
    }

    @keyframes rotate {
        to { transform: rotate(360deg); }
    }

    .product-image {
        font-size: 8rem;
        color: var(--primary);
        z-index: 2;
        position: relative;
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    .awards-section {
        position: absolute;
        top: 2rem;
        right: 2rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        z-index: 15;
    }

    .award-badge {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 0.75rem;
        border-radius: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--text-dark);
        box-shadow: var(--shadow);
        border: 1px solid rgba(255, 107, 53, 0.2);
    }

    .award-icon {
        width: 24px;
        height: 24px;
        background: var(--gradient-primary);
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.75rem;
    }

    /* Hero Controls */
    .hero-controls {
        position: absolute;
        bottom: 2rem;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 0.75rem;
        z-index: 15;
    }

    .hero-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.5);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .hero-dot.active {
        background: var(--primary);
        border-color: var(--primary);
        transform: scale(1.2);
    }

    .hero-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 3rem;
        height: 3rem;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 15;
    }

    .hero-nav:hover {
        background: var(--primary);
        transform: translateY(-50%) scale(1.1);
    }

    .hero-nav.prev { left: 2rem; }
    .hero-nav.next { right: 2rem; }

    /* Technology Stats Section */
    .tech-stats {
        padding: 6rem 0;
        background: var(--light-gray);
        position: relative;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 3rem;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .stat-card {
        background: var(--white);
        padding: 3rem 2rem;
        border-radius: 1.5rem;
        text-align: center;
        box-shadow: var(--shadow);
        border: 1px solid rgba(255, 107, 53, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        opacity: 0;
        transform: translateY(30px);
    }

    .stat-card.visible {
        animation: fadeInUp 0.6s ease forwards;
    }

    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: var(--gradient-primary);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .stat-card:hover::before {
        transform: scaleX(1);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .stat-icon {
        width: 4rem;
        height: 4rem;
        background: var(--gradient-primary);
        border-radius: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 1.5rem;
        margin: 0 auto 1.5rem;
    }

    .stat-value {
        font-size: 3rem;
        font-weight: 900;
        color: var(--text-dark);
        margin-bottom: 0.5rem;
        font-family: 'JetBrains Mono', monospace;
    }

    .stat-label {
        font-size: 1rem;
        font-weight: 600;
        color: var(--text-light);
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    /* Modern Categories Section */
    .categories-section {
        padding: 8rem 0;
        background: var(--white);
    }

    .section-header {
        text-align: center;
        margin-bottom: 5rem;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
        padding: 0 2rem;
    }

    .section-subtitle {
        font-family: 'JetBrains Mono', monospace;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--primary);
        letter-spacing: 0.1em;
        margin-bottom: 1rem;
        text-transform: uppercase;
    }

    .section-title {
        font-size: clamp(2rem, 5vw, 3.5rem);
        font-weight: 800;
        color: var(--text-dark);
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .section-description {
        font-size: 1.125rem;
        color: var(--text-light);
        line-height: 1.7;
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .category-card {
        background: var(--white);
        border-radius: 1.5rem;
        overflow: hidden;
        box-shadow: var(--shadow);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
        position: relative;
        group: category;
    }

    .category-card:hover {
        transform: translateY(-10px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary);
    }

    .category-image {
        height: 200px;
        background: linear-gradient(135deg, var(--light-gray) 0%, #e2e8f0 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3.5rem;
        color: var(--primary);
        position: relative;
        overflow: hidden;
    }

    .category-image::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--gradient-primary);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .category-card:hover .category-image::before {
        opacity: 0.1;
    }

    .category-content {
        padding: 2rem;
        position: relative;
    }

    .category-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-dark);
        margin-bottom: 0.75rem;
        transition: color 0.3s ease;
    }

    .category-card:hover .category-title {
        color: var(--primary);
    }

    .category-description {
        color: var(--text-light);
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .category-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--primary);
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        transition: all 0.3s ease;
    }

    .category-link:hover {
        gap: 0.75rem;
    }

    /* Featured Products Section */
    .products-section {
        padding: 8rem 0;
        background: var(--light-gray);
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2.5rem;
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .product-card {
        background: var(--white);
        border-radius: 1.5rem;
        overflow: hidden;
        box-shadow: var(--shadow);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        position: relative;
        group: product;
    }

    .product-card:hover {
        transform: translateY(-15px);
        box-shadow: var(--shadow-xl);
    }

    .product-image-container {
        height: 250px;
        background: linear-gradient(135deg, var(--light-gray) 0%, #e2e8f0 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 4rem;
        color: var(--primary);
        position: relative;
        overflow: hidden;
    }

    .product-badge {
        position: absolute;
        top: 1rem;
        left: 1rem;
        background: var(--gradient-primary);
        color: var(--white);
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        z-index: 5;
    }

    .product-content {
        padding: 2rem;
    }

    .product-category {
        font-family: 'JetBrains Mono', monospace;
        font-size: 0.75rem;
        font-weight: 500;
        color: var(--primary);
        text-transform: uppercase;
        letter-spacing: 0.1em;
        margin-bottom: 0.75rem;
    }

    .product-title {
        font-size: 1.375rem;
        font-weight: 700;
        color: var(--text-dark);
        margin-bottom: 1rem;
        line-height: 1.3;
        transition: color 0.3s ease;
    }

    .product-card:hover .product-title {
        color: var(--primary);
    }

    .product-description {
        color: var(--text-light);
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    .product-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .product-btn {
        background: var(--gradient-primary);
        color: var(--white);
        padding: 0.875rem 1.75rem;
        border-radius: 2rem;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .product-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    /* CTA Section */
    .cta-section {
        padding: 8rem 0;
        background: var(--gradient-tech);
        color: var(--white);
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .cta-content {
        max-width: 800px;
        margin: 0 auto;
        padding: 0 2rem;
        position: relative;
        z-index: 10;
    }

    .cta-title {
        font-size: clamp(2.5rem, 6vw, 4rem);
        font-weight: 800;
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .cta-description {
        font-size: 1.25rem;
        line-height: 1.7;
        opacity: 0.9;
        margin-bottom: 3rem;
    }

    .cta-buttons {
        display: flex;
        gap: 1.5rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn-cta {
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1.125rem 2.5rem;
        border-radius: 3rem;
        text-decoration: none;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .btn-primary-cta {
        background: var(--primary);
        color: var(--white);
        box-shadow: var(--shadow-lg);
    }

    .btn-primary-cta:hover {
        background: var(--primary-dark);
        transform: translateY(-3px);
        box-shadow: var(--shadow-xl);
    }

    .btn-outline-cta {
        background: transparent;
        color: var(--white);
        border-color: var(--white);
    }

    .btn-outline-cta:hover {
        background: var(--white);
        color: var(--dark);
        transform: translateY(-3px);
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .hero-content {
            grid-template-columns: 1fr;
            gap: 3rem;
            text-align: center;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
        }

        .hero-nav {
            display: none;
        }

        .awards-section {
            position: relative;
            top: auto;
            right: auto;
            flex-direction: row;
            justify-content: center;
            margin-top: 2rem;
        }
    }

    @media (max-width: 768px) {
        .hero {
            height: auto;
            min-height: 100vh;
            padding: 100px 0 50px;
        }

        .stats-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .categories-grid,
        .products-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }

        .btn-cta {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }

        .container {
            padding: 0 1rem;
        }
    }

    /* Advanced Animations */
    .reveal {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }

    .reveal.visible {
        opacity: 1;
        transform: translateY(0);
    }

    .stagger-item {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.4s ease;
    }

    .stagger-item.visible {
        opacity: 1;
        transform: translateY(0);
    }

    /* Loading Animation */
    .page-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--dark);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
    }

    .loader {
        width: 60px;
        height: 60px;
        border: 3px solid rgba(255, 107, 53, 0.3);
        border-radius: 50%;
        border-top-color: var(--primary);
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .page-loader.hidden {
        opacity: 0;
        pointer-events: none;
    }
    </style>
</head>

<body>
    <!-- Page Loader -->
    <div class="page-loader" id="pageLoader">
        <div class="loader"></div>
    </div>

    <?php include 'includes/navbar.php'; ?>

    <!-- Modern Hero Section with Slider -->
    <section class="hero" id="hero">
    <div class="geometric-bg">
        <div class="network-lines"></div>
        <!-- 3D Grid Pattern -->
        <div class="grid-3d"></div>
        <!-- Floating Particles -->
        <div class="floating-particles" id="particles"></div>
    </div>
    
    <div class="container">
        <div class="hero-slider" id="heroSlider">
            <?php foreach ($hero_slides as $index => $slide): ?>
            <div class="hero-slide <?php echo $index === 0 ? 'active' : ''; ?>" data-slide="<?php echo $index; ?>">
                <div class="hero-content">
                    <div class="hero-text">
                        <div class="hero-subtitle">
                            <span class="typing-text">GNG Makine Innovation</span>
                            <div class="tech-indicator">
                                <div class="tech-dot"></div>
                                <span>Live Tech</span>
                            </div>
                        </div>
                        
                        <h1 class="hero-title">
                            <span class="title-line"><?php echo $slide['title']; ?></span>
                            <span class="title-line gradient-text"><?php echo $slide['subtitle']; ?></span>
                        </h1>
             
                        <p class="hero-description"><?php echo $slide['description']; ?></p>
                        
                        <div class="tech-features">
                            <?php foreach ($slide['tech_features'] as $index => $feature): ?>
                            <span class="tech-feature" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                                <i class="fas fa-check"></i>
                                <?php echo $feature; ?>
                            </span>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="hero-actions">
                            <a href="<?php echo $slide['cta_link']; ?>" class="hero-cta primary">
                                <span class="cta-text"><?php echo $slide['cta_text']; ?></span>
                                <div class="cta-icon">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                            </a>
                            <a href="<?php echo BASE_URL; ?>/demo" class="hero-cta secondary">
                                <span class="cta-text">Demo İzle</span>
                                <div class="cta-icon">
                                    <i class="fas fa-play"></i>
                                </div>
                            </a>
                        </div>
                    </div>

                    <div class="hero-visual">
                        <!-- Hero Image -->
                        <div class="hero-image-container">
                            <img src="<?php echo SITE_URL; ?>/assets/images/hero/slide-<?php echo $index + 1; ?>.png"
                                 alt="<?php echo escape_html($slide['title'] . ' ' . $slide['subtitle']); ?>"
                                 class="hero-image">

                            <!-- Image Overlay Effects -->
                            <div class="image-overlay">
                                <div class="tech-grid-overlay"></div>
                                <div class="glow-effect"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
      
        <!-- Enhanced Controls -->
        <div class="hero-controls">
            <?php foreach ($hero_slides as $index => $slide): ?>
            <div class="hero-dot <?php echo $index === 0 ? 'active' : ''; ?>" data-slide="<?php echo $index; ?>">
                <div class="dot-progress"></div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="hero-nav prev" id="heroPrev">
            <i class="fas fa-chevron-left"></i>
            <div class="nav-ripple"></div>
        </div>
        <div class="hero-nav next" id="heroNext">
            <i class="fas fa-chevron-right"></i>
            <div class="nav-ripple"></div>
        </div>
        
        <!-- Scroll Indicator -->
        <div class="scroll-indicator">
            <div class="scroll-text">Keşfetmeye Devam Et</div>
            <div class="scroll-arrow">
                <i class="fas fa-chevron-down"></i>
            </div>
        </div>
    </div>
</section>
    <!-- Technology Stats Section -->
    <section class="tech-stats">
        <div class="stats-grid">
            <?php foreach ($tech_stats as $stat): ?>
            <div class="stat-card reveal" style="animation-delay: <?php echo $stat['delay']; ?>s;">
                <div class="stat-icon">
                    <i class="fas fa-<?php echo $stat['icon']; ?>"></i>
                </div>
                <div class="stat-value" data-count="<?php echo $stat['value']; ?>">0</div>
                <div class="stat-label"><?php echo $stat['label']; ?></div>
            </div>
            <?php endforeach; ?>
        </div>
    </section>

    <!-- Modern Categories Section -->
    <section class="categories-section">
        <div class="container">
            <div class="section-header reveal">
                <div class="section-subtitle">Teknoloji Alanlarımız</div>
                <h2 class="section-title">İleri Teknoloji Çözümleri</h2>
                <p class="section-description">
                    Endüstri 4.0 standartlarında, gelişmiş makine teknolojileri ile 
                    üretimde yeni çağı başlatıyoruz. Hassasiyet, kalite ve güvenilirlik.
                </p>
            </div>

            <div class="categories-grid">
                <?php if (!empty($categories)): ?>
                    <?php foreach ($categories as $index => $category): ?>
                    <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo urlencode($category['slug']); ?>" 
                       class="category-card stagger-item" 
                       style="animation-delay: <?php echo $index * 0.1; ?>s;">
                        <div class="category-image">
                            <i class="fas fa-<?php echo $category['icon'] ?? 'cog'; ?>"></i>
                        </div>
                        <div class="category-content">
                            <h3 class="category-title"><?php echo htmlspecialchars($category['name']); ?></h3>
                            <p class="category-description">
                                <?php echo htmlspecialchars($category['description'] ?? 'İleri teknoloji çözümleri'); ?>
                            </p>
                            <span class="category-link">
                                Keşfet <i class="fas fa-arrow-right"></i>
                            </span>
                        </div>
                    </a>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Varsayılan kategoriler -->
                    <?php 
                    $default_categories = [
                        ['name' => 'CNC Teknolojisi', 'icon' => 'microchip', 'desc' => 'Yeni nesil CNC sistemleri ve hassas işleme teknolojileri'],
                        ['name' => 'Lazer Sistemleri', 'icon' => 'laser', 'desc' => 'Fiber lazer kesim ve kaynak sistemleri'],
                        ['name' => 'Robotik Çözümler', 'icon' => 'robot', 'desc' => 'Otomasyon ve robotik üretim sistemleri'],
                        ['name' => 'Akıllı Sistemler', 'icon' => 'brain', 'desc' => 'AI destekli üretim ve izleme sistemleri'],
                        ['name' => 'Hassas İşleme', 'icon' => 'crosshairs', 'desc' => 'Mikron seviyesinde hassas işleme makineleri'],
                        ['name' => 'Endüstri 4.0', 'icon' => 'network-wired', 'desc' => 'IoT entegrasyonu ve akıllı fabrika çözümleri']
                    ];
                    ?>
                    <?php foreach ($default_categories as $index => $category): ?>
                    <div class="category-card stagger-item" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                        <div class="category-image">
                            <i class="fas fa-<?php echo $category['icon']; ?>"></i>
                        </div>
                        <div class="category-content">
                            <h3 class="category-title"><?php echo $category['name']; ?></h3>
                            <p class="category-description"><?php echo $category['desc']; ?></p>
                            <span class="category-link">
                                Keşfet <i class="fas fa-arrow-right"></i>
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="products-section">
        <div class="container">
            <div class="section-header reveal">
                <div class="section-subtitle">Öne Çıkan Teknolojiler</div>
                <h2 class="section-title">Gelişmiş Makine Sistemleri</h2>
                <p class="section-description">
                    En yeni teknolojilerle donatılmış, sektör lideri makine sistemlerimizi keşfedin.
                </p>
            </div>

            <div class="products-grid">
                <?php if (!empty($featured_products)): ?>
                    <?php foreach ($featured_products as $index => $product): ?>
                    <div class="product-card stagger-item" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                        <div class="product-image-container">
                            <?php if (!empty($product['main_image'])): ?>
                                <img src="<?php echo UPLOADS_URL . '/products/' . $product['main_image']; ?>"
                                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     style="width: 100%; height: 100%; object-fit: cover;">
                            <?php else: ?>
                                <i class="fas fa-<?php echo $product['category_icon'] ?? 'cog'; ?>"></i>
                            <?php endif; ?>

                            <?php if ($product['featured']): ?>
                                <div class="product-badge">Yeni Teknoloji</div>
                            <?php endif; ?>
                        </div>

                        <div class="product-content">
                            <?php if (!empty($product['category_name'])): ?>
                                <div class="product-category"><?php echo htmlspecialchars($product['category_name']); ?></div>
                            <?php endif; ?>

                            <h3 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h3>

                            <?php if (!empty($product['short_description'])): ?>
                                <p class="product-description">
                                    <?php echo htmlspecialchars(substr($product['short_description'], 0, 120)) . '...'; ?>
                                </p>
                            <?php endif; ?>

                            <div class="product-footer">
                                <a href="<?php echo BASE_URL; ?>/product-detail.php?slug=<?php echo urlencode($product['slug']); ?>"
                                   class="product-btn">
                                    <i class="fas fa-arrow-right"></i>
                                    İncele
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Varsayılan ürünler -->
                    <?php 
                    $default_products = [
                        ['name' => 'FIBERMAK Gen-5 CNC', 'category' => 'CNC Teknolojisi', 'desc' => 'Yeni nesil hassas işleme teknolojisi', 'icon' => 'microchip'],
                        ['name' => 'LaserTech Pro 4000', 'category' => 'Lazer Sistemleri', 'desc' => 'Gelişmiş fiber lazer kesim sistemi', 'icon' => 'laser'],
                        ['name' => 'RoboArm Elite X1', 'category' => 'Robotik Çözümler', 'desc' => 'AI destekli robotik işleme sistemi', 'icon' => 'robot']
                    ];
                    ?>
                    <?php foreach ($default_products as $index => $product): ?>
                    <div class="product-card stagger-item" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                        <div class="product-image-container">
                            <i class="fas fa-<?php echo $product['icon']; ?>"></i>
                            <div class="product-badge">Yeni Teknoloji</div>
                        </div>
                        <div class="product-content">
                            <div class="product-category"><?php echo $product['category']; ?></div>
                            <h3 class="product-title"><?php echo $product['name']; ?></h3>
                            <p class="product-description"><?php echo $product['desc']; ?></p>
                            <div class="product-footer">
                                <a href="<?php echo BASE_URL; ?>/products.php" class="product-btn">
                                    <i class="fas fa-arrow-right"></i>
                                    İncele
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <div style="text-align: center; margin-top: 4rem;">
                <a href="<?php echo BASE_URL; ?>/products.php" class="hero-cta">
                    <i class="fas fa-th-large"></i>
                    Tüm Teknolojileri Keşfet
                </a>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="geometric-bg">
            <div class="network-lines"></div>
        </div>
        
        <div class="container">
            <div class="cta-content reveal">
                <h2 class="cta-title">Gelecek Teknolojileri Bugün</h2>
                <p class="cta-description">
                    İleri teknoloji çözümlerimizle üretimde yeni standartlar oluşturun. 
                    Uzman ekibimizle projelerinizi hayata geçirin.
                </p>

                <div class="cta-buttons">
                    <a href="<?php echo BASE_URL; ?>/contact.php" class="btn-cta btn-primary-cta">
                        <i class="fas fa-rocket"></i>
                        Projemi Başlat
                    </a>
                    <a href="<?php echo BASE_URL; ?>/about.php" class="btn-cta btn-outline-cta">
                        <i class="fas fa-info-circle"></i>
                        Teknolojilerimizi Keşfet
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Modern JavaScript -->
    <script>
    // Modern Hero Slider Class
    class ModernHeroSlider {
        constructor() {
            this.currentSlide = 0;
            this.slides = document.querySelectorAll('.hero-slide');
            this.dots = document.querySelectorAll('.hero-dot');
            this.prevBtn = document.getElementById('heroPrev');
            this.nextBtn = document.getElementById('heroNext');
            this.autoplayTimer = null;
            this.autoplayDuration = 8000;
            
            this.init();
        }
        
        init() {
            this.bindEvents();
            this.startAutoplay();
        }
        
        bindEvents() {
            this.prevBtn?.addEventListener('click', () => this.prevSlide());
            this.nextBtn?.addEventListener('click', () => this.nextSlide());
            
            this.dots.forEach((dot, index) => {
                dot.addEventListener('click', () => this.goToSlide(index));
            });
            
            // Pause on hover
            const heroSection = document.getElementById('hero');
            heroSection?.addEventListener('mouseenter', () => this.pauseAutoplay());
            heroSection?.addEventListener('mouseleave', () => this.startAutoplay());
        }
        
        goToSlide(index) {
            this.slides[this.currentSlide].classList.remove('active');
            this.dots[this.currentSlide].classList.remove('active');
            
            this.currentSlide = index;
            
            this.slides[this.currentSlide].classList.add('active');
            this.dots[this.currentSlide].classList.add('active');
        }
        
        nextSlide() {
            const nextIndex = (this.currentSlide + 1) % this.slides.length;
            this.goToSlide(nextIndex);
        }
        
        prevSlide() {
            const prevIndex = this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1;
            this.goToSlide(prevIndex);
        }
        
        startAutoplay() {
            this.pauseAutoplay();
            this.autoplayTimer = setInterval(() => {
                this.nextSlide();
            }, this.autoplayDuration);
        }
        
        pauseAutoplay() {
            if (this.autoplayTimer) {
                clearInterval(this.autoplayTimer);
                this.autoplayTimer = null;
            }
        }
    }

    // Advanced Number Counter
    class NumberCounter {
        constructor(element) {
            this.element = element;
            this.target = this.element.getAttribute('data-count');
            this.hasStarted = false;
        }
        
        start() {
            if (this.hasStarted) return;
            this.hasStarted = true;
            
            if (this.target === '24/7') {
                this.animateText('24/7');
                return;
            }
            
            const isPercent = this.target.includes('%');
            const hasPlus = this.target.includes('+');
            const numericTarget = parseFloat(this.target.replace(/[^\d.]/g, ''));
            
            let current = 0;
            const increment = numericTarget / 60;
            const duration = 2000;
            const stepTime = duration / 60;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= numericTarget) {
                    current = numericTarget;
                    clearInterval(timer);
                }
                
                let displayValue = Math.floor(current * 10) / 10;
                if (isPercent) {
                    this.element.textContent = displayValue + '%';
                } else if (hasPlus) {
                    this.element.textContent = Math.floor(displayValue) + '+';
                } else {
                    this.element.textContent = Math.floor(displayValue);
                }
            }, stepTime);
        }
        
        animateText(text) {
            let index = 0;
            const timer = setInterval(() => {
                this.element.textContent = text.substring(0, index + 1);
                index++;
                if (index >= text.length) {
                    clearInterval(timer);
                }
            }, 100);
        }
    }

    // Intersection Observer for Animations
    class AnimationObserver {
        constructor() {
            this.observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };
            
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        
                        // Trigger counters for stats
                        if (entry.target.classList.contains('stat-card')) {
                            const counter = new NumberCounter(entry.target.querySelector('.stat-value'));
                            setTimeout(() => counter.start(), Math.random() * 500);
                        }
                        
                        // Stagger animations
                        if (entry.target.classList.contains('stagger-item')) {
                            const delay = parseFloat(entry.target.style.animationDelay) * 1000 || 0;
                            setTimeout(() => {
                                entry.target.classList.add('visible');
                            }, delay);
                        }
                    }
                });
            }, this.observerOptions);
            
            this.init();
        }
        
        init() {
            const animatedElements = document.querySelectorAll('.reveal, .stat-card, .stagger-item');
            animatedElements.forEach(el => this.observer.observe(el));
        }
    }

    // Page Loader
    class PageLoader {
        constructor() {
            this.loader = document.getElementById('pageLoader');
            this.init();
        }
        
        init() {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    this.loader.classList.add('hidden');
                    document.body.style.overflow = 'visible';
                }, 1000);
            });
            
            // Hide body overflow until loaded
            document.body.style.overflow = 'hidden';
        }
    }

    // Smooth Scrolling
    function initSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // Initialize Everything
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize all components
        new PageLoader();
        new ModernHeroSlider();
        new AnimationObserver();
        initSmoothScrolling();
        
        // Add loading complete class
        setTimeout(() => {
            document.body.classList.add('loaded');
        }, 1500);
        
        console.log('🚀 GNG Makine - Modern Interface Loaded');
    });

    // Performance monitoring
    window.addEventListener('load', function() {
        if ('performance' in window) {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('⚡ Page Load Time:', Math.round(perfData.loadEventEnd - perfData.loadEventStart) + 'ms');
        }
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') {
            document.getElementById('heroPrev')?.click();
        } else if (e.key === 'ArrowRight') {
            document.getElementById('heroNext')?.click();
        }
    });
    </script>

    <?php include 'includes/footer.php'; ?>
</body>
</html>