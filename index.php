<script>
// Hero Slider Variables
let currentSlide = 0;
let isAutoPlay = true;
let autoPlayInterval;
const slides = document.querySelectorAll('.hero-slide');
const dots = document.querySelectorAll('.hero-dot');
const totalSlides = slides.length;

// Tech Particles Animation
function createTechParticles() {
    const particlesContainer = document.getElementById('techParticles');
    const particleCount = 30;

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'tech-particle';
        
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 15 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
        
        particlesContainer.appendChild(particle);
    }
}

// Binary Background
function createBinaryBackground() {
    const binaryBg = document.getElementById('binaryBg');
    const binaryCount = 20;
    
    for (let i = 0; i < binaryCount; i++) {
        const binaryLine = document.createElement('div');
        binaryLine.className = 'binary-line';
        
        let binaryText = '';
        for (let j = 0; j < 100; j++) {
            binaryText += Math.random() > 0.5 ? '1' : '0';
        }
        
        binaryLine.textContent = binaryText;
        binaryLine.style.left = Math.random() * 100 + '%';
        binaryLine.style.animationDelay = Math.random() * 20 + 's';
        binaryLine.style.animationDuration = (Math.random() * 15 + 15) + 's';
        
        binaryBg.appendChild(binaryLine);
    }
}

// Hero Slider Functions
function showSlide(index) {
    // Reset all slides
    slides.forEach((slide, i) => {
        slide.classList.remove('active', 'prev');
        if (i === index) {
            slide.classList.add('active');
        } else if (i < index) {
            slide.classList.add('prev');
        }
    });
    
    // Update dots
    dots.forEach((dot, i) => {
        dot.classList.toggle('active', i === index);
    });
    
    currentSlide = index;
    
    // Reset progress bar
    const progressBar = document.getElementById('heroProgress');
    progressBar.style.width = '0%';
    
    // Restart<?php
/**
 * GNG Makine - Modern Animasyonlu Ana Sayfa
 */

// Konfigürasyonu dahil et
require_once __DIR__ . '/config/config.php';

// Sayfa meta bilgileri
$page_title = "Ana Sayfa - " . SITE_NAME;
$page_description = "Endüstriyel makine sektöründe 15+ yıllık tecrübemizle kaliteli çözümler sunuyoruz. CNC, torna, freze, kaynak makineleri ve daha fazlası.";
$page_keywords = "endüstriyel makine, CNC makine, torna, freze, kaynak makinesi, GNG Makine";
$current_page = "home";

// Veritabanından verileri çek
try {
    // Öne çıkan ürünler
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name, c.slug as category_slug
                          FROM products p
                          LEFT JOIN categories c ON p.category_id = c.id
                          WHERE p.status = 'active' AND p.featured = 1
                          ORDER BY p.sort_order ASC, p.created_at DESC LIMIT 8");
    $stmt->execute();
    $featured_products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Kategoriler
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE status = 'active' ORDER BY sort_order ASC LIMIT 6");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Son blog yazıları
    $stmt = $pdo->prepare("SELECT * FROM articles WHERE status = 'published' ORDER BY created_at DESC LIMIT 3");
    $stmt->execute();
    $latest_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Son mesajlar
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM contact_messages WHERE status = 'unread'");
    $stmt->execute();
    $unread_messages = $stmt->fetch()['total'];

    // Site istatistikleri
    $stats = [
        'experience' => '15+',
        'customers' => '500+',
        'projects' => '1000+',
        'support' => '24/7'
    ];

} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    $featured_products = [];
    $categories = [];
    $latest_articles = [];
    $unread_messages = 0;
    $stats = [
        'experience' => '15+',
        'customers' => '500+',
        'projects' => '1000+',
        'support' => '24/7'
    ];
}

// Hizmetler verisi
$services = [
    [
        'icon' => 'fas fa-shopping-cart',
        'title' => 'Makine Satışı',
        'description' => 'Yeni ve ikinci el makine seçenekleri'
    ],
    [
        'icon' => 'fas fa-tools',
        'title' => 'Teknik Destek',
        'description' => '7/24 uzman teknisyen desteği'
    ],
    [
        'icon' => 'fas fa-wrench',
        'title' => 'Bakım & Onarım',
        'description' => 'Profesyonel bakım ve onarım hizmetleri'
    ],
    [
        'icon' => 'fas fa-cogs',
        'title' => 'Yedek Parça',
        'description' => 'Geniş yedek parça stoğu'
    ]
];

// Hero içeriği - Makine ve teknoloji temalı kaydırmalı içerik
$hero_slides = [
    [
        'title' => 'CNC Torna <span class="highlight">Teknolojisi</span>',
        'text' => 'Yüksek hassasiyetli CNC torna makineleri ile mikron seviyesinde işleme kalitesi. Endüstriyel üretimde mükemmellik standartları.',
        'icon' => 'fas fa-cog',
        'image' => 'cnc-torna.jpg'
    ],
    [
        'title' => 'Freze Makine <span class="highlight">Çözümleri</span>',
        'text' => 'Çok eksenli freze makineleri ile karmaşık geometrilerde işleme imkanı. Modern üretim süreçleriniz için güçlü çözümler.',
        'icon' => 'fas fa-tools',
        'image' => 'freze-makine.jpg'
    ],
    [
        'title' => 'Kaynak <span class="highlight">Teknolojileri</span>',
        'text' => 'Gelişmiş kaynak sistemleri ile dayanıklı bağlantılar. Robotik kaynak çözümleri ve otomatik sistemler.',
        'icon' => 'fas fa-fire',
        'image' => 'kaynak-makine.jpg'
    ],
    [
        'title' => 'Endüstri 4.0 <span class="highlight">Çözümleri</span>',
        'text' => 'Akıllı fabrika sistemleri ve IoT entegrasyon çözümleri. Gelecekteki üretim süreçlerinizi bugün hayata geçirin.',
        'icon' => 'fas fa-microchip',
        'image' => 'endustri-4.jpg'
    ]
// Hero içeriği - Makine ve teknoloji temalı kaydırmalı içerik
$hero_slides = [
    [
        'title' => 'CNC Torna <span class="highlight">Teknolojisi</span>',
        'text' => 'Yüksek hassasiyetli CNC torna makineleri ile mikron seviyesinde işleme kalitesi. Endüstriyel üretimde mükemmellik standartları.',
        'icon' => 'fas fa-cog',
        'image' => 'cnc-torna.jpg'
    ],
    [
        'title' => 'Freze Makine <span class="highlight">Çözümleri</span>',
        'text' => 'Çok eksenli freze makineleri ile karmaşık geometrilerde işleme imkanı. Modern üretim süreçleriniz için güçlü çözümler.',
        'icon' => 'fas fa-tools',
        'image' => 'freze-makine.jpg'
    ],
    [
        'title' => 'Kaynak <span class="highlight">Teknolojileri</span>',
        'text' => 'Gelişmiş kaynak sistemleri ile dayanıklı bağlantılar. Robotik kaynak çözümleri ve otomatik sistemler.',
        'icon' => 'fas fa-fire',
        'image' => 'kaynak-makine.jpg'
    ],
    [
        'title' => 'Endüstri 4.0 <span class="highlight">Çözümleri</span>',
        'text' => 'Akıllı fabrika sistemleri ve IoT entegrasyon çözümleri. Gelecekteki üretim süreçlerinizi bugün hayata geçirin.',
        'icon' => 'fas fa-microchip',
        'image' => 'endustri-4.jpg'
    ]
];

// Sertifikalar
$certificates = [
    'ISO 9001:2015 Kalite Yönetim Sistemi',
    'ISO 14001:2015 Çevre Yönetim Sistemi',
    'OHSAS 18001 İş Sağlığı ve Güvenliği',
    'CE Uygunluk Beyanı',
    'TSE Hizmet Yeterlilik Belgesi',
    'Kaizen Sürekli İyileştirme Sertifikası'
];

include 'includes/header.php';
?>

<style>
:root {
    --primary: #F9B233;
    --primary-dark: #e6a02e;
    --secondary: #8A8C8F;
    --accent: #e67e22;
    --dark: #2C3E50;
    --light: #ECF0F1;
    --white: #FFFFFF;
    --text-dark: #2C3E50;
    --text-light: #7F8C8D;
    --shadow: 0 10px 30px rgba(0,0,0,0.1);
    --shadow-hover: 0 20px 40px rgba(0,0,0,0.15);
    --transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
    --gradient-dark: linear-gradient(135deg, var(--dark) 0%, #34495e 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section - Kaydırmalı Makine Teması */
.hero {
    height: 100vh;
    background: var(--gradient-dark);
    color: var(--white);
    position: relative;
    overflow: hidden;
    margin-top: -80px;
    padding-top: 80px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(249, 178, 51, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(230, 126, 34, 0.1) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(249, 178, 51, 0.05) 50%, transparent 70%);
    animation: backgroundShift 10s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-10px) translateY(-5px); }
    50% { transform: translateX(5px) translateY(-10px); }
    75% { transform: translateX(-5px) translateY(5px); }
}

.hero-slider {
    position: relative;
    height: 100%;
    overflow: hidden;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    opacity: 0;
    transform: translateX(100%);
    transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hero-slide.active {
    opacity: 1;
    transform: translateX(0);
}

.hero-slide.prev {
    transform: translateX(-100%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    width: 100%;
}

.hero-text {
    opacity: 0;
    transform: translateX(-80px);
    animation: slideInLeft 1.2s ease 0.3s forwards;
}

.hero-slide.active .hero-text {
    animation: slideInLeft 1.2s ease 0.3s forwards;
}

@keyframes slideInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hero-text h1 {
    font-size: clamp(2.8rem, 5vw, 4.2rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 30px;
    background: linear-gradient(45deg, var(--white), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-text .highlight {
    position: relative;
    display: inline-block;
    color: var(--primary);
    -webkit-text-fill-color: var(--primary);
}

.hero-text .highlight::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--primary);
    animation: underlineExpand 1.5s ease 2s forwards;
}

@keyframes underlineExpand {
    to { width: 100%; }
}

.hero-text p {
    font-size: 1.3rem;
    line-height: 1.7;
    margin-bottom: 40px;
    opacity: 0.95;
    color: #ecf0f1;
}

.hero-icon {
    position: absolute;
    top: -20px;
    right: -20px;
    width: 80px;
    height: 80px;
    background: rgba(249, 178, 51, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--primary);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(249, 178, 51, 0.3);
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.hero-visual {
    position: relative;
    opacity: 0;
    transform: translateY(-80px);
    animation: slideInTop 1.2s ease 0.6s forwards;
}

.hero-slide.active .hero-visual {
    animation: slideInTop 1.2s ease 0.6s forwards;
}

@keyframes slideInTop {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-image {
    width: 100%;
    max-width: 550px;
    height: 450px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.hero-image::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg, 
        transparent, 
        rgba(249, 178, 51, 0.1), 
        transparent,
        rgba(230, 126, 34, 0.1),
        transparent
    );
    animation: shimmerRotate 4s linear infinite;
}

@keyframes shimmerRotate {
    0% { transform: translateX(-100%) translateY(-100%) rotate(0deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(360deg); }
}

.hero-image img {
    width: 90%;
    height: 90%;
    object-fit: cover;
    border-radius: 20px;
    z-index: 1;
    position: relative;
}

.hero-image .placeholder-icon {
    font-size: 6rem;
    color: var(--primary);
    z-index: 1;
    position: relative;
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Slider Controls */
.hero-controls {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    z-index: 10;
}

.hero-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.6);
    cursor: pointer;
    transition: all 0.3s ease;
}

.hero-dot.active {
    background: var(--primary);
    border-color: var(--primary);
    transform: scale(1.3);
}

.hero-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    z-index: 10;
}

.hero-nav:hover {
    background: rgba(249, 178, 51, 0.3);
    border-color: var(--primary);
    transform: translateY(-50%) scale(1.1);
}

.hero-nav.prev {
    left: 30px;
}

.hero-nav.next {
    right: 30px;
}

/* Auto-play progress bar */
.hero-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--primary);
    width: 0%;
    transition: width 0.1s linear;
    z-index: 10;
}

/* Particle effects for tech theme */
.tech-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.tech-particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary);
    border-radius: 50%;
    opacity: 0.6;
    animation: techFloat 15s linear infinite;
}

@keyframes techFloat {
    0% {
        transform: translateY(100vh) translateX(0) rotate(0deg);
        opacity: 0;
    }
    10% { opacity: 0.6; }
    90% { opacity: 0.6; }
    100% {
        transform: translateY(-100px) translateX(200px) rotate(360deg);
        opacity: 0;
    }
}

/* Binary code background effect */
.binary-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.05;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 20px;
    color: var(--primary);
    overflow: hidden;
    z-index: 1;
}

.binary-line {
    position: absolute;
    animation: binaryScroll 20s linear infinite;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: 0 10px 30px rgba(249, 178, 51, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(249, 178, 51, 0.4);
}

.btn-outline {
    background: transparent;
    color: var(--white);
    border-color: var(--white);
}

.btn-outline:hover {
    background: var(--white);
    color: var(--dark);
    transform: translateY(-3px);
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 20px;
}

/* Stats Section */
.stats-section {
    padding: 80px 0;
    background: var(--gradient-primary);
    color: var(--white);
    position: relative;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    text-align: center;
}

.stat-item {
    padding: 40px 20px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--white);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-item:hover::before {
    transform: scaleX(1);
}

.stat-item:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.25);
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 900;
    display: block;
    margin-bottom: 15px;
    opacity: 0;
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 600;
    opacity: 0.9;
}

/* Categories Section */
.categories-section {
    padding: 120px 0;
    background: var(--white);
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-header h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    color: var(--text-dark);
    margin-bottom: 20px;
    position: relative;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.category-card {
    background: var(--white);
    border-radius: 25px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 2px solid transparent;
    text-decoration: none;
    color: inherit;
    position: relative;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.category-card:hover::before {
    opacity: 0.1;
}

.category-card:hover {
    transform: translateY(-15px) scale(1.02);
    border-color: var(--primary);
    box-shadow: var(--shadow-hover);
}

.category-image {
    height: 220px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: var(--primary);
    position: relative;
    overflow: hidden;
}

.category-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0.1;
    transform: scale(0);
    transition: transform 0.3s ease;
}

.category-card:hover .category-image::after {
    transform: scale(1);
}

.category-info {
    padding: 35px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.category-info h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 15px;
    transition: color 0.3s ease;
}

.category-card:hover .category-info h3 {
    color: var(--primary);
}

.category-info p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 25px;
}

.category-link {
    color: var(--primary);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
}

.category-link:hover {
    color: var(--accent);
    transform: translateX(5px);
}

/* Products Section */
.products-section {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
}

.product-card {
    background: var(--white);
    border-radius: 25px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 2px solid transparent;
    position: relative;
}

.product-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.product-card:hover::after {
    opacity: 0.05;
}

.product-card:hover {
    transform: translateY(-15px) scale(1.02);
    border-color: var(--primary);
    box-shadow: var(--shadow-hover);
}

.product-image {
    height: 250px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3.5rem;
    color: var(--primary);
    position: relative;
    overflow: hidden;
}

.product-badge {
    position: absolute;
    top: 20px;
    left: 20px;
    background: var(--gradient-primary);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.product-info {
    padding: 30px;
    position: relative;
    z-index: 2;
}

.product-category {
    color: var(--primary);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 15px;
    line-height: 1.3;
    transition: color 0.3s ease;
}

.product-card:hover .product-title {
    color: var(--primary);
}

.product-description {
    color: var(--text-light);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 25px;
}

.product-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-btn {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 600;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.product-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.product-btn:hover::before {
    left: 100%;
}

.product-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(249, 178, 51, 0.3);
}

/* Services Overview */
.services-overview {
    padding: 120px 0;
    background: var(--white);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.service-card {
    background: var(--white);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 2px solid transparent;
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary);
    box-shadow: var(--shadow-hover);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 2rem;
    margin: 0 auto 25px;
    transition: var(--transition);
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
}

.service-card h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.service-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Certificates Section */
.certificates-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.certificates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 60px;
}

.certificate-item {
    background: var(--white);
    padding: 25px;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border-left: 4px solid var(--primary);
    display: flex;
    align-items: center;
}

.certificate-item:hover {
    transform: translateX(10px);
}

.certificate-item i {
    color: var(--primary);
    font-size: 1.5rem;
    margin-right: 15px;
}

.certificate-item span {
    font-weight: 600;
    color: var(--text-dark);
}

/* Blog Section */
.blog-section {
    padding: 120px 0;
    background: var(--white);
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.blog-card {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.blog-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.blog-image {
    height: 200px;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--white);
}

.blog-content {
    padding: 30px;
}

.blog-date {
    color: var(--primary);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.blog-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 15px;
    line-height: 1.4;
}

.blog-excerpt {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 20px;
}

.blog-link {
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
}

.blog-link:hover {
    color: var(--accent);
    transform: translateX(5px);
}

/* CTA Section */
.cta-section {
    padding: 120px 0;
    background: var(--gradient-dark);
    color: var(--white);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    animation: float 20s ease-in-out infinite;
}

.cta-content {
    position: relative;
    z-index: 2;
}

.cta-content h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    margin-bottom: 25px;
    background: linear-gradient(45deg, var(--white), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cta-content p {
    font-size: 1.3rem;
    margin-bottom: 50px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 25px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Scroll Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s ease;
}

.scale-in.visible {
    opacity: 1;
    transform: scale(1);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 50px;
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .hero-buttons,
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .categories-grid,
    .products-grid,
    .services-grid,
    .blog-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .certificates-grid {
        grid-template-columns: 1fr;
    }
}

/* Advanced Hover Effects */
.magnetic-btn {
    transition: transform 0.1s ease;
}

.tilt-card {
    transform-style: preserve-3d;
    transition: transform 0.1s ease;
}

/* Glassmorphism Effects */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Gradient Text */
.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}
</style>

<?php include 'includes/navbar.php'; ?>

<!-- Hero Section with Tech Slider -->
<section class="hero" id="hero">
    <div class="tech-particles" id="techParticles"></div>
    <div class="binary-bg" id="binaryBg"></div>
    
    <div class="container">
        <div class="hero-slider" id="heroSlider">
            <?php foreach ($hero_slides as $index => $slide): ?>
            <div class="hero-slide <?php echo $index === 0 ? 'active' : ''; ?>" data-slide="<?php echo $index; ?>">
                <div class="hero-content">
                    <div class="hero-text">
                        <div class="hero-icon">
                            <i class="<?php echo $slide['icon']; ?>"></i>
                        </div>
                        <h1><?php echo $slide['title']; ?></h1>
                        <p><?php echo $slide['text']; ?></p>
                        
                        <div class="hero-buttons">
                            <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-primary magnetic-btn">
                                <i class="fas fa-search"></i>
                                Ürünleri İncele
                            </a>
                            <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-outline magnetic-btn">
                                <i class="fas fa-phone"></i>
                                İletişime Geç
                            </a>
                        </div>
                    </div>

                    <div class="hero-visual">
                        <div class="hero-image glass">
                            <?php if (!empty($slide['image'])): ?>
                                <img src="<?php echo UPLOADS_URL; ?>/hero/<?php echo $slide['image']; ?>" 
                                     alt="<?php echo strip_tags($slide['title']); ?>">
                            <?php else: ?>
                                <div class="placeholder-icon">
                                    <i class="<?php echo $slide['icon']; ?>"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Slider Controls -->
        <div class="hero-controls">
            <?php foreach ($hero_slides as $index => $slide): ?>
            <div class="hero-dot <?php echo $index === 0 ? 'active' : ''; ?>" data-slide="<?php echo $index; ?>"></div>
            <?php endforeach; ?>
        </div>
        
        <div class="hero-nav prev" id="heroPrev">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="hero-nav next" id="heroNext">
            <i class="fas fa-chevron-right"></i>
        </div>
        
        <div class="hero-progress" id="heroProgress"></div>
    </div>
</section>

<!-- Animated Stats Section -->
<section class="stats-section">
    <div class="container">
        <div class="stats-grid">
            <?php foreach ($stats as $key => $value): ?>
            <div class="stat-item glass tilt-card">
                <span class="stat-number" data-count="<?php echo $value; ?>">0</span>
                <div class="stat-label">
                    <?php 
                    $labels = [
                        'experience' => 'Yıllık Tecrübe',
                        'customers' => 'Mutlu Müşteri',
                        'projects' => 'Tamamlanan Proje',
                        'support' => 'Teknik Destek'
                    ];
                    echo $labels[$key];
                    ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Modern Categories Section -->
<section class="categories-section fade-in">
    <div class="container">
        <div class="section-header">
            <h2>Ürün <span class="gradient-text">Kategorilerimiz</span></h2>
            <p>Geniş ürün yelpazemizle endüstriyel ihtiyaçlarınıza uygun çözümler sunuyoruz.</p>
        </div>

        <div class="categories-grid">
            <?php if (!empty($categories)): ?>
                <?php foreach ($categories as $index => $category): ?>
                <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo urlencode($category['slug']); ?>" 
                   class="category-card tilt-card fade-in" 
                   style="animation-delay: <?php echo $index * 0.1; ?>s;">
                    <div class="category-image">
                        <i class="fas fa-<?php echo $category['icon'] ?? 'cog'; ?>"></i>
                    </div>
                    <div class="category-info">
                        <h3><?php echo htmlspecialchars($category['name']); ?></h3>
                        <p><?php echo htmlspecialchars($category['description'] ?? 'Kategori açıklaması'); ?></p>
                        <span class="category-link">
                            Ürünleri Gör <i class="fas fa-arrow-right"></i>
                        </span>
                    </div>
                </a>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- Varsayılan kategoriler -->
                <a href="<?php echo BASE_URL; ?>/products.php?category=cnc-torna" class="category-card tilt-card">
                    <div class="category-image">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="category-info">
                        <h3>CNC Torna</h3>
                        <p>Hassas işleme için CNC torna makineleri</p>
                        <span class="category-link">
                            Ürünleri Gör <i class="fas fa-arrow-right"></i>
                        </span>
                    </div>
                </a>

                <a href="<?php echo BASE_URL; ?>/products.php?category=freze" class="category-card tilt-card">
                    <div class="category-image">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="category-info">
                        <h3>Freze Makineleri</h3>
                        <p>Çok amaçlı freze makineleri</p>
                        <span class="category-link">
                            Ürünleri Gör <i class="fas fa-arrow-right"></i>
                        </span>
                    </div>
                </a>

                <a href="<?php echo BASE_URL; ?>/products.php?category=kaynak" class="category-card tilt-card">
                    <div class="category-image">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="category-info">
                        <h3>Kaynak Makineleri</h3>
                        <p>Profesyonel kaynak ekipmanları</p>
                        <span class="category-link">
                            Ürünleri Gör <i class="fas fa-arrow-right"></i>
                        </span>
                    </div>
                </a>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="products-section fade-in">
    <div class="container">
        <div class="section-header">
            <h2>Öne Çıkan <span class="gradient-text">Ürünlerimiz</span></h2>
            <p>En çok tercih edilen ve kaliteli ürünlerimizi keşfedin.</p>
        </div>

        <div class="products-grid">
            <?php if (!empty($featured_products)): ?>
                <?php foreach ($featured_products as $index => $product): ?>
                <div class="product-card tilt-card scale-in" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                    <div class="product-image">
                        <?php if (!empty($product['main_image'])): ?>
                            <img src="<?php echo UPLOADS_URL . '/products/' . $product['main_image']; ?>"
                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                 style="width: 100%; height: 100%; object-fit: cover;">
                        <?php else: ?>
                            <i class="fas fa-cog"></i>
                        <?php endif; ?>

                        <?php if ($product['featured']): ?>
                            <div class="product-badge">Öne Çıkan</div>
                        <?php endif; ?>
                    </div>

                    <div class="product-info">
                        <?php if (!empty($product['category_name'])): ?>
                            <div class="product-category"><?php echo htmlspecialchars($product['category_name']); ?></div>
                        <?php endif; ?>

                        <h3 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h3>

                        <?php if (!empty($product['short_description'])): ?>
                            <p class="product-description"><?php echo htmlspecialchars(substr($product['short_description'], 0, 120)) . '...'; ?></p>
                        <?php endif; ?>

                        <div class="product-footer">
                            <a href="<?php echo BASE_URL; ?>/product-detail.php?slug=<?php echo urlencode($product['slug']); ?>"
                               class="product-btn">
                                Detayları Gör
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- Varsayılan ürünler -->
                <div class="product-card tilt-card">
                    <div class="product-image">
                        <i class="fas fa-cog"></i>
                        <div class="product-badge">Öne Çıkan</div>
                    </div>
                    <div class="product-info">
                        <div class="product-category">CNC Torna</div>
                        <h3 class="product-title">CNC Torna Makinesi</h3>
                        <p class="product-description">Yüksek hassasiyetli CNC torna makinesi</p>
                        <div class="product-footer">
                            <a href="<?php echo BASE_URL; ?>/products.php" class="product-btn">
                                Detayları Gör
                            </a>
                        </div>
                    </div>
                </div>

                <div class="product-card tilt-card">
                    <div class="product-image">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-category">Freze</div>
                        <h3 class="product-title">Freze Makinesi</h3>
                        <p class="product-description">Çok amaçlı freze makinesi</p>
                        <div class="product-footer">
                            <a href="<?php echo BASE_URL; ?>/products.php" class="product-btn">
                                Detayları Gör
                            </a>
                        </div>
                    </div>
                </div>

                <div class="product-card tilt-card">
                    <div class="product-image">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-category">Kaynak</div>
                        <h3 class="product-title">Kaynak Makinesi</h3>
                        <p class="product-description">Profesyonel kaynak ekipmanı</p>
                        <div class="product-footer">
                            <a href="<?php echo BASE_URL; ?>/products.php" class="product-btn">
                                Detayları Gör
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <div style="text-align: center; margin-top: 50px;">
            <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-primary magnetic-btn">
                <i class="fas fa-th-large"></i>
                Tüm Ürünleri Gör
            </a>
        </div>
    </div>
</section>

<!-- Services Overview -->
<section class="services-overview fade-in">
    <div class="container">
        <div class="section-header">
            <h2>Hizmet <span class="gradient-text">Alanlarımız</span></h2>
            <p>Kapsamlı hizmet yelpazemizle endüstriyel ihtiyaçlarınıza çözüm üretiyoruz.</p>
        </div>

        <div class="services-grid">
            <?php foreach ($services as $index => $service): ?>
            <div class="service-card tilt-card fade-in" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                <div class="service-icon">
                    <i class="<?php echo $service['icon']; ?>"></i>
                </div>
                <h3><?php echo htmlspecialchars($service['title']); ?></h3>
                <p><?php echo htmlspecialchars($service['description']); ?></p>
            </div>
            <?php endforeach; ?>
        </div>

        <div style="text-align: center; margin-top: 50px;">
            <a href="<?php echo BASE_URL; ?>/services.php" class="btn btn-primary magnetic-btn">
                <i class="fas fa-arrow-right"></i>
                Tüm Hizmetleri Gör
            </a>
        </div>
    </div>
</section>

<!-- Certificates Section -->
<section class="certificates-section fade-in">
    <div class="container">
        <div class="section-header">
            <h2>Kalite <span class="gradient-text">Sertifikalarımız</span></h2>
            <p>Uluslararası standartlara uygunluğumuzu belgeleyen sertifikalarımız.</p>
        </div>

        <div class="certificates-grid">
            <?php foreach ($certificates as $index => $certificate): ?>
            <div class="certificate-item slide-in-left" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                <i class="fas fa-certificate"></i>
                <span><?php echo htmlspecialchars($certificate); ?></span>
            </div>
            <?php endforeach; ?>
        </div>

        <div style="text-align: center; margin-top: 50px;">
            <a href="<?php echo BASE_URL; ?>/certificates.php" class="btn btn-primary magnetic-btn">
                <i class="fas fa-award"></i>
                Tüm Sertifikaları Gör
            </a>
        </div>
    </div>
</section>

<!-- Blog Section -->
<?php if (!empty($latest_articles)): ?>
<section class="blog-section fade-in">
    <div class="container">
        <div class="section-header">
            <h2>Son <span class="gradient-text">Haberler</span></h2>
            <p>Sektördeki gelişmeler ve güncel içeriklerimiz.</p>
        </div>

        <div class="blog-grid">
            <?php foreach ($latest_articles as $index => $article): ?>
            <article class="blog-card tilt-card scale-in" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                <div class="blog-image">
                    <?php if (!empty($article['featured_image'])): ?>
                        <img src="<?php echo UPLOADS_URL . '/articles/' . $article['featured_image']; ?>"
                             alt="<?php echo htmlspecialchars($article['title']); ?>"
                             style="width: 100%; height: 100%; object-fit: cover;">
                    <?php else: ?>
                        <i class="fas fa-newspaper"></i>
                    <?php endif; ?>
                </div>
                <div class="blog-content">
                    <div class="blog-date">
                        <i class="fas fa-calendar-alt"></i>
                        <?php echo date('d.m.Y', strtotime($article['created_at'])); ?>
                    </div>
                    <h3 class="blog-title"><?php echo htmlspecialchars($article['title']); ?></h3>
                    <?php if (!empty($article['excerpt'])): ?>
                        <p class="blog-excerpt"><?php echo htmlspecialchars(substr($article['excerpt'], 0, 120)) . '...'; ?></p>
                    <?php endif; ?>
                    <a href="<?php echo BASE_URL; ?>/article.php?slug=<?php echo urlencode($article['slug']); ?>" class="blog-link">
                        Devamını Oku <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </article>
            <?php endforeach; ?>
        </div>

        <div style="text-align: center; margin-top: 50px;">
            <a href="<?php echo BASE_URL; ?>/academy.php" class="btn btn-primary magnetic-btn">
                <i class="fas fa-graduation-cap"></i>
                Tüm Makaleleri Gör
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="cta-content fade-in">
            <h2>Projeleriniz İçin Doğru Çözüm</h2>
            <p>Endüstriyel makine ihtiyaçlarınız için uzman ekibimizle iletişime geçin. Size özel çözümler sunalım.</p>

            <div class="cta-buttons">
                <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-primary magnetic-btn">
                    <i class="fas fa-phone"></i>
                    Hemen İletişime Geçin
                </a>
                <a href="<?php echo BASE_URL; ?>/about.php" class="btn btn-outline magnetic-btn">
                    <i class="fas fa-info-circle"></i>
                    Hakkımızda
                </a>
            </div>
        </div>
    </div>
</section>

<script>
// Particles Animation
function createParticles() {
    const particlesContainer = document.getElementById('particles');
    const particleCount = 50;

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        const size = Math.random() * 4 + 2;
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 15 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
        
        particlesContainer.appendChild(particle);
    }
}

// Number Counter Animation
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');
    
    counters.forEach(counter => {
        const target = counter.getAttribute('data-count');
        const isPercent = target.includes('+');
        const numericTarget = parseInt(target.replace(/[^\d]/g, ''));
        let current = 0;
        const increment = numericTarget / 100;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= numericTarget) {
                current = numericTarget;
                clearInterval(timer);
            }
            
            if (isPercent) {
                counter.textContent = Math.floor(current) + '+';
            } else if (target === '24/7') {
                counter.textContent = '24/7';
            } else {
                counter.textContent = Math.floor(current) + '+';
            }
        }, 50);
    });
}

// Intersection Observer for Scroll Animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('visible');
            
            // Trigger counter animation for stats section
            if (entry.target.classList.contains('stats-section')) {
                animateCounters();
            }
        }
    });
}, observerOptions);

// Observe all animated elements
document.addEventListener('DOMContentLoaded', function() {
    const animatedElements = document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right, .scale-in, .stats-section');
    animatedElements.forEach(el => observer.observe(el));
    
    createParticles();
});

// Magnetic Button Effect
document.querySelectorAll('.magnetic-btn').forEach(btn => {
    btn.addEventListener('mousemove', function(e) {
        const rect = this.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;
        
        this.style.transform = `translate(${x * 0.3}px, ${y * 0.3}px)`;
    });
    
    btn.addEventListener('mouseleave', function() {
        this.style.transform = 'translate(0, 0)';
    });
});

// 3D Tilt Effect for Cards
document.querySelectorAll('.tilt-card').forEach(card => {
    card.addEventListener('mousemove', function(e) {
        const rect = this.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        const rotateX = (y - centerY) / 10;
        const rotateY = (centerX - x) / 10;
        
        this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.05, 1.05, 1.05)`;
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) scale3d(1, 1, 1)';
    });
});

// Smooth Scroll for Internal Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Parallax Effect for Hero
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const rate = scrolled * -0.5;
    
    const hero = document.getElementById('hero');
    if (hero) {
        hero.style.transform = `translateY(${rate}px)`;
    }
});

// Enhanced Page Transitions
window.addEventListener('beforeunload', function() {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.3s ease';
});

// Performance optimization for animations
const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');

if (prefersReducedMotion.matches) {
    // Disable animations for users who prefer reduced motion
    document.documentElement.style.setProperty('--transition', 'none');
}
</script>

<?php include 'includes/footer.php'; ?>repeat(2, 1fr);
        gap: 30px;
    }

    .categories-grid,
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
    }
}

@media (max-width: 768px) {
    .hero {
        height: auto;
        min-height: 100vh;
        padding: 80px 0;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .stats-grid {
        grid-template-columns: <?php
/**
 * GNG Makine - Modern Animasyonlu Ana Sayfa
 */

// Konfigürasyonu dahil et
require_once __DIR__ . '/config/config.php';

// Sayfa meta bilgileri
$page_title = "Ana Sayfa - " . SITE_NAME;
$page_description = "Endüstriyel makine sektöründe 15+ yıllık tecrübemizle kaliteli çözümler sunuyoruz. CNC, torna, freze, kaynak makineleri ve daha fazlası.";
$page_keywords = "endüstriyel makine, CNC makine, torna, freze, kaynak makinesi, GNG Makine";
$current_page = "home";

// Veritabanından verileri çek
try {
    // Öne çıkan ürünler
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name, c.slug as category_slug
                          FROM products p
                          LEFT JOIN categories c ON p.category_id = c.id
                          WHERE p.status = 'active' AND p.featured = 1
                          ORDER BY p.sort_order ASC, p.created_at DESC LIMIT 8");
    $stmt->execute();
    $featured_products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Kategoriler
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE status = 'active' ORDER BY sort_order ASC LIMIT 6");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Son blog yazıları
    $stmt = $pdo->prepare("SELECT * FROM articles WHERE status = 'published' ORDER BY created_at DESC LIMIT 3");
    $stmt->execute();
    $latest_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Son mesajlar
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM contact_messages WHERE status = 'unread'");
    $stmt->execute();
    $unread_messages = $stmt->fetch()['total'];

    // Site istatistikleri
    $stats = [
        'experience' => '15+',
        'customers' => '500+',
        'projects' => '1000+',
        'support' => '24/7'
    ];

} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    $featured_products = [];
    $categories = [];
    $latest_articles = [];
    $unread_messages = 0;
    $stats = [
        'experience' => '15+',
        'customers' => '500+',
        'projects' => '1000+',
        'support' => '24/7'
    ];
}

// Hizmetler verisi
$services = [
    [
        'icon' => 'fas fa-shopping-cart',
        'title' => 'Makine Satışı',
        'description' => 'Yeni ve ikinci el makine seçenekleri'
    ],
    [
        'icon' => 'fas fa-tools',
        'title' => 'Teknik Destek',
        'description' => '7/24 uzman teknisyen desteği'
    ],
    [
        'icon' => 'fas fa-wrench',
        'title' => 'Bakım & Onarım',
        'description' => 'Profesyonel bakım ve onarım hizmetleri'
    ],
    [
        'icon' => 'fas fa-cogs',
        'title' => 'Yedek Parça',
        'description' => 'Geniş yedek parça stoğu'
    ]
];

// Sertifikalar
$certificates = [
    'ISO 9001:2015 Kalite Yönetim Sistemi',
    'ISO 14001:2015 Çevre Yönetim Sistemi',
    'OHSAS 18001 İş Sağlığı ve Güvenliği',
    'CE Uygunluk Beyanı',
    'TSE Hizmet Yeterlilik Belgesi',
    'Kaizen Sürekli İyileştirme Sertifikası'
];

include 'includes/header.php';
?>
    
    <style>
        :root {
            --primary: #F9B233;
            --primary-dark: #e6a02e;
            --secondary: #8A8C8F;
            --accent: #e67e22;
            --dark: #2C3E50;
            --light: #ECF0F1;
            --white: #FFFFFF;
            --text-dark: #2C3E50;
            --text-light: #7F8C8D;
            --shadow: 0 10px 30px rgba(0,0,0,0.1);
            --shadow-hover: 0 20px 40px rgba(0,0,0,0.15);
            --transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            --gradient-dark: linear-gradient(135deg, var(--dark) 0%, #34495e 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Loading animasyonu kaldırıldı */

        /* Parallax Hero Section */
        .hero {
            height: 100vh;
            background: var(--gradient-dark);
            color: var(--white);
            position: relative;
            display: flex;
            align-items: center;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMiIgZmlsbD0iIzNFNzNFRiIgZmlsbC1vcGFjaXR5PSIwLjEiLz4KPC9zdmc+');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .hero-particles {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .particle {
            position: absolute;
            background: var(--primary);
            border-radius: 50%;
            animation: particle-float 15s linear infinite;
            opacity: 0.3;
        }

        @keyframes particle-float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% { opacity: 0.3; }
            90% { opacity: 0.3; }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
        }

        .hero-text {
            opacity: 0;
            transform: translateX(-50px);
            animation: slideInLeft 1s ease 0.5s forwards;
        }

        @keyframes slideInLeft {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .hero-text h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 25px;
            background: linear-gradient(45deg, var(--white), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-text .highlight {
            position: relative;
            display: inline-block;
        }

        .hero-text .highlight::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 3px;
            background: var(--primary);
            animation: underline 2s ease 1.5s forwards;
        }

        @keyframes underline {
            to { width: 100%; }
        }

        .hero-text p {
            font-size: 1.2rem;
            line-height: 1.7;
            margin-bottom: 40px;
            opacity: 0.9;
            color: #bdc3c7;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--white);
            box-shadow: 0 10px 30px rgba(249, 178, 51, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(249, 178, 51, 0.4);
        }

        .btn-outline {
            background: transparent;
            color: var(--white);
            border-color: var(--white);
        }

        .btn-outline:hover {
            background: var(--white);
            color: var(--dark);
            transform: translateY(-3px);
        }

        .hero-visual {
            position: relative;
            opacity: 0;
            transform: translateX(50px);
            animation: slideInRight 1s ease 0.7s forwards;
        }

        @keyframes slideInRight {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .hero-image {
            width: 100%;
            max-width: 500px;
            height: 400px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 5rem;
            color: var(--primary);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .hero-image::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(249, 178, 51, 0.1), transparent);
            animation: shimmer 3s linear infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        /* Animated Stats Section */
        .stats-section {
            padding: 80px 0;
            background: var(--gradient-primary);
            color: var(--white);
            position: relative;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 40px;
            text-align: center;
        }

        .stat-item {
            padding: 40px 20px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--white);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .stat-item:hover::before {
            transform: scaleX(1);
        }

        .stat-item:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.25);
        }

        .stat-number {
            font-size: 3.5rem;
            font-weight: 900;
            display: block;
            margin-bottom: 15px;
            opacity: 0;
        }

        .stat-label {
            font-size: 1.1rem;
            font-weight: 600;
            opacity: 0.9;
        }

        /* Modern Categories Section */
        .categories-section {
            padding: 120px 0;
            background: var(--white);
            position: relative;
        }

        .section-header {
            text-align: center;
            margin-bottom: 80px;
        }

        .section-header h2 {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 800;
            color: var(--text-dark);
            margin-bottom: 20px;
            position: relative;
        }

        .section-header h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: var(--gradient-primary);
            border-radius: 2px;
        }

        .section-header p {
            font-size: 1.2rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }

        .category-card {
            background: var(--white);
            border-radius: 25px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            border: 2px solid transparent;
            text-decoration: none;
            color: inherit;
            position: relative;
            group: category;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .category-card:hover::before {
            opacity: 0.1;
        }

        .category-card:hover {
            transform: translateY(-15px) scale(1.02);
            border-color: var(--primary);
            box-shadow: var(--shadow-hover);
        }

        .category-image {
            height: 220px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: var(--primary);
            position: relative;
            overflow: hidden;
        }

        .category-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0.1;
            transform: scale(0);
            transition: transform 0.3s ease;
        }

        .category-card:hover .category-image::after {
            transform: scale(1);
        }

        .category-info {
            padding: 35px;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .category-info h3 {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 15px;
            transition: color 0.3s ease;
        }

        .category-card:hover .category-info h3 {
            color: var(--primary);
        }

        .category-info p {
            color: var(--text-light);
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .category-link {
            color: var(--primary);
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: var(--transition);
        }

        .category-link:hover {
            color: var(--accent);
            transform: translateX(5px);
        }

        /* Products Section with Modern Cards */
        .products-section {
            padding: 120px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
        }

        .product-card {
            background: var(--white);
            border-radius: 25px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            border: 2px solid transparent;
            position: relative;
            group: product;
        }

        .product-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .product-card:hover::after {
            opacity: 0.05;
        }

        .product-card:hover {
            transform: translateY(-15px) scale(1.02);
            border-color: var(--primary);
            box-shadow: var(--shadow-hover);
        }

        .product-image {
            height: 250px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3.5rem;
            color: var(--primary);
            position: relative;
            overflow: hidden;
        }

        .product-badge {
            position: absolute;
            top: 20px;
            left: 20px;
            background: var(--gradient-primary);
            color: var(--white);
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 2;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .product-info {
            padding: 30px;
            position: relative;
            z-index: 2;
        }

        .product-category {
            color: var(--primary);
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .product-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 15px;
            line-height: 1.3;
            transition: color 0.3s ease;
        }

        .product-card:hover .product-title {
            color: var(--primary);
        }

        .product-description {
            color: var(--text-light);
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .product-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .product-btn {
            background: var(--gradient-primary);
            color: var(--white);
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 600;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .product-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .product-btn:hover::before {
            left: 100%;
        }

        .product-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(249, 178, 51, 0.3);
        }

        /* Services Overview */
        .services-overview {
            padding: 120px 0;
            background: var(--white);
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 60px;
        }

        .service-card {
            background: var(--white);
            padding: 40px 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: var(--shadow);
            transition: var(--transition);
            border: 2px solid transparent;
        }

        .service-card:hover {
            transform: translateY(-10px);
            border-color: var(--primary);
            box-shadow: var(--shadow-hover);
        }

        .service-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 2rem;
            margin: 0 auto 25px;
            transition: var(--transition);
        }

        .service-card:hover .service-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .service-card h3 {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 15px;
        }

        .service-card p {
            color: var(--text-light);
            line-height: 1.6;
        }

        /* Certificates Section */
        .certificates-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .certificates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 60px;
        }

        .certificate-item {
            background: var(--white);
            padding: 25px;
            border-radius: 15px;
            box-shadow: var(--shadow);
            transition: var(--transition);
            border-left: 4px solid var(--primary);
        }

        .certificate-item:hover {
            transform: translateX(10px);
        }

        .certificate-item i {
            color: var(--primary);
            font-size: 1.5rem;
            margin-right: 15px;
        }

        .certificate-item span {
            font-weight: 600;
            color: var(--text-dark);
        }

        /* Blog Section */
        .blog-section {
            padding: 120px 0;
            background: var(--white);
        }

        .blog-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 60px;
        }

        .blog-card {
            background: var(--white);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
        }

        .blog-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-hover);
        }

        .blog-image {
            height: 200px;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: var(--white);
        }

        .blog-content {
            padding: 30px;
        }

        .blog-date {
            color: var(--primary);
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .blog-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .blog-excerpt {
            color: var(--text-light);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .blog-link {
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: var(--transition);
        }

        .blog-link:hover {
            color: var(--accent);
            transform: translateX(5px);
        }

        /* CTA Section */
        .cta-section {
            padding: 120px 0;
            background: var(--gradient-dark);
            color: var(--white);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .cta-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            animation: float 20s ease-in-out infinite;
        }

        .cta-content {
            position: relative;
            z-index: 2;
        }

        .cta-content h2 {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 800;
            margin-bottom: 25px;
            background: linear-gradient(45deg, var(--white), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .cta-content p {
            font-size: 1.3rem;
            margin-bottom: 50px;
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-buttons {
            display: flex;
            gap: 25px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Scroll Animations */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .slide-in-left {
            opacity: 0;
            transform: translateX(-50px);
            transition: all 0.6s ease;
        }

        .slide-in-left.visible {
            opacity: 1;
            transform: translateX(0);
        }

        .slide-in-right {
            opacity: 0;
            transform: translateX(50px);
            transition: all 0.6s ease;
        }

        .slide-in-right.visible {
            opacity: 1;
            transform: translateX(0);
        }

        .scale-in {
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.6s ease;
        }

        .scale-in.visible {
            opacity: 1;
            transform: scale(1);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .hero-content {
                grid-template-columns: 1fr;
                gap: 50px;
                text-align: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 30px;
            }

            .categories-grid,
            .products-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 25px;
            }
        }

        @media (max-width: 768px) {
            .hero {
                height: auto;
                min-height: 100vh;
                padding: 80px 0;
            }

            .hero-text h1 {
                font-size: 2.5rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 25px;
            }

            .hero-buttons,
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 280px;
                justify-content: center;
            }

            .categories-grid,
            .products-grid,
            .services-grid,
            .blog-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .section-header h2 {
                font-size: 2rem;
            }

            .certificates-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Advanced Hover Effects */
        .magnetic-btn {
            transition: transform 0.1s ease;
        }

        .tilt-card {
            transform-style: preserve-3d;
            transition: transform 0.1s ease;
        }

        /* Glassmorphism Effects */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Gradient Text */
        .gradient-text {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <!-- Hero Section with Parallax -->
    <section class="hero" id="hero">
        <div class="hero-particles" id="particles"></div>
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>Endüstriyel Makine <span class="highlight">Çözümleriniz</span></h1>
                    <p>15 yılı aşkın tecrübemizle endüstriyel makine sektöründe güvenilir çözüm ortağınızız. Kalite, güvenilirlik ve müşteri memnuniyeti odaklı hizmet anlayışımızla sektörde öncü konumdayız.</p>

                    <div class="hero-buttons">
                        <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-primary magnetic-btn">
                            <i class="fas fa-search"></i>
                            Ürünleri İncele
                        </a>
                        <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-outline magnetic-btn">
                            <i class="fas fa-phone"></i>
                            İletişime Geç
                        </a>
                    </div>
                </div>

                <div class="hero-visual">
                    <div class="hero-image glass">
                        <i class="fas fa-industry"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Animated Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="stats-grid">
                <?php foreach ($stats as $key => $value): ?>
                <div class="stat-item glass tilt-card">
                    <span class="stat-number" data-count="<?php echo $value; ?>">0</span>
                    <div class="stat-label">
                        <?php 
                        $labels = [
                            'experience' => 'Yıllık Tecrübe',
                            'customers' => 'Mutlu Müşteri',
                            'projects' => 'Tamamlanan Proje',
                            'support' => 'Teknik Destek'
                        ];
                        echo $labels[$key];
                        ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Modern Categories Section -->
    <section class="categories-section fade-in">
        <div class="container">
            <div class="section-header">
                <h2>Ürün <span class="gradient-text">Kategorilerimiz</span></h2>
                <p>Geniş ürün yelpazemizle endüstriyel ihtiyaçlarınıza uygun çözümler sunuyoruz.</p>
            </div>

            <div class="categories-grid">
                <?php if (!empty($categories)): ?>
                    <?php foreach ($categories as $index => $category): ?>
                    <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo urlencode($category['slug']); ?>" 
                       class="category-card tilt-card fade-in" 
                       style="animation-delay: <?php echo $index * 0.1; ?>s;">
                        <div class="category-image">
                            <i class="fas fa-<?php echo $category['icon'] ?? 'cog'; ?>"></i>
                        </div>
                        <div class="category-info">
                            <h3><?php echo htmlspecialchars($category['name']); ?></h3>
                            <p><?php echo htmlspecialchars($category['description'] ?? 'Kategori açıklaması'); ?></p>
                            <span class="category-link">
                                Ürünleri Gör <i class="fas fa-arrow-right"></i>
                            </span>
                        </div>
                    </a>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Varsayılan kategoriler -->
                    <a href="<?php echo BASE_URL; ?>/products.php?category=cnc-torna" class="category-card tilt-card">
                        <div class="category-image">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="category-info">
                            <h3>CNC Torna</h3>
                            <p>Hassas işleme için CNC torna makineleri</p>
                            <span class="category-link">
                                Ürünleri Gör <i class="fas fa-arrow-right"></i>
                            </span>
                        </div>
                    </a>

                    <a href="<?php echo BASE_URL; ?>/products.php?category=freze" class="category-card tilt-card">
                        <div class="category-image">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="category-info">
                            <h3>Freze Makineleri</h3>
                            <p>Çok amaçlı freze makineleri</p>
                            <span class="category-link">
                                Ürünleri Gör <i class="fas fa-arrow-right"></i>
                            </span>
                        </div>
                    </a>

                    <a href="<?php echo BASE_URL; ?>/products.php?category=kaynak" class="category-card tilt-card">
                        <div class="category-image">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="category-info">
                            <h3>Kaynak Makineleri</h3>
                            <p>Profesyonel kaynak ekipmanları</p>
                            <span class="category-link">
                                Ürünleri Gör <i class="fas fa-arrow-right"></i>
                            </span>
                        </div>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="products-section fade-in">
        <div class="container">
            <div class="section-header">
                <h2>Öne Çıkan <span class="gradient-text">Ürünlerimiz</span></h2>
                <p>En çok tercih edilen ve kaliteli ürünlerimizi keşfedin.</p>
            </div>

            <div class="products-grid">
                <?php if (!empty($featured_products)): ?>
                    <?php foreach ($featured_products as $index => $product): ?>
                    <div class="product-card tilt-card scale-in" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                        <div class="product-image">
                            <?php if (!empty($product['main_image'])): ?>
                                <img src="<?php echo UPLOADS_URL . '/products/' . $product['main_image']; ?>"
                                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     style="width: 100%; height: 100%; object-fit: cover;">
                            <?php else: ?>
                                <i class="fas fa-cog"></i>
                            <?php endif; ?>

                            <?php if ($product['featured']): ?>
                                <div class="product-badge">Öne Çıkan</div>
                            <?php endif; ?>
                        </div>

                        <div class="product-info">
                            <?php if (!empty($product['category_name'])): ?>
                                <div class="product-category"><?php echo htmlspecialchars($product['category_name']); ?></div>
                            <?php endif; ?>

                            <h3 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h3>

                            <?php if (!empty($product['short_description'])): ?>
                                <p class="product-description"><?php echo htmlspecialchars(substr($product['short_description'], 0, 120)) . '...'; ?></p>
                            <?php endif; ?>

                            <div class="product-footer">
                                <a href="<?php echo BASE_URL; ?>/product-detail.php?slug=<?php echo urlencode($product['slug']); ?>"
                                   class="product-btn">
                                    Detayları Gör
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Varsayılan ürünler -->
                    <div class="product-card tilt-card">
                        <div class="product-image">
                            <i class="fas fa-cog"></i>
                            <div class="product-badge">Öne Çıkan</div>
                        </div>
                        <div class="product-info">
                            <div class="product-category">CNC Torna</div>
                            <h3 class="product-title">CNC Torna Makinesi</h3>
                            <p class="product-description">Yüksek hassasiyetli CNC torna makinesi</p>
                            <div class="product-footer">
                                <a href="<?php echo BASE_URL; ?>/products.php" class="product-btn">
                                    Detayları Gör
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="product-card tilt-card">
                        <div class="product-image">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="product-info">
                            <div class="product-category">Freze</div>
                            <h3 class="product-title">Freze Makinesi</h3>
                            <p class="product-description">Çok amaçlı freze makinesi</p>
                            <div class="product-footer">
                                <a href="<?php echo BASE_URL; ?>/products.php" class="product-btn">
                                    Detayları Gör
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="product-card tilt-card">
                        <div class="product-image">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="product-info">
                            <div class="product-category">Kaynak</div>
                            <h3 class="product-title">Kaynak Makinesi</h3>
                            <p class="product-description">Profesyonel kaynak ekipmanı</p>
                            <div class="product-footer">
                                <a href="<?php echo BASE_URL; ?>/products.php" class="product-btn">
                                    Detayları Gör
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div style="text-align: center; margin-top: 50px;">
                <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-primary magnetic-btn">
                    <i class="fas fa-th-large"></i>
                    Tüm Ürünleri Gör
                </a>
            </div>
        </div>
    </section>

    <!-- Services Overview -->
    <section class="services-overview fade-in">
        <div class="container">
            <div class="section-header">
                <h2>Hizmet <span class="gradient-text">Alanlarımız</span></h2>
                <p>Kapsamlı hizmet yelpazemizle endüstriyel ihtiyaçlarınıza çözüm üretiyoruz.</p>
            </div>

            <div class="services-grid">
                <?php foreach ($services as $index => $service): ?>
                <div class="service-card tilt-card fade-in" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                    <div class="service-icon">
                        <i class="<?php echo $service['icon']; ?>"></i>
                    </div>
                    <h3><?php echo htmlspecialchars($service['title']); ?></h3>
                    <p><?php echo htmlspecialchars($service['description']); ?></p>
                </div>
                <?php endforeach; ?>
            </div>

            <div style="text-align: center; margin-top: 50px;">
                <a href="<?php echo BASE_URL; ?>/services.php" class="btn btn-primary magnetic-btn">
                    <i class="fas fa-arrow-right"></i>
                    Tüm Hizmetleri Gör
                </a>
            </div>
        </div>
    </section>

    <!-- Certificates Section -->
    <section class="certificates-section fade-in">
        <div class="container">
            <div class="section-header">
                <h2>Kalite <span class="gradient-text">Sertifikalarımız</span></h2>
                <p>Uluslararası standartlara uygunluğumuzu belgeleyen sertifikalarımız.</p>
            </div>

            <div class="certificates-grid">
                <?php foreach ($certificates as $index => $certificate): ?>
                <div class="certificate-item slide-in-left" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                    <i class="fas fa-certificate"></i>
                    <span><?php echo htmlspecialchars($certificate); ?></span>
                </div>
                <?php endforeach; ?>
            </div>

            <div style="text-align: center; margin-top: 50px;">
                <a href="<?php echo BASE_URL; ?>/certificates.php" class="btn btn-primary magnetic-btn">
                    <i class="fas fa-award"></i>
                    Tüm Sertifikaları Gör
                </a>
            </div>
        </div>
    </section>

    <!-- Blog Section -->
    <?php if (!empty($latest_articles)): ?>
    <section class="blog-section fade-in">
        <div class="container">
            <div class="section-header">
                <h2>Son <span class="gradient-text">Haberler</span></h2>
                <p>Sektördeki gelişmeler ve güncel içeriklerimiz.</p>
            </div>

            <div class="blog-grid">
                <?php foreach ($latest_articles as $index => $article): ?>
                <article class="blog-card tilt-card scale-in" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                    <div class="blog-image">
                        <?php if (!empty($article['featured_image'])): ?>
                            <img src="<?php echo UPLOADS_URL . '/articles/' . $article['featured_image']; ?>"
                                 alt="<?php echo htmlspecialchars($article['title']); ?>"
                                 style="width: 100%; height: 100%; object-fit: cover;">
                        <?php else: ?>
                            <i class="fas fa-newspaper"></i>
                        <?php endif; ?>
                    </div>
                    <div class="blog-content">
                        <div class="blog-date">
                            <i class="fas fa-calendar-alt"></i>
                            <?php echo date('d.m.Y', strtotime($article['created_at'])); ?>
                        </div>
                        <h3 class="blog-title"><?php echo htmlspecialchars($article['title']); ?></h3>
                        <?php if (!empty($article['excerpt'])): ?>
                            <p class="blog-excerpt"><?php echo htmlspecialchars(substr($article['excerpt'], 0, 120)) . '...'; ?></p>
                        <?php endif; ?>
                        <a href="<?php echo BASE_URL; ?>/article.php?slug=<?php echo urlencode($article['slug']); ?>" class="blog-link">
                            Devamını Oku <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </article>
                <?php endforeach; ?>
            </div>

            <div style="text-align: center; margin-top: 50px;">
                <a href="<?php echo BASE_URL; ?>/academy.php" class="btn btn-primary magnetic-btn">
                    <i class="fas fa-graduation-cap"></i>
                    Tüm Makaleleri Gör
                </a>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content fade-in">
                <h2>Projeleriniz İçin Doğru Çözüm</h2>
                <p>Endüstriyel makine ihtiyaçlarınız için uzman ekibimizle iletişime geçin. Size özel çözümler sunalım.</p>

                <div class="cta-buttons">
                    <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-primary magnetic-btn">
                        <i class="fas fa-phone"></i>
                        Hemen İletişime Geçin
                    </a>
                    <a href="<?php echo BASE_URL; ?>/about.php" class="btn btn-outline magnetic-btn">
                        <i class="fas fa-info-circle"></i>
                        Hakkımızda
                    </a>
                </div>
            </div>
        </div>
    </section>

    <?php include 'includes/footer.php'; ?>

    <script>
        // Particles Animation
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                const size = Math.random() * 4 + 2;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                
                particlesContainer.appendChild(particle);
            }
        }

        // Number Counter Animation
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-number');
            
            counters.forEach(counter => {
                const target = counter.getAttribute('data-count');
                const isPercent = target.includes('+');
                const numericTarget = parseInt(target.replace(/[^\d]/g, ''));
                let current = 0;
                const increment = numericTarget / 100;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= numericTarget) {
                        current = numericTarget;
                        clearInterval(timer);
                    }
                    
                    if (isPercent) {
                        counter.textContent = Math.floor(current) + '+';
                    } else if (target === '24/7') {
                        counter.textContent = '24/7';
                    } else {
                        counter.textContent = Math.floor(current) + '+';
                    }
                }, 50);
            });
        }

        // Intersection Observer for Scroll Animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    
                    // Trigger counter animation for stats section
                    if (entry.target.classList.contains('stats-section')) {
                        animateCounters();
                    }
                }
            });
        }, observerOptions);

        // Observe all animated elements
        document.addEventListener('DOMContentLoaded', function() {
            const animatedElements = document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right, .scale-in, .stats-section');
            animatedElements.forEach(el => observer.observe(el));
            
            createParticles();
        });

        // Magnetic Button Effect
        document.querySelectorAll('.magnetic-btn').forEach(btn => {
            btn.addEventListener('mousemove', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;
                
                this.style.transform = `translate(${x * 0.3}px, ${y * 0.3}px)`;
            });
            
            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translate(0, 0)';
            });
        });

        // 3D Tilt Effect for Cards
        document.querySelectorAll('.tilt-card').forEach(card => {
            card.addEventListener('mousemove', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const rotateX = (y - centerY) / 10;
                const rotateY = (centerX - x) / 10;
                
                this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.05, 1.05, 1.05)`;
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) scale3d(1, 1, 1)';
            });
        });

        // Smooth Scroll for Internal Links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Parallax Effect for Hero
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            const hero = document.getElementById('hero');
            if (hero) {
                hero.style.transform = `translateY(${rate}px)`;
            }
        });

        // Enhanced Page Transitions
        window.addEventListener('beforeunload', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.3s ease';
        });

        // Performance optimization for animations
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
        
        if (prefersReducedMotion.matches) {
            // Disable animations for users who prefer reduced motion
            document.documentElement.style.setProperty('--transition', 'none');
        }
    </script>
</body>
</html>