<?php
/**
 * GNG Makine - Ana <PERSON>
 */

// Konfigürasyonu dahil et
require_once __DIR__ . '/config/config.php';

// Sayfa meta bilgileri
$page_title = $site_settings['site_title'] . ' - ' . $site_settings['site_description'];
$page_description = $site_settings['site_description'] . ' - Gaziantep\'te endüstriyel makine üretimi ve satışı konusunda uzman ekibimizle hizmetinizdeyiz.';
$page_keywords = 'GNG Makine, endüstriyel makine, üretim ma<PERSON>, Gaziantep, makine imalatı, sanayi makinele<PERSON>';
$page_image = ASSETS_URL . '/images/hero/gng-makine-hero.jpg';

// Veritabanından verileri çek
try {
    $db = Database::getInstance();
    
    // Öne çıkan ürünleri çek
    $featured_products = $db->fetchAll("
        SELECT p.*, c.name as category_name, c.slug as category_slug
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.status = 'active' AND p.featured = 1 
        ORDER BY p.sort_order ASC, p.created_at DESC 
        LIMIT 6
    ");
    
    // Kategorileri çek
    $categories = $db->fetchAll("
        SELECT c.*, 
               (SELECT COUNT(*) FROM products p WHERE p.category_id = c.id AND p.status = 'active') as product_count,
               (SELECT p.main_image FROM products p WHERE p.category_id = c.id AND p.status = 'active' AND p.main_image IS NOT NULL ORDER BY p.featured DESC, p.created_at DESC LIMIT 1) as sample_image
        FROM categories c 
        WHERE c.status = 'active' AND c.parent_id IS NULL 
        ORDER BY c.sort_order ASC, c.name ASC 
        LIMIT 4
    ");
    
    // Son haberler/makaleler çek
    $latest_articles = $db->fetchAll("
        SELECT a.*, u.full_name as author_name
        FROM articles a 
        LEFT JOIN admin_users u ON a.author_id = u.id 
        WHERE a.status = 'published' AND a.published_at <= NOW() 
        ORDER BY a.featured DESC, a.published_at DESC 
        LIMIT 3
    ");
    
    // İstatistikler (örnek veriler - gerçek projede dinamik olabilir)
    $stats = [
        'experience_years' => COMPANY_EXPERIENCE_YEARS,
        'satisfied_customers' => 500,
        'completed_projects' => 1200,
        'team_members' => 25
    ];
    
} catch (Exception $e) {
    // Hata durumunda varsayılan veriler
    $featured_products = [];
    $categories = [];
    $latest_articles = [];
    $stats = [
        'experience_years' => 10,
        'satisfied_customers' => 500,
        'completed_projects' => 1200,
        'team_members' => 25
    ];
}

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-slider">
        <!-- Slide 1 -->
        <div class="hero-slide active" data-bg="<?php echo ASSETS_URL; ?>/images/hero/slide-1.jpg">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1 class="hero-title">
                            <span class="title-small">GNG Makine ile</span>
                            <span class="title-main">Endüstriyel Çözümler</span>
                            <span class="title-accent">Yeni Nesil Teknoloji</span>
                        </h1>
                        <p class="hero-description">
                            <?php echo COMPANY_EXPERIENCE_YEARS; ?> yıllık deneyimimizle, üretimden satış sonrası hizmete kadar 
                            kapsamlı endüstriyel makine çözümleri sunuyoruz. Modern teknoloji ve güvenilir hizmet anlayışımızla yanınızdayız.
                        </p>
                        <div class="hero-actions">
                            <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-primary btn-large">
                                <i class="fas fa-cogs"></i>
                                Ürünlerimizi İnceleyin
                            </a>
                            <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-outline btn-large">
                                <i class="fas fa-phone"></i>
                                Hemen İletişime Geçin
                            </a>
                        </div>
                    </div>
                    <div class="hero-visual">
                        <div class="hero-stats">
                            <div class="stat-item">
                                <div class="stat-number" data-count="<?php echo $stats['experience_years']; ?>">0</div>
                                <div class="stat-label">Yıl Deneyim</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" data-count="<?php echo $stats['satisfied_customers']; ?>">0</div>
                                <div class="stat-label">Mutlu Müşteri</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" data-count="<?php echo $stats['completed_projects']; ?>">0</div>
                                <div class="stat-label">Tamamlanan Proje</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Slide 2 -->
        <div class="hero-slide" data-bg="<?php echo ASSETS_URL; ?>/images/hero/slide-2.jpg">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1 class="hero-title">
                            <span class="title-small">Kalite ve Güven</span>
                            <span class="title-main">Profesyonel Hizmet</span>
                            <span class="title-accent">7/24 Teknik Destek</span>
                        </h1>
                        <p class="hero-description">
                            ISO 9001 kalite standardında üretim yapan firmamız, satış sonrası teknik destek ve yedek parça 
                            hizmetleriyle müşteri memnuniyetini ön planda tutar.
                        </p>
                        <div class="hero-actions">
                            <a href="<?php echo BASE_URL; ?>/services.php" class="btn btn-primary btn-large">
                                <i class="fas fa-tools"></i>
                                Hizmetlerimiz
                            </a>
                            <a href="<?php echo BASE_URL; ?>/about.php" class="btn btn-outline btn-large">
                                <i class="fas fa-info-circle"></i>
                                Hakkımızda
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Slide 3 -->
        <div class="hero-slide" data-bg="<?php echo ASSETS_URL; ?>/images/hero/slide-3.jpg">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1 class="hero-title">
                            <span class="title-small">Eğitim ve Gelişim</span>
                            <span class="title-main">AEM Akademi</span>
                            <span class="title-accent">Uzman Kadro Eğitimi</span>
                        </h1>
                        <p class="hero-description">
                            AEM Akademi ile makine operatörleri ve teknik personel için düzenlediğimiz eğitim programlarıyla, 
                            verimliliği artırın ve uzman kadro yetiştirin.
                        </p>
                        <div class="hero-actions">
                            <a href="<?php echo BASE_URL; ?>/academy.php" class="btn btn-primary btn-large">
                                <i class="fas fa-graduation-cap"></i>
                                Akademiye Katılın
                            </a>
                            <a href="<?php echo BASE_URL; ?>/contact.php?type=training" class="btn btn-outline btn-large">
                                <i class="fas fa-calendar-alt"></i>
                                Eğitim Takvimi
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Hero Controls -->
    <div class="hero-controls">
        <button class="hero-control hero-prev" aria-label="Önceki Slayt">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="hero-control hero-next" aria-label="Sonraki Slayt">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>
    
    <!-- Hero Indicators -->
    <div class="hero-indicators">
        <button class="hero-indicator active" data-slide="0"></button>
        <button class="hero-indicator" data-slide="1"></button>
        <button class="hero-indicator" data-slide="2"></button>
    </div>
    
    <!-- Scroll Down Arrow -->
    <div class="scroll-indicator">
        <div class="scroll-arrow">
            <i class="fas fa-chevron-down"></i>
        </div>
        <span class="scroll-text">Keşfetmeye Devam Edin</span>
    </div>
</section>

<!-- Features Section -->
<section class="features-section">
    <div class="container">
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-award"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">ISO 9001 Kalite</h3>
                    <p class="feature-description">Uluslararası standartlarda üretim ve kalite güvencesi</p>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">7/24 Teknik Destek</h3>
                    <p class="feature-description">Kesintisiz hizmet ve anında teknik destek</p>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">Hızlı Teslimat</h3>
                    <p class="feature-description">Türkiye geneli hızlı ve güvenli teslimat</p>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">Yedek Parça Garantisi</h3>
                    <p class="feature-description">Kapsamlı yedek parça stoku ve garanti</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="categories-section">
    <div class="container">
        <div class="section-header">
            <div class="section-title-wrapper">
                <h2 class="section-title">Ürün Kategorilerimiz</h2>
                <p class="section-subtitle">İhtiyacınıza uygun makine kategorilerini keşfedin</p>
            </div>
            <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-outline">
                <span>Tüm Ürünler</span>
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
        
        <div class="categories-grid">
            <?php if (!empty($categories)): ?>
                <?php foreach ($categories as $category): ?>
                <div class="category-card">
                    <div class="category-image">
                        <?php if (!empty($category['sample_image'])): ?>
                            <img src="<?php echo UPLOADS_URL; ?>/products/<?php echo escape_html($category['sample_image']); ?>" 
                                 alt="<?php echo escape_html($category['name']); ?>" 
                                 loading="lazy">
                        <?php else: ?>
                            <img src="<?php echo ASSETS_URL; ?>/images/categories/default-category.jpg" 
                                 alt="<?php echo escape_html($category['name']); ?>" 
                                 loading="lazy">
                        <?php endif; ?>
                        <div class="category-overlay">
                            <div class="category-overlay-content">
                                <span class="product-count"><?php echo $category['product_count']; ?> Ürün</span>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo escape_html($category['slug']); ?>" 
                                   class="category-link">
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="category-content">
                        <h3 class="category-title">
                            <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo escape_html($category['slug']); ?>">
                                <?php echo escape_html($category['name']); ?>
                            </a>
                        </h3>
                        <?php if (!empty($category['description'])): ?>
                            <p class="category-description">
                                <?php echo escape_html(substr($category['description'], 0, 100)); ?>
                                <?php if (strlen($category['description']) > 100): ?>...<?php endif; ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- Varsayılan kategoriler -->
                <div class="category-card">
                    <div class="category-image">
                        <img src="<?php echo ASSETS_URL; ?>/images/categories/uretim.jpg" alt="Üretim Makineleri" loading="lazy">
                        <div class="category-overlay">
                            <div class="category-overlay-content">
                                <span class="product-count">25 Ürün</span>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=uretim" class="category-link">
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="category-content">
                        <h3 class="category-title">
                            <a href="<?php echo BASE_URL; ?>/products.php?category=uretim">Üretim Makineleri</a>
                        </h3>
                        <p class="category-description">Endüstriyel üretim süreçleri için geliştirilmiş modern makineler</p>
                    </div>
                </div>
                
                <div class="category-card">
                    <div class="category-image">
                        <img src="<?php echo ASSETS_URL; ?>/images/categories/calisan.jpg" alt="Çalışan Makineleri" loading="lazy">
                        <div class="category-overlay">
                            <div class="category-overlay-content">
                                <span class="product-count">18 Ürün</span>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=calisan" class="category-link">
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="category-content">
                        <h3 class="category-title">
                            <a href="<?php echo BASE_URL; ?>/products.php?category=calisan">Çalışan Makineleri</a>
                        </h3>
                        <p class="category-description">İş gücü verimliliğini artıran çalışan destekli makine sistemleri</p>
                    </div>
                </div>
                
                <div class="category-card">
                    <div class="category-image">
                        <img src="<?php echo ASSETS_URL; ?>/images/categories/alan.jpg" alt="Alan Makineleri" loading="lazy">
                        <div class="category-overlay">
                            <div class="category-overlay-content">
                                <span class="product-count">12 Ürün</span>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=alan" class="category-link">
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="category-content">
                        <h3 class="category-title">
                            <a href="<?php echo BASE_URL; ?>/products.php?category=alan">Alan Makineleri</a>
                        </h3>
                        <p class="category-description">Geniş çalışma alanları için optimize edilmiş makine çözümleri</p>
                    </div>
                </div>
                
                <div class="category-card">
                    <div class="category-image">
                        <img src="<?php echo ASSETS_URL; ?>/images/categories/musteri.jpg" alt="Müşteri Odaklı" loading="lazy">
                        <div class="category-overlay">
                            <div class="category-overlay-content">
                                <span class="product-count">15 Ürün</span>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=musteri" class="category-link">
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="category-content">
                        <h3 class="category-title">
                            <a href="<?php echo BASE_URL; ?>/products.php?category=musteri">Müşteri Odaklı</a>
                        </h3>
                        <p class="category-description">Özel müşteri ihtiyaçlarına göre tasarlanmış özel çözümler</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<?php if (!empty($featured_products)): ?>
<section class="products-section">
    <div class="container">
        <div class="section-header">
            <div class="section-title-wrapper">
                <h2 class="section-title">Öne Çıkan Ürünlerimiz</h2>
                <p class="section-subtitle">En çok tercih edilen ve yenilikçi ürünlerimizi keşfedin</p>
            </div>
            <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-outline">
                <span>Tüm Ürünler</span>
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
        
        <div class="products-grid">
            <?php foreach ($featured_products as $product): ?>
            <div class="product-card">
                <div class="product-image">
                    <?php if (!empty($product['main_image'])): ?>
                        <img src="<?php echo UPLOADS_URL; ?>/products/<?php echo escape_html($product['main_image']); ?>" 
                             alt="<?php echo escape_html($product['name']); ?>" 
                             loading="lazy">
                    <?php else: ?>
                        <img src="<?php echo ASSETS_URL; ?>/images/products/default-product.jpg" 
                             alt="<?php echo escape_html($product['name']); ?>" 
                             loading="lazy">
                    <?php endif; ?>
                    
                    <div class="product-badge">
                        <span class="badge badge-featured">Öne Çıkan</span>
                    </div>
                    
                    <div class="product-overlay">
                        <div class="product-actions">
                            <a href="<?php echo BASE_URL; ?>/product-detail.php?slug=<?php echo escape_html($product['slug']); ?>" 
                               class="btn btn-primary btn-sm">
                                <i class="fas fa-eye"></i>
                                İncele
                            </a>
                            <button class="btn btn-outline btn-sm quick-contact" 
                                    data-product="<?php echo escape_html($product['name']); ?>">
                                <i class="fas fa-phone"></i>
                                İletişim
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="product-content">
                    <div class="product-meta">
                        <?php if (!empty($product['category_name'])): ?>
                            <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo escape_html($product['category_slug']); ?>" 
                               class="product-category">
                                <?php echo escape_html($product['category_name']); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                    
                    <h3 class="product-title">
                        <a href="<?php echo BASE_URL; ?>/product-detail.php?slug=<?php echo escape_html($product['slug']); ?>">
                            <?php echo escape_html($product['name']); ?>
                        </a>
                    </h3>
                    
                    <?php if (!empty($product['description'])): ?>
                        <p class="product-description">
                            <?php echo escape_html(substr(strip_tags($product['description']), 0, 120)); ?>
                            <?php if (strlen(strip_tags($product['description'])) > 120): ?>...<?php endif; ?>
                        </p>
                    <?php endif; ?>
                    
                    <div class="product-specs">
                        <?php if (!empty($product['model_number'])): ?>
                            <span class="spec-item">
                                <i class="fas fa-tag"></i>
                                Model: <?php echo escape_html($product['model_number']); ?>
                            </span>
                        <?php endif; ?>
                        
                        <?php if (!empty($product['power'])): ?>
                            <span class="spec-item">
                                <i class="fas fa-bolt"></i>
                                Güç: <?php echo escape_html($product['power']); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- About Section -->
<section class="about-section">
    <div class="container">
        <div class="about-content">
            <div class="about-text">
                <div class="section-header">
                    <h2 class="section-title">GNG Makine Neden Tercih Edilir?</h2>
                    <p class="section-subtitle">
                        <?php echo COMPANY_EXPERIENCE_YEARS; ?> yıllık deneyimimizle sektörde güvenilir ve yenilikçi çözümler sunuyoruz
                    </p>
                </div>
                
                <div class="about-features">
                    <div class="about-feature">
                        <div class="feature-icon">
                            <i class="fas fa-medal"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Uzman Ekip</h4>
                            <p>Alanında uzman mühendis ve teknisyenlerden oluşan profesyonel ekibimiz</p>
                        </div>
                    </div>
                    
                    <div class="about-feature">
                        <div class="feature-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Özel Çözümler</h4>
                            <p>Müşteri ihtiyaçlarına özel tasarım ve üretim hizmetleri</p>
                        </div>
                    </div>
                    
                    <div class="about-feature">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Güvenilir Servis</h4>
                            <p>Satış sonrası 7/24 teknik destek ve bakım hizmetleri</p>
                        </div>
                    </div>
                </div>
                
                <div class="about-actions">
                    <a href="<?php echo BASE_URL; ?>/about.php" class="btn btn-primary">
                        <i class="fas fa-info-circle"></i>
                        Hakkımızda Daha Fazla
                    </a>
                    <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-outline">
                        <i class="fas fa-handshake"></i>
                        İş Ortaklığı
                    </a>
                </div>
            </div>
            
            <div class="about-visual">
                <div class="about-image">
                    <img src="<?php echo ASSETS_URL; ?>/images/about/factory-team.jpg" 
                         alt="GNG Makine Fabrika ve Ekip" 
                         loading="lazy">
                </div>
                
                <div class="stats-card">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number" data-count="<?php echo $stats['experience_years']; ?>">0</div>
                            <div class="stat-label">Yıl Deneyim</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-count="<?php echo $stats['satisfied_customers']; ?>">0</div>
                            <div class="stat-label">Mutlu Müşteri</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-count="<?php echo $stats['completed_projects']; ?>">0</div>
                            <div class="stat-label">Proje</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-count="<?php echo $stats['team_members']; ?>">0</div>
                            <div class="stat-label">Ekip Üyesi</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="services-section">
    <div class="container">
        <div class="section-header centered">
            <h2 class="section-title">Kapsamlı Hizmetlerimiz</h2>
            <p class="section-subtitle">Satıştan kuruluma, eğitimden bakıma kadar tüm süreçlerde yanınızdayız</p>
        </div>
        
        <div class="services-grid">
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h3 class="service-title">Satış & Danışmanlık</h3>
                <p class="service-description">
                    İhtiyacınıza en uygun makine seçimi için uzman danışmanlık ve kapsamlı ürün gamı
                </p>
                <ul class="service-features">
                    <li>Ücretsiz keşif ve analiz</li>
                    <li>Özel çözüm tasarımı</li>
                    <li>Rekabetçi fiyatlar</li>
                </ul>
            </div>
            
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-wrench"></i>
                </div>
                <h3 class="service-title">Kurulum & Devreye Alma</h3>
                <p class="service-description">
                    Profesyonel kurulum ekibimizle makinelerinizi güvenli ve hızlı şekilde devreye alıyoruz
                </p>
                <ul class="service-features">
                    <li>Uzman kurulum ekibi</li>
                    <li>Test ve kalibrasyon</li>
                    <li>Güvenlik kontrolü</li>
                </ul>
            </div>
            
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h3 class="service-title">Eğitim & AEM Akademi</h3>
                <p class="service-description">
                    Operatör ve teknisyen eğitimleriyle ekibinizin verimliliğini maksimuma çıkarın
                </p>
                <ul class="service-features">
                    <li>Operatör eğitim programları</li>
                    <li>Teknik personel sertifikasyonu</li>
                    <li>Online eğitim platformu</li>
                </ul>
            </div>
            
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <h3 class="service-title">Bakım & Onarım</h3>
                <p class="service-description">
                    7/24 teknik destek ve periyodik bakım hizmetleriyle makinelerinizin ömrünü uzatın
                </p>
                <ul class="service-features">
                    <li>7/24 teknik destek</li>
                    <li>Preventif bakım programları</li>
                    <li>Orijinal yedek parça garantisi</li>
                </ul>
            </div>
        </div>
        
        <div class="services-cta">
            <a href="<?php echo BASE_URL; ?>/services.php" class="btn btn-primary btn-large">
                <i class="fas fa-arrow-right"></i>
                Tüm Hizmetlerimizi Görün
            </a>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="testimonials-section">
    <div class="container">
        <div class="section-header centered">
            <h2 class="section-title">Müşteri Görüşleri</h2>
            <p class="section-subtitle">Müşterilerimizin deneyimlerini ve başarı hikayelerini keşfedin</p>
        </div>
        
        <div class="testimonials-slider">
            <div class="testimonial-card">
                <div class="testimonial-content">
                    <div class="testimonial-stars">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <blockquote class="testimonial-text">
                        "GNG Makine'den aldığımız üretim hattı sayesinde kapasitemizi %40 artırdık. 
                        Hem ürün kaliteleri hem de satış sonrası destekleri mükemmel. Kesinlikle tavsiye ederim."
                    </blockquote>
                    <div class="testimonial-author">
                        <div class="author-image">
                            <img src="<?php echo ASSETS_URL; ?>/images/testimonials/client-1.jpg" 
                                 alt="Mehmet Yılmaz" loading="lazy">
                        </div>
                        <div class="author-info">
                            <h4 class="author-name">Mehmet Yılmaz</h4>
                            <p class="author-position">Üretim Müdürü, ABC Tekstil</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="testimonial-card">
                <div class="testimonial-content">
                    <div class="testimonial-stars">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <blockquote class="testimonial-text">
                        "3 yıldır GNG Makine ile çalışıyoruz. Makinelerin kalitesi ve dayanıklılığı gerçekten etkileyici. 
                        7/24 teknik destek hizmeti işimizi çok kolaylaştırıyor."
                    </blockquote>
                    <div class="testimonial-author">
                        <div class="author-image">
                            <img src="<?php echo ASSETS_URL; ?>/images/testimonials/client-2.jpg" 
                                 alt="Ayşe Demir" loading="lazy">
                        </div>
                        <div class="author-info">
                            <h4 class="author-name">Ayşe Demir</h4>
                            <p class="author-position">Genel Müdür, DEF Gıda</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="testimonial-card">
                <div class="testimonial-content">
                    <div class="testimonial-stars">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <blockquote class="testimonial-text">
                        "AEM Akademi eğitim programları sayesinde ekibimizin verimliliği önemli ölçüde arttı. 
                        Bu tür eğitim olanakları sunan bir firma ile çalışmak büyük avantaj."
                    </blockquote>
                    <div class="testimonial-author">
                        <div class="author-image">
                            <img src="<?php echo ASSETS_URL; ?>/images/testimonials/client-3.jpg" 
                                 alt="Can Özkan" loading="lazy">
                        </div>
                        <div class="author-info">
                            <h4 class="author-name">Can Özkan</h4>
                            <p class="author-position">Teknik Direktör, GHI Otomotiv</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="testimonials-controls">
            <button class="testimonial-control testimonial-prev" aria-label="Önceki Görüş">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button class="testimonial-control testimonial-next" aria-label="Sonraki Görüş">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>
</section>

<!-- News/Articles Section -->
<?php if (!empty($latest_articles)): ?>
<section class="news-section">
    <div class="container">
        <div class="section-header">
            <div class="section-title-wrapper">
                <h2 class="section-title">Haberler & Duyurular</h2>
                <p class="section-subtitle">Sektörden son haberler ve şirket duyurularımızı takip edin</p>
            </div>
            <a href="<?php echo BASE_URL; ?>/press.php" class="btn btn-outline">
                <span>Tüm Haberler</span>
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
        
        <div class="news-grid">
            <?php foreach ($latest_articles as $article): ?>
            <article class="news-card">
                <div class="news-image">
                    <?php if (!empty($article['featured_image'])): ?>
                        <img src="<?php echo UPLOADS_URL; ?>/articles/<?php echo escape_html($article['featured_image']); ?>" 
                             alt="<?php echo escape_html($article['title']); ?>" 
                             loading="lazy">
                    <?php else: ?>
                        <img src="<?php echo ASSETS_URL; ?>/images/news/default-news.jpg" 
                             alt="<?php echo escape_html($article['title']); ?>" 
                             loading="lazy">
                    <?php endif; ?>
                    
                    <?php if ($article['featured']): ?>
                        <div class="news-badge">
                            <span class="badge badge-featured">Öne Çıkan</span>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="news-content">
                    <div class="news-meta">
                        <span class="news-date">
                            <i class="fas fa-calendar"></i>
                            <?php echo format_date($article['published_at'], 'd.m.Y'); ?>
                        </span>
                        <?php if (!empty($article['author_name'])): ?>
                            <span class="news-author">
                                <i class="fas fa-user"></i>
                                <?php echo escape_html($article['author_name']); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <h3 class="news-title">
                        <a href="<?php echo BASE_URL; ?>/article.php?slug=<?php echo escape_html($article['slug']); ?>">
                            <?php echo escape_html($article['title']); ?>
                        </a>
                    </h3>
                    
                    <?php if (!empty($article['excerpt'])): ?>
                        <p class="news-excerpt">
                            <?php echo escape_html(substr($article['excerpt'], 0, 150)); ?>
                            <?php if (strlen($article['excerpt']) > 150): ?>...<?php endif; ?>
                        </p>
                    <?php endif; ?>
                    
                    <a href="<?php echo BASE_URL; ?>/article.php?slug=<?php echo escape_html($article['slug']); ?>" 
                       class="news-link">
                        Devamını Oku
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </article>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- CTA Section -->
<section class="cta-section">
    <div class="cta-background">
        <img src="<?php echo ASSETS_URL; ?>/images/cta/cta-bg.jpg" alt="İletişim" loading="lazy">
        <div class="cta-overlay"></div>
    </div>
    
    <div class="container">
        <div class="cta-content">
            <div class="cta-text">
                <h2 class="cta-title">Projeniз İçin En Doğru Çözümü Bulalım</h2>
                <p class="cta-description">
                    Uzman ekibimiz size özel çözümler geliştirmek için hazır. 
                    Ücretsiz keşif ve danışmanlık hizmeti için hemen iletişime geçin.
                </p>
                <div class="cta-features">
                    <div class="cta-feature">
                        <i class="fas fa-check"></i>
                        <span>Ücretsiz Keşif ve Analiz</span>
                    </div>
                    <div class="cta-feature">
                        <i class="fas fa-check"></i>
                        <span>Özel Çözüm Tasarımı</span>
                    </div>
                    <div class="cta-feature">
                        <i class="fas fa-check"></i>
                        <span>7/24 Teknik Destek</span>
                    </div>
                </div>
            </div>
            
            <div class="cta-actions">
                <a href="<?php echo BASE_URL; ?>/contact.php?type=quote" class="btn btn-primary btn-large">
                    <i class="fas fa-calculator"></i>
                    Ücretsiz Teklif Alın
                </a>
                <a href="tel:<?php echo str_replace([' ', '(', ')', '-'], '', $site_settings['company_phone'] ?? ''); ?>" 
                   class="btn btn-outline btn-large">
                    <i class="fas fa-phone"></i>
                    <?php echo escape_html($site_settings['company_phone'] ?? 'Hemen Arayın'); ?>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Partners Section -->
<section class="partners-section">
    <div class="container">
        <div class="section-header centered">
            <h2 class="section-title">İş Ortaklarımız</h2>
            <p class="section-subtitle">Güvenilir markalarla güçlü iş birlikleri</p>
        </div>
        
        <div class="partners-slider">
            <div class="partner-logo">
                <img src="<?php echo ASSETS_URL; ?>/images/partners/partner-1.png" alt="Partner 1" loading="lazy">
            </div>
            <div class="partner-logo">
                <img src="<?php echo ASSETS_URL; ?>/images/partners/partner-2.png" alt="Partner 2" loading="lazy">
            </div>
            <div class="partner-logo">
                <img src="<?php echo ASSETS_URL; ?>/images/partners/partner-3.png" alt="Partner 3" loading="lazy">
            </div>
            <div class="partner-logo">
                <img src="<?php echo ASSETS_URL; ?>/images/partners/partner-4.png" alt="Partner 4" loading="lazy">
            </div>
            <div class="partner-logo">
                <img src="<?php echo ASSETS_URL; ?>/images/partners/partner-5.png" alt="Partner 5" loading="lazy">
            </div>
            <div class="partner-logo">
                <img src="<?php echo ASSETS_URL; ?>/images/partners/partner-6.png" alt="Partner 6" loading="lazy">
            </div>
        </div>
    </div>
</section>

<!-- Quick Contact Modal -->
<div class="modal quick-contact-modal" id="quick-contact-modal">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Hızlı İletişim</h3>
            <button class="modal-close" aria-label="Kapat">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <form class="quick-contact-form" id="quick-contact-form" method="POST" action="<?php echo BASE_URL; ?>/ajax/quick-contact.php">
                <div class="form-group">
                    <label for="contact-name">Ad Soyad *</label>
                    <input type="text" id="contact-name" name="name" class="form-control" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="contact-phone">Telefon *</label>
                        <input type="tel" id="contact-phone" name="phone" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="contact-email">E-posta</label>
                        <input type="email" id="contact-email" name="email" class="form-control">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="contact-product">İlgilenilen Ürün</label>
                    <input type="text" id="contact-product" name="product" class="form-control" readonly>
                </div>
                
                <div class="form-group">
                    <label for="contact-message">Mesajınız *</label>
                    <textarea id="contact-message" name="message" class="form-control" rows="4" 
                              placeholder="Lütfen ihtiyacınızı kısaca açıklayın..." required></textarea>
                </div>
                
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary modal-close">İptal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        Gönder
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Page specific scripts -->
<?php
$page_scripts = [
    'home.js',
    'slider.js',
    'counter.js',
    'modal.js'
];
?>

<style>
/* ============================================================================
   GNG MAKİNE - ANA SAYFA CSS
   ============================================================================ */

/* CSS Reset & Variables */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #F9B233;
    --secondary-color: #8A8C8F;
    --accent-color: #e67e22;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --text-muted: #95a5a6;
    --bg-light: #f8f9fa;
    --bg-dark: #2c3e50;
    --white: #ffffff;
    --black: #000000;
    --success: #27ae60;
    --warning: #f39c12;
    --danger: #e74c3c;
    --info: #3498db;
    
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    --gradient-overlay: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.6));
    
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Base Styles */
body {
    font-family: var(--font-family);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-dark);
    background: var(--white);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ============================================================================
   LOADING SCREEN
   ============================================================================ */

.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-screen.fade-out {
    opacity: 0;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--bg-light);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ============================================================================
   TOP BAR
   ============================================================================ */

.top-bar {
    background: var(--bg-dark);
    color: var(--white);
    padding: 10px 0;
    font-size: 14px;
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.contact-info {
    display: flex;
    gap: 25px;
    flex-wrap: wrap;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.contact-item a {
    color: var(--white);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.contact-item a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    color: var(--white);
    font-size: 16px;
    transition: color var(--transition-fast);
}

.social-links a:hover {
    color: var(--primary-color);
}

/* ============================================================================
   MAIN HEADER
   ============================================================================ */

.main-header {
    background: var(--white);
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: background var(--transition-normal);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo a {
    display: flex;
    align-items: center;
    gap: 15px;
    text-decoration: none;
    color: var(--text-dark);
}

.logo-img {
    height: 50px;
    width: auto;
}

.logo-text {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.main-nav {
    display: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px 0;
    font-weight: 500;
    color: var(--text-dark);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white);
    box-shadow: var(--shadow-lg);
    border-radius: 8px;
    padding: 15px 0;
    min-width: 220px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
}

.nav-item:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 20px;
    color: var(--text-dark);
    text-decoration: none;
    transition: background var(--transition-fast);
}

.dropdown-link:hover {
    background: var(--bg-light);
    color: var(--primary-color);
}

.dropdown-divider {
    margin: 10px 0;
    border: none;
    border-top: 1px solid var(--bg-light);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.action-btn {
    background: none;
    border: none;
    color: var(--text-dark);
    font-size: 18px;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.action-btn:hover {
    background: var(--bg-light);
    color: var(--primary-color);
}

.mobile-menu-toggle {
    display: flex;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.hamburger-line {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    transition: var(--transition-normal);
}

/* ============================================================================
   BUTTONS
   ============================================================================ */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
}

.btn-primary {
    color: var(--white);
    background: var(--gradient-primary);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    color: var(--primary-color);
    background: transparent;
    border-color: var(--primary-color);
}

.btn-outline:hover {
    color: var(--white);
    background: var(--primary-color);
    transform: translateY(-2px);
}

.btn-secondary {
    color: var(--text-dark);
    background: var(--bg-light);
    border-color: var(--secondary-color);
}

.btn-large {
    padding: 16px 32px;
    font-size: 18px;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 14px;
}

/* ============================================================================
   HERO SECTION
   ============================================================================ */

.hero-section {
    position: relative;
    height: 100vh;
    min-height: 600px;
    overflow: hidden;
}

.hero-slider {
    position: relative;
    width: 100%;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.hero-slide.active {
    opacity: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-overlay);
}

.hero-content {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    z-index: 2;
}

.hero-text {
    flex: 1;
    max-width: 600px;
    color: var(--white);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 30px;
}

.title-small {
    display: block;
    font-size: 1.5rem;
    font-weight: 400;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.title-main {
    display: block;
    font-size: 4rem;
    font-weight: 800;
}

.title-accent {
    display: block;
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-top: 10px;
}

.hero-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 40px;
    opacity: 0.9;
}

.hero-actions {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.hero-visual {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-stats {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item {
    text-align: center;
    color: var(--white);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

.hero-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
}

.hero-control {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: none;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    color: var(--white);
    font-size: 20px;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.hero-control:hover {
    background: var(--primary-color);
    transform: scale(1.1);
}

.hero-prev {
    left: 30px;
}

.hero-next {
    right: 30px;
}

.hero-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    z-index: 3;
}

.hero-indicator {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    border: 2px solid var(--white);
    background: transparent;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.hero-indicator.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    right: 30px;
    text-align: center;
    color: var(--white);
    z-index: 3;
}

.scroll-arrow {
    font-size: 24px;
    animation: bounce 2s infinite;
    margin-bottom: 10px;
}

.scroll-text {
    font-size: 14px;
    opacity: 0.8;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* ============================================================================
   FEATURES SECTION
   ============================================================================ */

.features-section {
    padding: 80px 0;
    background: var(--bg-light);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature-card {
    background: var(--white);
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-10px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 32px;
    color: var(--white);
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.feature-description {
    color: var(--text-light);
    line-height: 1.6;
}

/* ============================================================================
   SECTION HEADERS
   ============================================================================ */

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: end;
    margin-bottom: 60px;
    gap: 30px;
}

.section-header.centered {
    text-align: center;
    flex-direction: column;
    align-items: center;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 600px;
    line-height: 1.6;
}

/* ============================================================================
   CATEGORIES SECTION
   ============================================================================ */

.categories-section {
    padding: 100px 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.category-card {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.category-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.category-card:hover .category-image img {
    transform: scale(1.05);
}

.category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.category-card:hover .category-overlay {
    opacity: 1;
}

.category-overlay-content {
    text-align: center;
    color: var(--white);
}

.product-count {
    display: block;
    font-size: 1.1rem;
    margin-bottom: 15px;
    font-weight: 500;
}

.category-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    color: var(--white);
    font-size: 20px;
    transition: all var(--transition-normal);
}

.category-link:hover {
    background: var(--accent-color);
    transform: scale(1.1);
}

.category-content {
    padding: 30px;
}

.category-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.category-title a {
    color: var(--text-dark);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.category-title a:hover {
    color: var(--primary-color);
}

.category-description {
    color: var(--text-light);
    line-height: 1.6;
}

/* ============================================================================
   PRODUCTS SECTION
   ============================================================================ */

.products-section {
    padding: 100px 0;
    background: var(--bg-light);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.product-card {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
}

.badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-featured {
    background: var(--primary-color);
    color: var(--white);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-actions {
    display: flex;
    gap: 15px;
}

.product-content {
    padding: 30px;
}

.product-meta {
    margin-bottom: 15px;
}

.product-category {
    display: inline-block;
    padding: 5px 12px;
    background: var(--bg-light);
    color: var(--primary-color);
    font-size: 12px;
    font-weight: 500;
    border-radius: 15px;
    text-decoration: none;
    transition: all var(--transition-fast);
}

.product-category:hover {
    background: var(--primary-color);
    color: var(--white);
}

.product-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 15px;
    line-height: 1.3;
}

.product-title a {
    color: var(--text-dark);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.product-title a:hover {
    color: var(--primary-color);
}

.product-description {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 20px;
}

.product-specs {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.spec-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: var(--text-muted);
}

/* ============================================================================
   ABOUT SECTION
   ============================================================================ */

.about-section {
    padding: 100px 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.about-features {
    margin: 40px 0;
}

.about-feature {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.about-feature .feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--white);
    flex-shrink: 0;
}

.about-feature .feature-content h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.about-feature .feature-content p {
    color: var(--text-light);
    line-height: 1.6;
}

.about-actions {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.about-visual {
    position: relative;
}

.about-image {
    border-radius: 20px;
    overflow: hidden;
    margin-bottom: 30px;
}

.about-image img {
    width: 100%;
    height: auto;
}

.stats-card {
    background: var(--white);
    border-radius: 20px;
    padding: 30px;
    box-shadow: var(--shadow-lg);
    position: relative;
    z-index: 2;
    margin-top: -80px;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.stats-card .stat-item {
    text-align: center;
}

.stats-card .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stats-card .stat-label {
    font-size: 1rem;
    color: var(--text-light);
}

/* ============================================================================
   SERVICES SECTION
   ============================================================================ */

.services-section {
    padding: 100px 0;
    background: var(--bg-light);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.service-card {
    background: var(--white);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 32px;
    color: var(--white);
}

.service-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.service-description {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 25px;
}

.service-features {
    list-style: none;
    padding: 0;
    text-align: left;
}

.service-features li {
    padding: 8px 0;
    color: var(--text-light);
    position: relative;
    padding-left: 25px;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success);
    font-weight: bold;
}

.services-cta {
    text-align: center;
}

/* ============================================================================
   TESTIMONIALS SECTION
   ============================================================================ */

.testimonials-section {
    padding: 100px 0;
    position: relative;
}

.testimonials-slider {
    display: flex;
    gap: 30px;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    padding-bottom: 20px;
}

.testimonial-card {
    flex: 0 0 400px;
    background: var(--white);
    padding: 40px;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    scroll-snap-align: start;
}

.testimonial-stars {
    display: flex;
    gap: 5px;
    margin-bottom: 25px;
    color: var(--warning);
}

.testimonial-text {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-dark);
    margin-bottom: 30px;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 20px;
}

.author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
}

.author-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 5px;
}

.author-position {
    color: var(--text-light);
    font-size: 14px;
}

.testimonials-controls {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 30px;
    pointer-events: none;
}

.testimonial-control {
    width: 50px;
    height: 50px;
    background: var(--white);
    border: none;
    border-radius: 50%;
    box-shadow: var(--shadow-lg);
    color: var(--text-dark);
    font-size: 18px;
    cursor: pointer;
    pointer-events: all;
    transition: all var(--transition-normal);
}

.testimonial-control:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

/* ============================================================================
   NEWS SECTION
   ============================================================================ */

.news-section {
    padding: 100px 0;
    background: var(--bg-light);
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.news-card {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.news-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.news-image {
    position: relative;
    height: 220px;
    overflow: hidden;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-badge {
    position: absolute;
    top: 15px;
    left: 15px;
}

.news-content {
    padding: 30px;
}

.news-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    font-size: 14px;
    color: var(--text-muted);
}

.news-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.news-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 15px;
    line-height: 1.3;
}

.news-title a {
    color: var(--text-dark);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.news-title a:hover {
    color: var(--primary-color);
}

.news-excerpt {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 20px;
}

.news-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    font-weight: 500;
    text-decoration: none;
    transition: all var(--transition-fast);
}

.news-link:hover {
    color: var(--accent-color);
    gap: 12px;
}

/* ============================================================================
   CTA SECTION
   ============================================================================ */

.cta-section {
    position: relative;
    padding: 120px 0;
    overflow: hidden;
}

.cta-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.cta-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cta-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
}

.cta-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--white);
    max-width: 800px;
    margin: 0 auto;
}

.cta-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 25px;
    line-height: 1.2;
}

.cta-description {
    font-size: 1.3rem;
    line-height: 1.6;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-features {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.cta-feature {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 500;
}

.cta-feature i {
    color: var(--primary-color);
    font-size: 20px;
}

.cta-actions {
    display: flex;
    justify-content: center;
    gap: 25px;
    flex-wrap: wrap;
}

/* ============================================================================
   PARTNERS SECTION
   ============================================================================ */

.partners-section {
    padding: 80px 0;
    background: var(--white);
}

.partners-slider {
    display: flex;
    gap: 60px;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    opacity: 0.7;
    transition: opacity var(--transition-normal);
}

.partners-slider:hover {
    opacity: 1;
}

.partner-logo {
    flex: 0 0 auto;
    height: 60px;
    display: flex;
    align-items: center;
    filter: grayscale(100%);
    transition: filter var(--transition-normal);
}

.partner-logo:hover {
    filter: grayscale(0%);
}

.partner-logo img {
    height: 100%;
    width: auto;
    max-width: 150px;
}

/* ============================================================================
   MODAL
   ============================================================================ */

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: none;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: relative;
    background: var(--white);
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: var(--shadow-2xl);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid var(--bg-light);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-muted);
    cursor: pointer;
    padding: 5px;
    transition: color var(--transition-fast);
}

.modal-close:hover {
    color: var(--text-dark);
}

.modal-body {
    padding: 30px;
}

/* ============================================================================
   FORMS
   ============================================================================ */

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 16px;
    font-family: inherit;
    transition: border-color var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-control::placeholder {
    color: var(--text-muted);
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 1024px) {
    .hero-title {
        font-size: 3rem;
    }
    
    .title-main {
        font-size: 3.5rem;
    }
    
    .title-accent {
        font-size: 2rem;
    }
    
    .hero-stats {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 30px;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: 50px;
        text-align: center;
    }
    
    .hero-content {
        flex-direction: column;
        text-align: center;
        gap: 40px;
    }
}

@media (max-width: 768px) {
    .main-nav {
        display: none;
    }
    
    .top-bar-content {
        justify-content: center;
    }
    
    .contact-info {
        gap: 15px;
    }
    
    .social-links {
        display: none;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .title-main {
        font-size: 3rem;
    }
    
    .title-accent {
        font-size: 1.8rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-visual {
        display: none;
    }
    
    .hero-controls {
        display: none;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .section-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .categories-grid,
    .products-grid,
    .services-grid,
    .news-grid {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }
    
    .feature-card {
        padding: 30px 20px;
    }
    
    .testimonials-slider {
        flex-direction: column;
        align-items: center;
    }
    
    .testimonial-card {
        flex: none;
        width: 100%;
        max-width: 400px;
    }
    
    .testimonials-controls {
        display: none;
    }
    
    .cta-title {
        font-size: 2.2rem;
    }
    
    .cta-description {
        font-size: 1.1rem;
    }
    
    .cta-features {
        flex-direction: column;
        gap: 20px;
    }
    
    .cta-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .partners-slider {
        gap: 30px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .stats-card {
        margin-top: 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .title-main {
        font-size: 2.5rem;
    }
    
    .title-accent {
        font-size: 1.5rem;
    }
    
    .btn-large {
        padding: 12px 20px;
        font-size: 16px;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .feature-icon,
    .service-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
    
    .category-card,
    .product-card,
    .news-card {
        border-radius: 15px;
    }
    
    .modal-content {
        margin: 20px;
        width: calc(100% - 40px);
    }
    
    .modal-header,
    .modal-body {
        padding: 20px;
    }
}

/* ============================================================================
   DESKTOP NAVIGATION
   ============================================================================ */

@media (min-width: 769px) {
    .main-nav {
        display: block;
    }
    
    .mobile-menu-toggle {
        display: none;
    }
    
    .action-btn {
        display: none;
    }
}

/* ============================================================================
   PRINT STYLES
   ============================================================================ */

@media print {
    .hero-section,
    .testimonials-section,
    .cta-section,
    .partners-section,
    .main-header,
    .top-bar {
        display: none;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.5;
        color: #000;
    }
    
    .section {
        padding: 20pt 0;
        page-break-inside: avoid;
    }
    
    .btn {
        display: none;
    }
}

/* ============================================================================
   ACCESSIBILITY
   ============================================================================ */

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .loading-spinner .spinner {
        animation: none;
    }
    
    .scroll-arrow {
        animation: none;
    }
}

/* Focus styles for accessibility */
.btn:focus,
.form-control:focus,
.nav-link:focus,
.hero-control:focus,
.hero-indicator:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #FF6B00;
        --text-dark: #000000;
        --text-light: #333333;
        --bg-light: #F5F5F5;
    }
    
    .hero-overlay {
        background: rgba(0, 0, 0, 0.8);
    }
    
    .card,
    .feature-card,
    .service-card {
        border: 2px solid #000;
    }
}
</style>

<!-- Page specific scripts -->
<?php
$page_scripts = [
    'home.js',
    'slider.js',
    'counter.js',
    'modal.js'
];
?>

<script>
// Ana sayfa JavaScript kodları
document.addEventListener('DOMContentLoaded', function() {
    
    // ============================================================================
    // HERO SLIDER
    // ============================================================================
    
    const heroSlider = {
        currentSlide: 0,
        slides: document.querySelectorAll('.hero-slide'),
        indicators: document.querySelectorAll('.hero-indicator'),
        prevBtn: document.querySelector('.hero-prev'),
        nextBtn: document.querySelector('.hero-next'),
        autoplayInterval: null,
        autoplayDelay: 5000,
        
        init() {
            this.bindEvents();
            this.startAutoplay();
        },
        
        bindEvents() {
            this.prevBtn?.addEventListener('click', () => this.prevSlide());
            this.nextBtn?.addEventListener('click', () => this.nextSlide());
            
            this.indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => this.goToSlide(index));
            });
            
            // Pause autoplay on hover
            const heroSection = document.querySelector('.hero-section');
            heroSection?.addEventListener('mouseenter', () => this.stopAutoplay());
            heroSection?.addEventListener('mouseleave', () => this.startAutoplay());
        },
        
        goToSlide(index) {
            this.slides[this.currentSlide]?.classList.remove('active');
            this.indicators[this.currentSlide]?.classList.remove('active');
            
            this.currentSlide = index;
            
            this.slides[this.currentSlide]?.classList.add('active');
            this.indicators[this.currentSlide]?.classList.add('active');
            
            // Update background
            const slide = this.slides[this.currentSlide];
            if (slide && slide.dataset.bg) {
                slide.style.backgroundImage = `url(${slide.dataset.bg})`;
            }
        },
        
        nextSlide() {
            const next = (this.currentSlide + 1) % this.slides.length;
            this.goToSlide(next);
        },
        
        prevSlide() {
            const prev = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
            this.goToSlide(prev);
        },
        
        startAutoplay() {
            this.stopAutoplay();
            this.autoplayInterval = setInterval(() => {
                this.nextSlide();
            }, this.autoplayDelay);
        },
        
        stopAutoplay() {
            if (this.autoplayInterval) {
                clearInterval(this.autoplayInterval);
            }
        }
    };
    
    // ============================================================================
    // COUNTER ANIMATION
    // ============================================================================
    
    function animateCounters() {
        const counters = document.querySelectorAll('[data-count]');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target;
                    const target = parseInt(counter.dataset.count);
                    const duration = 2000;
                    const step = target / (duration / 16);
                    let current = 0;
                    
                    const updateCounter = () => {
                        current += step;
                        if (current < target) {
                            counter.textContent = Math.floor(current);
                            requestAnimationFrame(updateCounter);
                        } else {
                            counter.textContent = target;
                        }
                    };
                    
                    updateCounter();
                    observer.unobserve(counter);
                }
            });
        }, { threshold: 0.5 });
        
        counters.forEach(counter => observer.observe(counter));
    }
    
    // ============================================================================
    // TESTIMONIAL SLIDER
    // ============================================================================
    
    const testimonialSlider = {
        slider: document.querySelector('.testimonials-slider'),
        prevBtn: document.querySelector('.testimonial-prev'),
        nextBtn: document.querySelector('.testimonial-next'),
        
        init() {
            if (!this.slider) return;
            
            this.prevBtn?.addEventListener('click', () => this.prev());
            this.nextBtn?.addEventListener('click', () => this.next());
        },
        
        prev() {
            this.slider.scrollBy({ left: -420, behavior: 'smooth' });
        },
        
        next() {
            this.slider.scrollBy({ left: 420, behavior: 'smooth' });
        }
    };
    
    // ============================================================================
    // QUICK CONTACT MODAL
    // ============================================================================
    
    const quickContactModal = {
        modal: document.getElementById('quick-contact-modal'),
        form: document.getElementById('quick-contact-form'),
        productInput: document.getElementById('contact-product'),
        
        init() {
            this.bindEvents();
        },
        
        bindEvents() {
            // Quick contact buttons
            document.querySelectorAll('.quick-contact').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    const productName = btn.dataset.product;
                    this.open(productName);
                });
            });
            
            // Close modal
            document.querySelectorAll('.modal-close').forEach(btn => {
                btn.addEventListener('click', () => this.close());
            });
            
            // Close on overlay click
            this.modal?.addEventListener('click', (e) => {
                if (e.target === this.modal) {
                    this.close();
                }
            });
            
            // Form submission
            this.form?.addEventListener('submit', (e) => this.handleSubmit(e));
        },
        
        open(productName = '') {
            if (this.productInput && productName) {
                this.productInput.value = productName;
            }
            this.modal?.classList.add('active');
            document.body.style.overflow = 'hidden';
        },
        
        close() {
            this.modal?.classList.remove('active');
            document.body.style.overflow = '';
            this.form?.reset();
        },
        
        async handleSubmit(e) {
            e.preventDefault();
            
            const formData = new FormData(this.form);
            const submitBtn = this.form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            try {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gönderiliyor...';
                submitBtn.disabled = true;
                
                const response = await fetch(this.form.action, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.showMessage('Mesajınız başarıyla gönderildi. En kısa sürede size dönüş yapacağız.', 'success');
                    this.close();
                } else {
                    this.showMessage(result.message || 'Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
                }
            } catch (error) {
                this.showMessage('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
            } finally {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        },
        
        showMessage(message, type) {
            // Simple alert for now - could be replaced with a toast system
            alert(message);
        }
    };
    
    // ============================================================================
    // SCROLL ANIMATIONS
    // ============================================================================
    
    function initScrollAnimations() {
        const animatedElements = document.querySelectorAll('.card, .feature-card, .service-card, .category-card, .product-card, .news-card');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });
        
        animatedElements.forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    }
    
    // ============================================================================
    // SMOOTH SCROLLING
    // ============================================================================
    
    function initSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Scroll indicator
        const scrollIndicator = document.querySelector('.scroll-indicator');
        scrollIndicator?.addEventListener('click', () => {
            const nextSection = document.querySelector('.features-section');
            nextSection?.scrollIntoView({ behavior: 'smooth' });
        });
    }
    
    // ============================================================================
    // LAZY LOADING
    // ============================================================================
    
    function initLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for older browsers
            images.forEach(img => {
                img.src = img.dataset.src;
            });
        }
    }
    
    // ============================================================================
    // INITIALIZE ALL
    // ============================================================================
    
    // Initialize all components
    heroSlider.init();
    testimonialSlider.init();
    quickContactModal.init();
    animateCounters();
    initScrollAnimations();
    initSmoothScrolling();
    initLazyLoading();
    
    // Remove loading screen
    setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.add('fade-out');
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
    }, 1000);
    
    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            quickContactModal.close();
        }
        
        if (e.key === 'ArrowLeft') {
            heroSlider.prevSlide();
        }
        
        if (e.key === 'ArrowRight') {
            heroSlider.nextSlide();
        }
    });
    
    // Track user interactions for analytics
    function trackEvent(action, category, label) {
        if (typeof gtag !== 'undefined') {
            gtag('event', action, {
                'event_category': category,
                'event_label': label
            });
        }
    }
    
    // Track important clicks
    document.addEventListener('click', (e) => {
        const target = e.target.closest('a, button');
        if (!target) return;
        
        // Track hero CTA clicks
        if (target.closest('.hero-actions')) {
            trackEvent('click', 'hero_cta', target.textContent.trim());
        }
        
        // Track product card clicks
        if (target.closest('.product-card')) {
            trackEvent('click', 'product_card', target.textContent.trim());
        }
        
        // Track category clicks
        if (target.closest('.category-card')) {
            trackEvent('click', 'category_card', target.textContent.trim());
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>