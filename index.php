<?php
// Ana sayfa - index.php
session_start();

// Config dosyalarını dahil et
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Sayfa bilgileri
$page_title = "Ana Sayfa - GNG Makine";
$page_description = "Profesyonel makine çözümleri ve endüstriyel ekipmanlar. GNG Makine ile kaliteli üretim, çalışan gü<PERSON>liği ve müşteri memnuniyeti.";
$current_page = "home";

// Veritabanından verileri çek
try {
    // Öne çıkan ürünler
    $stmt = $pdo->prepare("SELECT * FROM products WHERE status = 'active' AND featured = 1 ORDER BY sort_order ASC, created_at DESC LIMIT 8");
    $stmt->execute();
    $featured_products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Kategoriler
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE status = 'active' ORDER BY sort_order ASC LIMIT 4");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Son blog yazıları
    $stmt = $pdo->prepare("SELECT * FROM articles WHERE status = 'published' AND published_at <= NOW() ORDER BY published_at DESC LIMIT 3");
    $stmt->execute();
    $latest_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Site istatistikleri
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_products FROM products WHERE status = 'active'");
    $stmt->execute();
    $stats['products'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_products'];

    $stmt = $pdo->prepare("SELECT COUNT(*) as total_categories FROM categories WHERE status = 'active'");
    $stmt->execute();
    $stats['categories'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_categories'];

} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    $featured_products = [];
    $categories = [];
    $latest_articles = [];
    $stats = ['products' => 0, 'categories' => 0];
}
?>

<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta name="keywords" content="makine, endüstriyel, üretim, GNG, profesyonel">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/logo/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/main.css">
    <link rel="stylesheet" href="/assets/css/components.css">
    <link rel="stylesheet" href="/assets/css/responsive.css">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        /* Ana Sayfa Özel Stilleri */
        :root {
            --primary-color: #f39c12;
            --secondary-color: #2c3e50;
            --accent-color: #e67e22;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --bg-light: #f8f9fa;
            --white: #ffffff;
            --shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background: var(--white);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #34495e 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            width: 100%;
        }

        .hero .container {
            max-width: 100%;
            width: 100%;
            margin: 0;
            padding: 0 20px;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('/assets/images/backgrounds/hero-pattern.svg') no-repeat center;
            background-size: cover;
            opacity: 0.1;
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .hero-text h1 {
            font-size: 3.5rem;
            font-weight: 800;
            color: var(--white);
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .hero-text .highlight {
            color: var(--primary-color);
        }

        .hero-text p {
            font-size: 1.25rem;
            color: #bdc3c7;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .hero-stats {
            display: flex;
            gap: 40px;
            margin-top: 40px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #95a5a6;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .hero-visual {
            position: relative;
            height: 500px;
        }

        .hero-logo {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 20px 60px rgba(243, 156, 18, 0.3);
        }

        .hero-logo .logo-text {
            font-size: 4rem;
            font-weight: 900;
            color: var(--white);
            letter-spacing: -2px;
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .floating-icon {
            position: absolute;
            color: var(--primary-color);
            opacity: 0.3;
            animation: float 6s ease-in-out infinite;
        }

        .floating-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-icon:nth-child(2) { top: 20%; right: 15%; animation-delay: 1s; }
        .floating-icon:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 2s; }
        .floating-icon:nth-child(4) { bottom: 15%; right: 10%; animation-delay: 3s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* Kategoriler Section */
        .categories-section {
            padding: 100px 0;
            background: var(--bg-light);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section-title {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--secondary-color);
            margin-bottom: 15px;
        }

        .section-title p {
            font-size: 1.1rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
        }

        .category-card {
            background: var(--white);
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .category-card:hover {
            transform: translateY(-10px);
            border-color: var(--primary-color);
            box-shadow: 0 20px 40px rgba(243, 156, 18, 0.2);
        }

        .category-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: var(--white);
            font-size: 2rem;
        }

        .category-card h3 {
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 10px;
        }

        .category-card p {
            color: var(--text-light);
            font-size: 0.95rem;
        }

        /* Ürünler Section */
        .products-section {
            padding: 100px 0;
            background: var(--white);
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .product-card {
            background: var(--white);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border: 1px solid #f1f2f6;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .product-image {
            height: 200px;
            background: var(--bg-light);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image img {
            transform: scale(1.05);
        }

        .product-info {
            padding: 25px;
        }

        .product-info h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .product-info p {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .product-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #f1f2f6;
        }

        .product-category {
            background: var(--primary-color);
            color: var(--white);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .view-details {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .view-details:hover {
            color: var(--accent-color);
        }

        /* CTA Section */
        .cta-section {
            padding: 80px 0;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            text-align: center;
            color: var(--white);
        }

        .cta-content h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .cta-content p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-primary {
            background: var(--white);
            color: var(--primary-color);
            border: 2px solid var(--white);
        }

        .btn-primary:hover {
            background: transparent;
            color: var(--white);
        }

        .btn-outline {
            background: transparent;
            color: var(--white);
            border: 2px solid var(--white);
        }

        .btn-outline:hover {
            background: var(--white);
            color: var(--primary-color);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 40px;
            }

            .hero-text h1 {
                font-size: 2.5rem;
            }

            .hero-stats {
                justify-content: center;
                gap: 30px;
            }

            .hero-visual {
                height: 300px;
            }

            .hero-logo {
                width: 200px;
                height: 200px;
            }

            .hero-logo .logo-text {
                font-size: 3rem;
            }

            .section-title h2 {
                font-size: 2rem;
            }

            .categories-grid {
                grid-template-columns: 1fr;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }

            .cta-content h2 {
                font-size: 2rem;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    <?php include 'includes/navbar.php'; ?>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Profesyonel <span class="highlight">Makine</span> Çözümleri</h1>
                <p>Endüstriyel kalitede makineler ile üretiminizi artırın, verimliliğinizi maksimuma çıkarın. GNG Makine güvencesi ile geleceğe yatırım yapın.</p>
                
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $stats['products']; ?>+</span>
                        <span class="stat-label">Ürün</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $stats['categories']; ?></span>
                        <span class="stat-label">Kategori</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">15+</span>
                        <span class="stat-label">Yıl Tecrübe</span>
                    </div>
                </div>
            </div>
            
            <div class="hero-visual">
                <div class="hero-logo">
                    <span class="logo-text">W</span>
                </div>
                <div class="floating-elements">
                    <i class="floating-icon fas fa-cog fa-2x"></i>
                    <i class="floating-icon fas fa-industry fa-2x"></i>
                    <i class="floating-icon fas fa-tools fa-2x"></i>
                    <i class="floating-icon fas fa-chart-line fa-2x"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Kategoriler Section -->
    <section class="categories-section">
        <div class="container">
            <div class="section-title">
                <h2>Makine Kategorileri</h2>
                <p>Farklı sektörlere özel tasarlanmış profesyonel makine çözümlerimizi keşfedin</p>
            </div>
            
            <div class="categories-grid">
                <?php if (!empty($categories)): ?>
                    <?php 
                    $category_icons = [
                        'uretim' => 'fas fa-industry',
                        'calisan' => 'fas fa-users-cog',
                        'alan' => 'fas fa-map-marked-alt',
                        'musteri' => 'fas fa-handshake'
                    ];
                    ?>
                    <?php foreach ($categories as $category): ?>
                        <div class="category-card">
                            <div class="category-icon">
                                <i class="<?php echo $category_icons[$category['slug']] ?? 'fas fa-cog'; ?>"></i>
                            </div>
                            <h3><?php echo htmlspecialchars($category['name']); ?></h3>
                            <p><?php echo htmlspecialchars($category['description'] ?? 'Bu kategorideki profesyonel makine çözümlerini keşfedin.'); ?></p>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Varsayılan kategoriler -->
                    <div class="category-card">
                        <div class="category-icon"><i class="fas fa-industry"></i></div>
                        <h3>Üretim Makineleri</h3>
                        <p>Yüksek kaliteli üretim için tasarlanmış endüstriyel makineler</p>
                    </div>
                    <div class="category-card">
                        <div class="category-icon"><i class="fas fa-users-cog"></i></div>
                        <h3>Çalışan Odaklı</h3>
                        <p>İşçi güvenliği ve verimliliği için optimize edilmiş makineler</p>
                    </div>
                    <div class="category-card">
                        <div class="category-icon"><i class="fas fa-map-marked-alt"></i></div>
                        <h3>Alan Makineleri</h3>
                        <p>Geniş alan kullanımı için tasarlanmış güçlü makineler</p>
                    </div>
                    <div class="category-card">
                        <div class="category-icon"><i class="fas fa-handshake"></i></div>
                        <h3>Müşteri Çözümleri</h3>
                        <p>Müşteri ihtiyaçlarına özel tasarım ve üretim hizmetleri</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Öne Çıkan Ürünler -->
    <section class="products-section">
        <div class="container">
            <div class="section-title">
                <h2>Öne Çıkan Ürünler</h2>
                <p>En popüler ve yenilikçi makine modellerimizi inceleyin</p>
            </div>
            
            <div class="products-grid">
                <?php if (!empty($featured_products)): ?>
                    <?php foreach ($featured_products as $product): ?>
                        <div class="product-card">
                            <div class="product-image">
                                <?php if ($product['main_image']): ?>
                                    <img src="/uploads/products/<?php echo htmlspecialchars($product['main_image']); ?>" 
                                         alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <i class="fas fa-cog fa-4x" style="color: var(--text-light);"></i>
                                <?php endif; ?>
                            </div>
                            <div class="product-info">
                                <h3><?php echo htmlspecialchars($product['name']); ?></h3>
                                <p><?php echo htmlspecialchars(substr($product['description'] ?? '', 0, 120)); ?>...</p>
                                <div class="product-meta">
                                    <span class="product-category">Makine</span>
                                    <a href="/product-detail.php?slug=<?php echo urlencode($product['slug']); ?>" class="view-details">
                                        Detayları Gör <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Varsayılan ürün kartları -->
                    <?php for ($i = 1; $i <= 6; $i++): ?>
                        <div class="product-card">
                            <div class="product-image">
                                <i class="fas fa-cog fa-4x" style="color: var(--text-light);"></i>
                            </div>
                            <div class="product-info">
                                <h3>Örnek Makine <?php echo $i; ?></h3>
                                <p>Bu makine hakkında detaylı bilgi ve özellikler burada yer alacak...</p>
                                <div class="product-meta">
                                    <span class="product-category">Makine</span>
                                    <a href="#" class="view-details">Detayları Gör <i class="fas fa-arrow-right"></i></a>
                                </div>
                            </div>
                        </div>
                    <?php endfor; ?>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Projeleriniz İçin Doğru Çözüm</h2>
                <p>Uzman ekibimiz size en uygun makine çözümünü bulmak için hazır. Hemen iletişime geçin!</p>
                <div class="cta-buttons">
                    <a href="/products.php" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        Ürünleri İncele
                    </a>
                    <a href="/contact.php" class="btn btn-outline">
                        <i class="fas fa-phone"></i>
                        İletişime Geç
                    </a>
                </div>
            </div>
        </div>
    </section>

    <?php include 'includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="/assets/js/main.js"></script>
    <script src="/assets/js/components.js"></script>
    <script src="/assets/js/animations.js"></script>

    <script>
        // Ana sayfa özel JavaScript kodları
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scrolling
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Hero stats counter animation
            const statsNumbers = document.querySelectorAll('.stat-number');
            const observerOptions = {
                threshold: 0.5,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = entry.target;
                        const finalNumber = parseInt(target.textContent);
                        animateCounter(target, finalNumber);
                        observer.unobserve(target);
                    }
                });
            }, observerOptions);

            statsNumbers.forEach(stat => {
                observer.observe(stat);
            });

            function animateCounter(element, target) {
                let current = 0;
                const increment = target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    element.textContent = Math.floor(current) + (target >= 10 ? '+' : '');
                }, 30);
            }

            // Parallax effect for hero section
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const heroSection = document.querySelector('.hero');
                if (heroSection) {
                    const rate = scrolled * -0.5;
                    heroSection.style.transform = `translateY(${rate}px)`;
                }
            });

            // Card hover effects
            const cards = document.querySelectorAll('.category-card, .product-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>