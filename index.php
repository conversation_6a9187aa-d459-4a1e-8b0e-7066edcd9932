<?php
/**
 * GNG Makine - <PERSON>
 */

// Konfigürasyonu dahil et
require_once __DIR__ . '/config/config.php';

// Sayfa meta bilgileri
$page_title = "Ana Sayfa - " . SITE_NAME;
$page_description = "Endüstriyel makine sektöründe 15+ yıllık tecrübemizle kaliteli çözümler sunuyoruz. CNC, torna, freze, kaynak makineleri ve daha fazlası.";
$page_keywords = "endüstriyel makine, CNC makine, torna, freze, kaynak makinesi, GNG Makine";
$current_page = "home";

// Veritabanından verileri çek
try {
    // Öne çıkan ürünler
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name, c.slug as category_slug
                          FROM products p
                          LEFT JOIN categories c ON p.category_id = c.id
                          WHERE p.status = 'active' AND p.featured = 1
                          ORDER BY p.sort_order ASC, p.created_at DESC LIMIT 8");
    $stmt->execute();
    $featured_products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Kategoriler
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE status = 'active' ORDER BY sort_order ASC LIMIT 6");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Son blog yazıları
    $stmt = $pdo->prepare("SELECT * FROM articles WHERE status = 'published' AND published_at <= NOW() ORDER BY published_at DESC LIMIT 3");
    $stmt->execute();
    $latest_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Site istatistikleri
    $stats = [
        'experience' => '15+',
        'customers' => '500+',
        'projects' => '1000+',
        'support' => '24/7'
    ];

} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    $featured_products = [];
    $categories = [];
    $latest_articles = [];
    $stats = [
        'experience' => '15+',
        'customers' => '500+',
        'projects' => '1000+',
        'support' => '24/7'
    ];
}

include 'includes/header.php';
?>
<style>
/* Ana Sayfa Özel Stilleri */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--text-dark) 0%, #34495e 100%);
    min-height: 90vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    color: var(--white);
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('<?php echo ASSETS_URL; ?>/images/patterns/dots.png') repeat;
    opacity: 0.1;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 25px;
    color: var(--white);
}

.hero-text .highlight {
    color: var(--primary-color);
}

.hero-text p {
    font-size: 1.3rem;
    line-height: 1.6;
    margin-bottom: 40px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-image {
    width: 100%;
    max-width: 500px;
    height: 400px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 5rem;
    color: var(--primary-color);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Stats Section */
.stats-section {
    padding: 80px 0;
    background: var(--primary-color);
    color: var(--white);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    text-align: center;
}

.stat-item {
    padding: 30px 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    display: block;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 500;
    opacity: 0.9;
}

/* Categories Section */
.categories-section {
    padding: 100px 0;
    background: var(--white);
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.category-card {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    text-decoration: none;
    color: inherit;
}

.category-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: 0 20px 40px rgba(243, 156, 18, 0.2);
}

.category-image {
    height: 200px;
    background: var(--bg-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--primary-color);
    position: relative;
    overflow: hidden;
}

.category-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    opacity: 0.1;
}

.category-info {
    padding: 30px;
    text-align: center;
}

.category-info h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.category-info p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 20px;
}

.category-link {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.category-link:hover {
    color: var(--accent-color);
    transform: translateX(5px);
}

/* Products Section */
.products-section {
    padding: 100px 0;
    background: var(--bg-light);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.product-card {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.product-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.product-image {
    height: 220px;
    background: var(--bg-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--primary-color);
    position: relative;
    overflow: hidden;
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--primary-color);
    color: var(--white);
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-info {
    padding: 25px;
}

.product-category {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 8px;
}

.product-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 12px;
    line-height: 1.3;
}

.product-description {
    color: var(--text-light);
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-btn {
    background: var(--primary-color);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.product-btn:hover {
    background: var(--accent-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* CTA Section */
.cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 50px;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .categories-grid,
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}
</style>

<?php include 'includes/navbar.php'; ?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Endüstriyel Makine <span class="highlight">Çözümleriniz</span></h1>
                <p>15 yılı aşkın tecrübemizle endüstriyel makine sektöründe güvenilir çözüm ortağınızız. Kalite, güvenilirlik ve müşteri memnuniyeti odaklı hizmet anlayışımızla sektörde öncü konumdayız.</p>

                <div class="hero-buttons">
                    <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        Ürünleri İncele
                    </a>
                    <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-outline">
                        <i class="fas fa-phone"></i>
                        İletişime Geç
                    </a>
                </div>
            </div>

            <div class="hero-visual">
                <div class="hero-image">
                    <i class="fas fa-industry"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="stats-section">
    <div class="container">
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-number"><?php echo $stats['experience']; ?></span>
                <div class="stat-label">Yıllık Tecrübe</div>
            </div>
            <div class="stat-item">
                <span class="stat-number"><?php echo $stats['customers']; ?></span>
                <div class="stat-label">Mutlu Müşteri</div>
            </div>
            <div class="stat-item">
                <span class="stat-number"><?php echo $stats['projects']; ?></span>
                <div class="stat-label">Tamamlanan Proje</div>
            </div>
            <div class="stat-item">
                <span class="stat-number"><?php echo $stats['support']; ?></span>
                <div class="stat-label">Teknik Destek</div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="categories-section">
    <div class="container">
        <div class="section-header">
            <h2>Ürün Kategorilerimiz</h2>
            <p>Geniş ürün yelpazemizle endüstriyel ihtiyaçlarınıza uygun çözümler sunuyoruz.</p>
        </div>

        <div class="categories-grid">
            <?php if (!empty($categories)): ?>
                <?php foreach ($categories as $category): ?>
                <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo urlencode($category['slug']); ?>" class="category-card">
                    <div class="category-image">
                        <i class="fas fa-<?php echo $category['icon'] ?? 'cog'; ?>"></i>
                    </div>
                    <div class="category-info">
                        <h3><?php echo htmlspecialchars($category['name']); ?></h3>
                        <p><?php echo htmlspecialchars($category['description'] ?? 'Kategori açıklaması'); ?></p>
                        <span class="category-link">
                            Ürünleri Gör <i class="fas fa-arrow-right"></i>
                        </span>
                    </div>
                </a>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- Varsayılan kategoriler -->
                <a href="<?php echo BASE_URL; ?>/products.php?category=cnc-torna" class="category-card">
                    <div class="category-image">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="category-info">
                        <h3>CNC Torna</h3>
                        <p>Hassas işleme için CNC torna makineleri</p>
                        <span class="category-link">
                            Ürünleri Gör <i class="fas fa-arrow-right"></i>
                        </span>
                    </div>
                </a>

                <a href="<?php echo BASE_URL; ?>/products.php?category=freze" class="category-card">
                    <div class="category-image">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="category-info">
                        <h3>Freze Makineleri</h3>
                        <p>Çok amaçlı freze makineleri</p>
                        <span class="category-link">
                            Ürünleri Gör <i class="fas fa-arrow-right"></i>
                        </span>
                    </div>
                </a>

                <a href="<?php echo BASE_URL; ?>/products.php?category=kaynak" class="category-card">
                    <div class="category-image">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="category-info">
                        <h3>Kaynak Makineleri</h3>
                        <p>Profesyonel kaynak ekipmanları</p>
                        <span class="category-link">
                            Ürünleri Gör <i class="fas fa-arrow-right"></i>
                        </span>
                    </div>
                </a>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="products-section">
    <div class="container">
        <div class="section-header">
            <h2>Öne Çıkan Ürünlerimiz</h2>
            <p>En çok tercih edilen ve kaliteli ürünlerimizi keşfedin.</p>
        </div>

        <div class="products-grid">
            <?php if (!empty($featured_products)): ?>
                <?php foreach ($featured_products as $product): ?>
                <div class="product-card">
                    <div class="product-image">
                        <?php if (!empty($product['image'])): ?>
                            <img src="<?php echo UPLOADS_URL . '/' . $product['image']; ?>"
                                 alt="<?php echo htmlspecialchars($product['name']); ?>">
                        <?php else: ?>
                            <i class="fas fa-cog"></i>
                        <?php endif; ?>

                        <?php if ($product['featured']): ?>
                            <div class="product-badge">Öne Çıkan</div>
                        <?php endif; ?>
                    </div>

                    <div class="product-info">
                        <?php if (!empty($product['category_name'])): ?>
                            <div class="product-category"><?php echo htmlspecialchars($product['category_name']); ?></div>
                        <?php endif; ?>

                        <h3 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h3>

                        <?php if (!empty($product['short_description'])): ?>
                            <p class="product-description"><?php echo htmlspecialchars($product['short_description']); ?></p>
                        <?php endif; ?>

                        <div class="product-footer">
                            <a href="<?php echo BASE_URL; ?>/product.php?slug=<?php echo urlencode($product['slug']); ?>"
                               class="product-btn">
                                Detayları Gör
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- Varsayılan ürünler -->
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-cog"></i>
                        <div class="product-badge">Öne Çıkan</div>
                    </div>
                    <div class="product-info">
                        <div class="product-category">CNC Torna</div>
                        <h3 class="product-title">CNC Torna Makinesi</h3>
                        <p class="product-description">Yüksek hassasiyetli CNC torna makinesi</p>
                        <div class="product-footer">
                            <a href="<?php echo BASE_URL; ?>/product.php?slug=cnc-torna" class="product-btn">
                                Detayları Gör
                            </a>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-category">Freze</div>
                        <h3 class="product-title">Freze Makinesi</h3>
                        <p class="product-description">Çok amaçlı freze makinesi</p>
                        <div class="product-footer">
                            <a href="<?php echo BASE_URL; ?>/product.php?slug=freze" class="product-btn">
                                Detayları Gör
                            </a>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-category">Kaynak</div>
                        <h3 class="product-title">Kaynak Makinesi</h3>
                        <p class="product-description">Profesyonel kaynak ekipmanı</p>
                        <div class="product-footer">
                            <a href="<?php echo BASE_URL; ?>/product.php?slug=kaynak" class="product-btn">
                                Detayları Gör
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <div class="text-center" style="margin-top: 50px;">
            <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-primary">
                <i class="fas fa-th-large"></i>
                Tüm Ürünleri Gör
            </a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <h2>Projeleriniz İçin Doğru Çözüm</h2>
            <p>Endüstriyel makine ihtiyaçlarınız için uzman ekibimizle iletişime geçin. Size özel çözümler sunalım.</p>

            <div class="cta-buttons">
                <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-secondary">
                    <i class="fas fa-phone"></i>
                    Hemen Arayın
                </a>
                <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-outline">
                    <i class="fas fa-search"></i>
                    Ürünleri İncele
                </a>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>