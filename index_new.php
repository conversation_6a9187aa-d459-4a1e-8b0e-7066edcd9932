<?php
/**
 * GNG Makine - Modern Animasyonlu Ana Sayfa
 */

// Konfigürasyonu dahil et
require_once __DIR__ . '/config/config.php';

// Sayfa meta bilgileri
$page_title = "Ana Sayfa - " . SITE_NAME;
$page_description = "Endüstriyel makine sektöründe 15+ yıllık tecrübemizle kaliteli çözümler sunuyoruz. CNC, torna, freze, kaynak makineleri ve daha fazlası.";
$page_keywords = "endüstriyel makine, CNC makine, torna, freze, kaynak makinesi, GNG Makine";
$current_page = "home";

// Veritabanından verileri çek
try {
    // Öne çıkan ürünler
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name, c.slug as category_slug
                          FROM products p
                          LEFT JOIN categories c ON p.category_id = c.id
                          WHERE p.status = 'active' AND p.featured = 1
                          ORDER BY p.sort_order ASC, p.created_at DESC LIMIT 8");
    $stmt->execute();
    $featured_products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Kategoriler
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE status = 'active' ORDER BY sort_order ASC LIMIT 6");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Son blog yazıları
    $stmt = $pdo->prepare("SELECT * FROM articles WHERE status = 'published' ORDER BY created_at DESC LIMIT 3");
    $stmt->execute();
    $latest_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Site istatistikleri
    $stats = [
        'experience' => '15+',
        'customers' => '500+',
        'projects' => '1000+',
        'support' => '24/7'
    ];

} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    $featured_products = [];
    $categories = [];
    $latest_articles = [];
    $stats = [
        'experience' => '15+',
        'customers' => '500+',
        'projects' => '1000+',
        'support' => '24/7'
    ];
}

// Hizmetler verisi
$services = [
    [
        'icon' => 'fas fa-shopping-cart',
        'title' => 'Makine Satışı',
        'description' => 'Yeni ve ikinci el makine seçenekleri'
    ],
    [
        'icon' => 'fas fa-tools',
        'title' => 'Teknik Destek',
        'description' => '7/24 uzman teknisyen desteği'
    ],
    [
        'icon' => 'fas fa-wrench',
        'title' => 'Bakım & Onarım',
        'description' => 'Profesyonel bakım ve onarım hizmetleri'
    ],
    [
        'icon' => 'fas fa-cogs',
        'title' => 'Yedek Parça',
        'description' => 'Geniş yedek parça stoğu'
    ]
];

// Sertifikalar
$certificates = [
    'ISO 9001:2015 Kalite Yönetim Sistemi',
    'ISO 14001:2015 Çevre Yönetim Sistemi',
    'OHSAS 18001 İş Sağlığı ve Güvenliği',
    'CE Uygunluk Beyanı',
    'TSE Hizmet Yeterlilik Belgesi',
    'Kaizen Sürekli İyileştirme Sertifikası'
];

include 'includes/header.php';
?>

<style>
:root {
    --primary: #F9B233;
    --primary-dark: #e6a02e;
    --secondary: #8A8C8F;
    --accent: #e67e22;
    --dark: #2C3E50;
    --light: #ECF0F1;
    --white: #FFFFFF;
    --text-dark: #2C3E50;
    --text-light: #7F8C8D;
    --shadow: 0 10px 30px rgba(0,0,0,0.1);
    --shadow-hover: 0 20px 40px rgba(0,0,0,0.15);
    --transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
    --gradient-dark: linear-gradient(135deg, var(--dark) 0%, #34495e 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Parallax Hero Section */
.hero {
    height: 100vh;
    background: var(--gradient-dark);
    color: var(--white);
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMiIgZmlsbD0iIzNFNzNFRiIgZmlsbC1vcGFjaXR5PSIwLjEiLz4KPC9zdmc+');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.particle {
    position: absolute;
    background: var(--primary);
    border-radius: 50%;
    animation: particle-float 15s linear infinite;
    opacity: 0.3;
}

@keyframes particle-float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% { opacity: 0.3; }
    90% { opacity: 0.3; }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.hero-text {
    opacity: 0;
    transform: translateX(-50px);
    animation: slideInLeft 1s ease 0.5s forwards;
}

@keyframes slideInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hero-text h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 25px;
    background: linear-gradient(45deg, var(--white), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-text .highlight {
    position: relative;
    display: inline-block;
}

.hero-text .highlight::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--primary);
    animation: underline 2s ease 1.5s forwards;
}

@keyframes underline {
    to { width: 100%; }
}

.hero-text p {
    font-size: 1.2rem;
    line-height: 1.7;
    margin-bottom: 40px;
    opacity: 0.9;
    color: #bdc3c7;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: 0 10px 30px rgba(249, 178, 51, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(249, 178, 51, 0.4);
}

.btn-outline {
    background: transparent;
    color: var(--white);
    border-color: var(--white);
}

.btn-outline:hover {
    background: var(--white);
    color: var(--dark);
    transform: translateY(-3px);
}

.hero-visual {
    position: relative;
    opacity: 0;
    transform: translateX(50px);
    animation: slideInRight 1s ease 0.7s forwards;
}

@keyframes slideInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hero-image {
    width: 100%;
    max-width: 500px;
    height: 400px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 5rem;
    color: var(--primary);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.hero-image::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(249, 178, 51, 0.1), transparent);
    animation: shimmer 3s linear infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}
</style>
