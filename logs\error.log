[04-Jul-2025 19:28:30 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 19:28:30 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `active_products`
[04-Jul-2025 19:28:37 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 19:28:37 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `active_products`
[04-Jul-2025 19:31:34 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 19:31:34 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 19:31:34 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 19:31:34 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 19:31:34 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 19:31:34 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 19:31:34 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 19:31:34 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 19:31:34 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 19:31:34 Europe/Istanbul] PHP Warning:  Undefined variable $unread_messages in C:\xampp\htdocs\gngmakine\admin\includes\header.php on line 485
[04-Jul-2025 19:31:39 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 19:31:39 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 19:31:39 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 19:31:39 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 19:31:39 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 19:31:39 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 19:31:39 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 19:31:39 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 19:31:39 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 19:32:12 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 19:32:12 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 19:32:12 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 19:32:12 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 19:32:12 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 19:32:12 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 19:32:12 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 19:32:12 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 19:32:12 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 19:39:05 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 19:39:05 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 19:39:05 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 19:39:05 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 19:39:05 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 19:39:05 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 19:39:05 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 19:39:05 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 19:39:05 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 19:41:48 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 19:41:48 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 19:41:48 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 19:41:48 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 19:41:48 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 19:41:48 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 19:41:48 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 19:41:48 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 19:41:48 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 19:44:34 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 19:44:34 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 19:44:34 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 19:44:34 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 19:44:34 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 19:44:34 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 19:44:34 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 19:44:34 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 19:44:34 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 19:45:17 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 19:45:17 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 19:45:17 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 19:45:17 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 19:45:17 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 19:45:17 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 19:45:17 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 19:45:17 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 19:45:17 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 19:46:22 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 19:46:22 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 19:46:22 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 19:46:22 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 19:46:22 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 19:46:22 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 19:46:22 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 19:46:22 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 19:46:22 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 19:46:32 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 19:46:32 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 19:46:32 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 19:46:32 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 19:46:32 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 19:46:32 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 19:46:32 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 19:46:32 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 19:46:32 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 19:55:11 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 19:55:11 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 19:55:11 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 19:55:11 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 19:55:11 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 19:55:11 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 19:55:11 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 19:55:11 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 19:55:11 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 19:55:17 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 19:55:17 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 19:55:17 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 19:55:17 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 19:55:17 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 19:55:17 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 19:55:17 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 19:55:17 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 19:55:17 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 20:03:46 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 20:03:46 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 20:03:46 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 20:03:46 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 20:03:46 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 20:03:46 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 20:03:46 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 20:03:46 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 20:03:46 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 20:04:02 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 20:04:02 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 20:04:02 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 20:04:02 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 20:04:02 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 20:04:02 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 20:04:02 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 20:04:02 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 20:04:02 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 20:04:58 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 20:04:58 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 20:04:58 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 20:04:58 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 20:04:58 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 20:04:58 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 20:04:58 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 20:04:58 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 20:04:58 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 20:05:38 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 20:05:38 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 20:05:38 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 20:05:38 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 20:05:38 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 20:05:38 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 20:05:38 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 20:05:38 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 20:05:38 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 20:05:58 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 20:05:58 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 20:05:58 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 20:05:58 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 20:05:58 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 20:05:58 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 20:05:58 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 20:05:58 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 20:05:58 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
[04-Jul-2025 20:05:59 Europe/Istanbul] SQL Query #1: SHOW TABLES
[04-Jul-2025 20:05:59 Europe/Istanbul] SQL Query #2: SELECT COUNT(*) as count FROM `admin_users`
[04-Jul-2025 20:05:59 Europe/Istanbul] SQL Query #3: SELECT COUNT(*) as count FROM `articles`
[04-Jul-2025 20:05:59 Europe/Istanbul] SQL Query #4: SELECT COUNT(*) as count FROM `categories`
[04-Jul-2025 20:05:59 Europe/Istanbul] SQL Query #5: SELECT COUNT(*) as count FROM `contact_messages`
[04-Jul-2025 20:05:59 Europe/Istanbul] SQL Query #6: SELECT COUNT(*) as count FROM `media`
[04-Jul-2025 20:05:59 Europe/Istanbul] SQL Query #7: SELECT COUNT(*) as count FROM `newsletter_subscribers`
[04-Jul-2025 20:05:59 Europe/Istanbul] SQL Query #8: SELECT COUNT(*) as count FROM `products`
[04-Jul-2025 20:05:59 Europe/Istanbul] SQL Query #9: SELECT COUNT(*) as count FROM `site_settings`
