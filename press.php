<?php
require_once 'config/config.php';

// Sayfa bilgileri
$page_title = 'Basın - GNG Makine';
$page_description = 'GNG Makine basın açıklamaları, haberler ve medya içerikleri';

// Basın açıklamalarını getir (articles tablosundan press kategorisindekiler)
try {
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name 
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        WHERE a.status = 'published' AND (c.name = 'Basın' OR c.name = 'Press' OR a.category_id = 0)
        ORDER BY a.created_at DESC 
        LIMIT 20
    ");
    $stmt->execute();
    $press_articles = $stmt->fetchAll();
} catch (PDOException $e) {
    $press_articles = [];
}

// Son haberler için ayrı sorgu
try {
    $stmt = $pdo->prepare("
        SELECT * FROM articles 
        WHERE status = 'published' 
        ORDER BY created_at DESC 
        LIMIT 6
    ");
    $stmt->execute();
    $latest_news = $stmt->fetchAll();
} catch (PDOException $e) {
    $latest_news = [];
}
?>

<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary: #F9B233;
            --secondary: #8A8C8F;
            --dark: #2C3E50;
            --light: #ECF0F1;
            --white: #FFFFFF;
            --text-dark: #2C3E50;
            --text-light: #7F8C8D;
            --shadow: 0 10px 30px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
            --border-radius: 15px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: var(--white);
            padding: 100px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Main Content */
        .main-content {
            padding: 80px 0;
        }

        .section-title {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 15px;
        }

        .section-title p {
            font-size: 1.1rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Press Grid */
        .press-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .press-card {
            background: var(--white);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            position: relative;
        }

        .press-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .press-card-image {
            height: 200px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            position: relative;
            overflow: hidden;
        }

        .press-card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .press-card:hover .press-card-image img {
            transform: scale(1.1);
        }

        .press-card-content {
            padding: 25px;
        }

        .press-card-date {
            color: var(--primary);
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .press-card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .press-card-excerpt {
            color: var(--text-light);
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .press-card-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }

        .press-card-link:hover {
            color: var(--secondary);
            transform: translateX(5px);
        }

        /* Latest News Sidebar */
        .sidebar {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow);
            height: fit-content;
            position: sticky;
            top: 120px;
        }

        .sidebar-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--primary);
        }

        .news-item {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .news-item:last-child {
            border-bottom: none;
        }

        .news-item-date {
            color: var(--text-light);
            font-size: 0.8rem;
            margin-bottom: 5px;
        }

        .news-item-title {
            font-size: 0.95rem;
            font-weight: 600;
            color: var(--text-dark);
            text-decoration: none;
            line-height: 1.4;
            transition: var(--transition);
        }

        .news-item-title:hover {
            color: var(--primary);
        }

        /* Two Column Layout */
        .content-layout {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            align-items: start;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero p {
                font-size: 1.1rem;
            }
            
            .press-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .content-layout {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .sidebar {
                position: static;
            }
            
            .section-title h2 {
                font-size: 2rem;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .press-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .press-card:nth-child(2) { animation-delay: 0.1s; }
        .press-card:nth-child(3) { animation-delay: 0.2s; }
        .press-card:nth-child(4) { animation-delay: 0.3s; }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1><i class="fas fa-newspaper"></i> Basın</h1>
            <p>GNG Makine'nin son gelişmeleri, basın açıklamaları ve medya içerikleri</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="content-layout">
                <!-- Press Articles -->
                <div class="press-content">
                    <div class="section-title">
                        <h2>Basın Açıklamaları</h2>
                        <p>Şirketimizin son gelişmeleri ve önemli duyuruları</p>
                    </div>

                    <?php if (!empty($press_articles)): ?>
                        <div class="press-grid">
                            <?php foreach ($press_articles as $article): ?>
                                <article class="press-card">
                                    <div class="press-card-image">
                                        <?php if (!empty($article['featured_image'])): ?>
                                            <img src="<?php echo UPLOADS_URL; ?>/articles/<?php echo htmlspecialchars($article['featured_image']); ?>" 
                                                 alt="<?php echo htmlspecialchars($article['title']); ?>">
                                        <?php else: ?>
                                            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: white; font-size: 3rem;">
                                                <i class="fas fa-newspaper"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="press-card-content">
                                        <div class="press-card-date">
                                            <i class="fas fa-calendar-alt"></i>
                                            <?php echo date('d.m.Y', strtotime($article['created_at'])); ?>
                                        </div>
                                        <h3 class="press-card-title">
                                            <?php echo htmlspecialchars($article['title']); ?>
                                        </h3>
                                        <?php if (!empty($article['excerpt'])): ?>
                                            <p class="press-card-excerpt">
                                                <?php echo htmlspecialchars(substr($article['excerpt'], 0, 150)) . '...'; ?>
                                            </p>
                                        <?php endif; ?>
                                        <a href="article.php?slug=<?php echo urlencode($article['slug']); ?>" class="press-card-link">
                                            Devamını Oku <i class="fas fa-arrow-right"></i>
                                        </a>
                                    </div>
                                </article>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div style="text-align: center; padding: 60px 20px; color: var(--text-light);">
                            <i class="fas fa-newspaper" style="font-size: 4rem; margin-bottom: 20px; opacity: 0.3;"></i>
                            <h3>Henüz basın açıklaması bulunmuyor</h3>
                            <p>Yakında yeni içerikler eklenecektir.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <aside class="sidebar">
                    <h3 class="sidebar-title">
                        <i class="fas fa-clock"></i> Son Haberler
                    </h3>
                    
                    <?php if (!empty($latest_news)): ?>
                        <?php foreach ($latest_news as $news): ?>
                            <div class="news-item">
                                <div class="news-item-date">
                                    <?php echo date('d.m.Y', strtotime($news['created_at'])); ?>
                                </div>
                                <a href="article.php?slug=<?php echo urlencode($news['slug']); ?>" class="news-item-title">
                                    <?php echo htmlspecialchars($news['title']); ?>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p style="color: var(--text-light); font-style: italic;">Henüz haber bulunmuyor.</p>
                    <?php endif; ?>
                </aside>
            </div>
        </div>
    </main>

    <?php include 'includes/footer.php'; ?>

    <script>
        // Smooth scroll for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading animation
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.3s ease';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>
