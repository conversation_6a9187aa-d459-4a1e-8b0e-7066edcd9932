<?php
/**
 * GNG Makine - Ürünler Sayfası
 */

// Konfigürasyonu dahil et
require_once __DIR__ . '/config/config.php';

// Sayfa meta bilgileri
$page_title = "Ürünlerimiz - " . SITE_NAME;
$page_description = "GNG Makine ürün kataloğu. CNC torna, freze, kaynak makineleri ve endüstriyel ekipmanlar. Kaliteli ve güvenilir makine çözümleri.";
$page_keywords = "<PERSON>r<PERSON>n<PERSON>, CNC torna, freze, kaynak makinesi, endüstriyel makine, katalog";
$current_page = "products";

// Filtreleme parametreleri
$category_filter = $_GET['category'] ?? '';
$search_query = $_GET['search'] ?? '';
$sort_by = $_GET['sort'] ?? 'name';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 12;
$offset = ($page - 1) * $per_page;

try {
    // Kategorileri çek
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE status = 'active' ORDER BY sort_order ASC, name ASC");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Ürünler için WHERE koşullarını hazırla
    $where_conditions = ["p.status = 'active'"];
    $params = [];

    if (!empty($category_filter)) {
        $where_conditions[] = "c.slug = ?";
        $params[] = $category_filter;
    }

    if (!empty($search_query)) {
        $where_conditions[] = "(p.name LIKE ? OR p.description LIKE ? OR p.short_description LIKE ?)";
        $search_param = '%' . $search_query . '%';
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }

    $where_clause = implode(' AND ', $where_conditions);

    // Sıralama
    $order_clause = match($sort_by) {
        'name' => 'p.name ASC',
        'name_desc' => 'p.name DESC',
        'newest' => 'p.created_at DESC',
        'oldest' => 'p.created_at ASC',
        'featured' => 'p.featured DESC, p.name ASC',
        default => 'p.name ASC'
    };

    // Toplam ürün sayısını al
    $count_sql = "SELECT COUNT(*) as total
                  FROM products p
                  LEFT JOIN categories c ON p.category_id = c.id
                  WHERE $where_clause";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_products = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_products / $per_page);

    // Ürünleri çek
    $products_sql = "SELECT p.*, c.name as category_name, c.slug as category_slug
                     FROM products p
                     LEFT JOIN categories c ON p.category_id = c.id
                     WHERE $where_clause
                     ORDER BY $order_clause
                     LIMIT $per_page OFFSET $offset";
    $stmt = $pdo->prepare($products_sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Seçili kategori bilgisini al
    $selected_category = null;
    if (!empty($category_filter)) {
        foreach ($categories as $cat) {
            if ($cat['slug'] === $category_filter) {
                $selected_category = $cat;
                break;
            }
        }
    }

} catch (PDOException $e) {
    error_log("Products page error: " . $e->getMessage());
    $categories = [];
    $products = [];
    $total_products = 0;
    $total_pages = 0;
    $selected_category = null;
}

include 'includes/header.php';
?>

<style>
/* Ürünler Sayfası Özel Stilleri */
.page-header {
    background: linear-gradient(135deg, var(--text-dark), #34495e);
    color: var(--white);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('<?php echo ASSETS_URL; ?>/images/patterns/dots.png') repeat;
    opacity: 0.1;
    z-index: 1;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 20px;
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
    font-size: 1rem;
}

.breadcrumb a {
    color: var(--white);
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.breadcrumb a:hover {
    opacity: 1;
}

.breadcrumb span {
    opacity: 0.6;
}

/* Products Section */
.products-section {
    padding: 100px 0;
    background: var(--white);
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 50px;
    flex-wrap: wrap;
    gap: 20px;
}

.products-info h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.products-count {
    color: var(--text-light);
    font-size: 1rem;
}

.products-filters {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-dark);
}

.filter-select {
    padding: 10px 15px;
    border: 2px solid var(--bg-light);
    border-radius: 8px;
    font-size: 0.95rem;
    background: var(--white);
    color: var(--text-dark);
    min-width: 150px;
    transition: border-color 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.search-box {
    display: flex;
    align-items: center;
    background: var(--white);
    border: 2px solid var(--bg-light);
    border-radius: 8px;
    overflow: hidden;
    transition: border-color 0.3s ease;
}

.search-box:focus-within {
    border-color: var(--primary-color);
}

.search-input {
    padding: 10px 15px;
    border: none;
    outline: none;
    font-size: 0.95rem;
    min-width: 200px;
}

.search-btn {
    padding: 10px 15px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-btn:hover {
    background: var(--accent-color);
}

/* Categories Filter */
.categories-filter {
    background: var(--bg-light);
    padding: 30px 0;
    margin-bottom: 50px;
}

.categories-list {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.category-btn {
    padding: 12px 24px;
    background: var(--white);
    color: var(--text-dark);
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.category-btn:hover,
.category-btn.active {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.product-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.product-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.product-image {
    height: 220px;
    background: var(--bg-light);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-image .placeholder-icon {
    font-size: 3rem;
    color: var(--text-light);
}

.product-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--primary-color);
    color: var(--white);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-info {
    padding: 25px;
}

.product-category {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.product-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 12px;
    line-height: 1.3;
}

.product-description {
    color: var(--text-light);
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 20px;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.view-details {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: color 0.3s ease;
}

.view-details:hover {
    color: var(--accent-color);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 50px;
}

.pagination a,
.pagination span {
    padding: 12px 16px;
    border: 2px solid var(--bg-light);
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination a:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--white);
}

.pagination .current {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination .disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 80px 20px;
}

.empty-state i {
    font-size: 4rem;
    color: var(--text-light);
    margin-bottom: 20px;
}

.empty-state h3 {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.empty-state p {
    color: var(--text-light);
    margin-bottom: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header h1 {
        font-size: 2.2rem;
    }

    .products-header {
        flex-direction: column;
        align-items: stretch;
    }

    .products-filters {
        justify-content: center;
    }

    .filter-select,
    .search-input {
        min-width: auto;
    }

    .categories-list {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 10px;
    }

    .category-btn {
        white-space: nowrap;
        flex-shrink: 0;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .pagination {
        flex-wrap: wrap;
    }
}
</style>

<?php include 'includes/navbar.php'; ?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1>Ürünlerimiz</h1>
        <p>Endüstriyel kalitede makine çözümlerimizi keşfedin. CNC torna, freze, kaynak makineleri ve daha fazlası.</p>

        <div class="breadcrumb">
            <a href="<?php echo BASE_URL; ?>">Ana Sayfa</a>
            <span><i class="fas fa-chevron-right"></i></span>
            <?php if ($selected_category): ?>
                <a href="<?php echo BASE_URL; ?>/products.php">Ürünler</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span><?php echo htmlspecialchars($selected_category['name']); ?></span>
            <?php else: ?>
                <span>Ürünler</span>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Categories Filter -->
<?php if (!empty($categories)): ?>
<section class="categories-filter">
    <div class="container">
        <div class="categories-list">
            <a href="<?php echo BASE_URL; ?>/products.php"
               class="category-btn <?php echo empty($category_filter) ? 'active' : ''; ?>">
                Tüm Ürünler
            </a>
            <?php foreach ($categories as $category): ?>
                <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo urlencode($category['slug']); ?>"
                   class="category-btn <?php echo $category_filter === $category['slug'] ? 'active' : ''; ?>">
                    <?php echo htmlspecialchars($category['name']); ?>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Products Section -->
<section class="products-section">
    <div class="container">
        <!-- Products Header -->
        <div class="products-header">
            <div class="products-info">
                <h2>
                    <?php if ($selected_category): ?>
                        <?php echo htmlspecialchars($selected_category['name']); ?>
                    <?php elseif (!empty($search_query)): ?>
                        "<?php echo htmlspecialchars($search_query); ?>" için Arama Sonuçları
                    <?php else: ?>
                        Tüm Ürünler
                    <?php endif; ?>
                </h2>
                <p class="products-count"><?php echo $total_products; ?> ürün bulundu</p>
            </div>

            <div class="products-filters">
                <!-- Search -->
                <form method="GET" action="" class="search-box">
                    <?php if (!empty($category_filter)): ?>
                        <input type="hidden" name="category" value="<?php echo htmlspecialchars($category_filter); ?>">
                    <?php endif; ?>
                    <input type="text" name="search" class="search-input"
                           placeholder="Ürün ara..."
                           value="<?php echo htmlspecialchars($search_query); ?>">
                    <button type="submit" class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </form>

                <!-- Sort -->
                <div class="filter-group">
                    <label>Sırala</label>
                    <select class="filter-select" onchange="updateSort(this.value)">
                        <option value="name" <?php echo $sort_by === 'name' ? 'selected' : ''; ?>>A-Z</option>
                        <option value="name_desc" <?php echo $sort_by === 'name_desc' ? 'selected' : ''; ?>>Z-A</option>
                        <option value="newest" <?php echo $sort_by === 'newest' ? 'selected' : ''; ?>>En Yeni</option>
                        <option value="featured" <?php echo $sort_by === 'featured' ? 'selected' : ''; ?>>Öne Çıkanlar</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <?php if (!empty($products)): ?>
            <div class="products-grid">
                <?php foreach ($products as $product): ?>
                    <div class="product-card">
                        <div class="product-image">
                            <?php if ($product['main_image']): ?>
                                <img src="<?php echo UPLOADS_URL; ?>/products/<?php echo htmlspecialchars($product['main_image']); ?>"
                                     alt="<?php echo htmlspecialchars($product['name']); ?>">
                            <?php else: ?>
                                <i class="placeholder-icon fas fa-cog"></i>
                            <?php endif; ?>

                            <?php if ($product['featured']): ?>
                                <div class="product-badge">Öne Çıkan</div>
                            <?php endif; ?>
                        </div>

                        <div class="product-info">
                            <?php if ($product['category_name']): ?>
                                <div class="product-category"><?php echo htmlspecialchars($product['category_name']); ?></div>
                            <?php endif; ?>

                            <h3 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h3>

                            <p class="product-description">
                                <?php
                                $description = $product['short_description'] ?: $product['description'];
                                echo htmlspecialchars(substr($description, 0, 120)) . '...';
                                ?>
                            </p>

                            <div class="product-meta">
                                <a href="<?php echo BASE_URL; ?>/product-detail.php?slug=<?php echo urlencode($product['slug']); ?>"
                                   class="view-details">
                                    Detayları Gör <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php
                    $current_url = $_SERVER['REQUEST_URI'];
                    $url_parts = parse_url($current_url);
                    parse_str($url_parts['query'] ?? '', $query_params);

                    // Previous page
                    if ($page > 1):
                        $query_params['page'] = $page - 1;
                        $prev_url = '?' . http_build_query($query_params);
                    ?>
                        <a href="<?php echo $prev_url; ?>" class="prev-page">
                            <i class="fas fa-chevron-left"></i> Önceki
                        </a>
                    <?php else: ?>
                        <span class="prev-page disabled">
                            <i class="fas fa-chevron-left"></i> Önceki
                        </span>
                    <?php endif; ?>

                    <?php
                    // Page numbers
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);

                    for ($i = $start_page; $i <= $end_page; $i++):
                        $query_params['page'] = $i;
                        $page_url = '?' . http_build_query($query_params);
                    ?>
                        <?php if ($i == $page): ?>
                            <span class="current"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="<?php echo $page_url; ?>"><?php echo $i; ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>

                    <?php
                    // Next page
                    if ($page < $total_pages):
                        $query_params['page'] = $page + 1;
                        $next_url = '?' . http_build_query($query_params);
                    ?>
                        <a href="<?php echo $next_url; ?>" class="next-page">
                            Sonraki <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php else: ?>
                        <span class="next-page disabled">
                            Sonraki <i class="fas fa-chevron-right"></i>
                        </span>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <!-- Empty State -->
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <h3>Ürün Bulunamadı</h3>
                <p>
                    <?php if (!empty($search_query)): ?>
                        "<?php echo htmlspecialchars($search_query); ?>" araması için sonuç bulunamadı.
                    <?php elseif (!empty($category_filter)): ?>
                        Bu kategoride henüz ürün bulunmuyor.
                    <?php else: ?>
                        Henüz ürün eklenmemiş.
                    <?php endif; ?>
                </p>
                <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-primary">
                    Tüm Ürünleri Görüntüle
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>

<script>
function updateSort(sortValue) {
    const url = new URL(window.location);
    url.searchParams.set('sort', sortValue);
    window.location.href = url.toString();
}
</script>

<?php include 'includes/footer.php'; ?>
