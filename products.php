<?php
/**
 * GNG Makine - Ürünler Sayfası
 */

// Konfigürasyonu dahil et
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

// Sayfa meta bilgileri
$page_title = "Ürünler - " . SITE_NAME;
$page_description = "Endüstriyel makine ürünlerimizi keşfedin. CNC torna, freze, kaynak, kesim makineleri ve daha fazlası.";
$page_keywords = "endüstriyel makine, CNC torna, freze, kaynak makinesi, kesim makinesi, pres";
$current_page = "products";

// URL parametrelerini al
$category_slug = isset($_GET['category']) ? $_GET['category'] : '';
$search_query = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = 12;
$offset = ($page - 1) * $per_page;

// Breadcrumb
$breadcrumb = [
    ['title' => 'Ana Sayfa', 'url' => BASE_URL],
    ['title' => 'Ürünler', 'url' => '']
];

try {
    // Kategorileri çek
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE status = 'active' ORDER BY sort_order ASC");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Ürünler için SQL sorgusu oluştur
    $where_conditions = ["p.status = 'active'"];
    $params = [];

    if ($category_slug) {
        $where_conditions[] = "c.slug = ?";
        $params[] = $category_slug;

        // Kategori bilgisini al
        $stmt = $pdo->prepare("SELECT * FROM categories WHERE slug = ? AND status = 'active'");
        $stmt->execute([$category_slug]);
        $current_category = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($current_category) {
            $breadcrumb[] = ['title' => $current_category['name'], 'url' => ''];
            $page_title = $current_category['name'] . " - " . SITE_NAME;
        }
    }

    if ($search_query) {
        $where_conditions[] = "(p.name LIKE ? OR p.short_description LIKE ? OR p.description LIKE ?)";
        $search_param = '%' . $search_query . '%';
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }

    $where_clause = implode(' AND ', $where_conditions);

    // Toplam ürün sayısını al
    $count_sql = "SELECT COUNT(*) as total FROM products p
                  LEFT JOIN categories c ON p.category_id = c.id
                  WHERE $where_clause";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_products = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Ürünleri çek
    $sql = "SELECT p.*, c.name as category_name, c.slug as category_slug
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE $where_clause
            ORDER BY p.featured DESC, p.sort_order ASC, p.created_at DESC
            LIMIT $per_page OFFSET $offset";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Sayfalama hesaplamaları
    $total_pages = ceil($total_products / $per_page);

} catch (PDOException $e) {
    error_log("Products page error: " . $e->getMessage());
    $categories = [];
    $products = [];
    $total_products = 0;
    $total_pages = 1;
}

include 'includes/header.php';
?>

<style>
/* Ürünler Sayfası Özel Stilleri */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.products-hero {
    background: linear-gradient(135deg, var(--text-dark) 0%, #34495e 100%);
    padding: 120px 0 80px;
    color: var(--white);
    text-align: center;
}

.products-hero h1 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 20px;
}

.products-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.products-main {
    padding: 80px 0;
    background: var(--white);
}

.products-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 40px;
}

/* Sidebar */
.products-sidebar {
    background: var(--bg-light);
    padding: 30px;
    border-radius: 20px;
    height: fit-content;
    position: sticky;
    top: 100px;
}

.sidebar-section {
    margin-bottom: 40px;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-color);
}

.search-form {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.search-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: var(--accent-color);
}

.category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-item {
    margin-bottom: 10px;
}

.category-link {
    display: block;
    padding: 12px 15px;
    color: var(--text-light);
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.category-link:hover,
.category-link.active {
    background: var(--primary-color);
    color: var(--white);
    transform: translateX(5px);
}

/* Products Content */
.products-content {
    min-height: 500px;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.products-count {
    color: var(--text-light);
    font-size: 1rem;
}

.products-sort {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-select {
    padding: 8px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: var(--white);
    color: var(--text-dark);
    cursor: pointer;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.product-card {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.product-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.product-image {
    height: 220px;
    background: var(--bg-light);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-placeholder {
    font-size: 3rem;
    color: var(--primary-color);
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--primary-color);
    color: var(--white);
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-info {
    padding: 25px;
}

.product-category {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 8px;
}

.product-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 12px;
    line-height: 1.3;
}

.product-description {
    color: var(--text-light);
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
}

.product-btn {
    background: var(--primary-color);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.product-btn:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 60px;
}

.pagination a,
.pagination span {
    padding: 10px 15px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination a {
    background: var(--bg-light);
    color: var(--text-dark);
    border: 2px solid transparent;
}

.pagination a:hover {
    background: var(--primary-color);
    color: var(--white);
}

.pagination .current {
    background: var(--primary-color);
    color: var(--white);
}

.pagination .disabled {
    background: #f0f0f0;
    color: #ccc;
    cursor: not-allowed;
}

/* No Results */
.no-results {
    text-align: center;
    padding: 80px 20px;
    color: var(--text-light);
}

.no-results i {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .products-layout {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .products-sidebar {
        position: static;
        order: 2;
    }

    .products-header {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 5px;
    }
}
</style>

<?php include 'includes/navbar.php'; ?>