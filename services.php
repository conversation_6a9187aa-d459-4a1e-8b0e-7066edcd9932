<?php
/**
 * GNG Makine - Hizmetler Sayfası
 */

// Konfigürasyonu dahil et
require_once __DIR__ . '/config/config.php';

// Sayfa meta bilgileri
$page_title = "Hizmetlerimiz - " . SITE_NAME;
$page_description = "GNG Makine olarak sunduğumuz profesyonel hizmetler: Makine satışı, teknik destek, bakım-onarım, yedek parça ve danışmanlık hizmetleri.";
$page_keywords = "makine satışı, teknik destek, bakım onarım, yedek par<PERSON>, danışmanlık, GNG Makine";
$current_page = "services";

// Hizmetler verisi
$services = [
    [
        'id' => 1,
        'title' => 'Makine Satışı',
        'icon' => 'fas fa-shopping-cart',
        'description' => 'Geniş ürün yelpazemizle endüstriyel ihtiyaçlarınıza uygun makineleri sizlere sunuyoruz.',
        'features' => [
            'Yeni ve ikinci el makine seçenekleri',
            'Kaliteli ve güvenilir markalar',
            'Rekabetçi fiyatlar',
            'Finansman imkanları',
            'Garanti ve servis desteği'
        ],
        'image' => 'machine-sales.jpg'
    ],
    [
        'id' => 2,
        'title' => 'Teknik Destek',
        'icon' => 'fas fa-tools',
        'description' => 'Uzman teknik ekibimizle 7/24 teknik destek hizmeti sunuyoruz.',
        'features' => [
            '7/24 teknik destek hattı',
            'Uzman teknisyen desteği',
            'Hızlı çözüm odaklı yaklaşım',
            'Uzaktan destek imkanı',
            'Acil durum müdahalesi'
        ],
        'image' => 'technical-support.jpg'
    ],
    [
        'id' => 3,
        'title' => 'Bakım & Onarım',
        'icon' => 'fas fa-wrench',
        'description' => 'Makinelerinizin uzun ömürlü olması için profesyonel bakım ve onarım hizmetleri.',
        'features' => [
            'Periyodik bakım programları',
            'Arıza tespiti ve onarım',
            'Orijinal yedek parça kullanımı',
            'Kalibrasyon hizmetleri',
            'Performans optimizasyonu'
        ],
        'image' => 'maintenance.jpg'
    ],
    [
        'id' => 4,
        'title' => 'Yedek Parça',
        'icon' => 'fas fa-cogs',
        'description' => 'Geniş yedek parça stoğumuzla makinelerinizin kesintisiz çalışmasını sağlıyoruz.',
        'features' => [
            'Geniş yedek parça stoğu',
            'Orijinal ve muadil parçalar',
            'Hızlı tedarik süreci',
            'Kalite garantisi',
            'Uygun fiyat politikası'
        ],
        'image' => 'spare-parts.jpg'
    ],
    [
        'id' => 5,
        'title' => 'Danışmanlık',
        'icon' => 'fas fa-lightbulb',
        'description' => 'Endüstriyel süreçlerinizi optimize etmek için uzman danışmanlık hizmetleri.',
        'features' => [
            'Süreç analizi ve optimizasyon',
            'Makine seçim danışmanlığı',
            'Verimlilik artırma çözümleri',
            'Maliyet analizi',
            'Proje yönetimi desteği'
        ],
        'image' => 'consulting.jpg'
    ],
    [
        'id' => 6,
        'title' => 'Eğitim & Sertifikasyon',
        'icon' => 'fas fa-graduation-cap',
        'description' => 'Personellerinizin makine kullanımında uzmanlaşması için eğitim programları.',
        'features' => [
            'Makine kullanım eğitimleri',
            'Güvenlik eğitimleri',
            'Sertifikasyon programları',
            'Uygulamalı eğitim',
            'Sürekli eğitim desteği'
        ],
        'image' => 'training.jpg'
    ]
];

include 'includes/header.php';
?>

<style>
/* Hizmetler Sayfası Özel Stilleri */
.page-header {
    background: linear-gradient(135deg, var(--text-dark), #34495e);
    color: var(--white);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('<?php echo ASSETS_URL; ?>/images/patterns/dots.png') repeat;
    opacity: 0.1;
    z-index: 1;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 20px;
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
    font-size: 1rem;
}

.breadcrumb a {
    color: var(--white);
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.breadcrumb a:hover {
    opacity: 1;
}

.breadcrumb span {
    opacity: 0.6;
}

/* Services Section */
.services-section {
    padding: 100px 0;
    background: var(--white);
}

.services-intro {
    text-align: center;
    margin-bottom: 80px;
}

.services-intro h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 25px;
}

.services-intro p {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
}

.service-card {
    background: var(--white);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: var(--shadow);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 2rem;
    margin-bottom: 25px;
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
}

.service-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.service-description {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 25px;
    font-size: 1rem;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    color: var(--text-dark);
    font-size: 0.95rem;
}

.service-features li::before {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    flex-shrink: 0;
}

/* CTA Section */
.cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Process Section */
.process-section {
    padding: 100px 0;
    background: var(--bg-light);
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.process-step {
    text-align: center;
    position: relative;
}

.process-step::after {
    content: '';
    position: absolute;
    top: 40px;
    right: -20px;
    width: 40px;
    height: 2px;
    background: var(--primary-color);
    display: none;
}

.process-step:not(:last-child)::after {
    display: block;
}

.step-number {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 20px;
    position: relative;
    z-index: 2;
}

.step-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.step-description {
    color: var(--text-light);
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header h1 {
        font-size: 2.2rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .service-card {
        padding: 30px 25px;
    }

    .process-steps {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .process-step::after {
        display: none;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}
</style>

<?php include 'includes/navbar.php'; ?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1>Hizmetlerimiz</h1>
        <p>Endüstriyel makine sektöründe kapsamlı hizmet yelpazesi ile yanınızdayız. Satıştan sonraya kadar her aşamada profesyonel destek.</p>

        <div class="breadcrumb">
            <a href="<?php echo BASE_URL; ?>">Ana Sayfa</a>
            <span><i class="fas fa-chevron-right"></i></span>
            <span>Hizmetlerimiz</span>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="services-section">
    <div class="container">
        <div class="services-intro">
            <h2>Kapsamlı Hizmet Anlayışımız</h2>
            <p>15 yılı aşkın tecrübemizle endüstriyel makine sektöründe A'dan Z'ye tüm ihtiyaçlarınızı karşılıyoruz. Satış öncesinden satış sonrasına kadar kesintisiz hizmet garantisi.</p>
        </div>

        <div class="services-grid">
            <?php foreach ($services as $service): ?>
            <div class="service-card">
                <div class="service-icon">
                    <i class="<?php echo $service['icon']; ?>"></i>
                </div>

                <h3 class="service-title"><?php echo htmlspecialchars($service['title']); ?></h3>

                <p class="service-description"><?php echo htmlspecialchars($service['description']); ?></p>

                <ul class="service-features">
                    <?php foreach ($service['features'] as $feature): ?>
                    <li><?php echo htmlspecialchars($feature); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Process Section -->
<section class="process-section">
    <div class="container">
        <div class="section-header">
            <h2>Hizmet Sürecimiz</h2>
            <p>Müşteri memnuniyeti odaklı yaklaşımımızla her projede aynı kaliteli hizmeti sunuyoruz.</p>
        </div>

        <div class="process-steps">
            <div class="process-step">
                <div class="step-number">1</div>
                <h3 class="step-title">İhtiyaç Analizi</h3>
                <p class="step-description">Uzman ekibimiz ihtiyaçlarınızı detaylı olarak analiz eder ve size en uygun çözümü belirler.</p>
            </div>

            <div class="process-step">
                <div class="step-number">2</div>
                <h3 class="step-title">Teklif Hazırlama</h3>
                <p class="step-description">Analiz sonuçlarına göre detaylı ve şeffaf fiyat teklifimizi hazırlayıp sunuyoruz.</p>
            </div>

            <div class="process-step">
                <div class="step-number">3</div>
                <h3 class="step-title">Uygulama</h3>
                <p class="step-description">Onaylanan teklif doğrultusunda hizmetimizi profesyonel ekibimizle hayata geçiriyoruz.</p>
            </div>

            <div class="process-step">
                <div class="step-number">4</div>
                <h3 class="step-title">Takip & Destek</h3>
                <p class="step-description">Hizmet sonrası sürekli takip ve destek ile müşteri memnuniyetinizi garanti ediyoruz.</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <h2>Hizmetlerimiz Hakkında Bilgi Alın</h2>
            <p>Endüstriyel makine ihtiyaçlarınız için uzman ekibimizle iletişime geçin. Size özel çözümler sunmak için buradayız.</p>

            <div class="cta-buttons">
                <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-secondary">
                    <i class="fas fa-phone"></i>
                    Hemen İletişime Geçin
                </a>
                <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-outline">
                    <i class="fas fa-search"></i>
                    Ürünlerimizi İnceleyin
                </a>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>