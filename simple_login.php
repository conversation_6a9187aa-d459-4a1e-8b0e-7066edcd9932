<?php
session_start();
require_once 'config/config.php';

$error_message = '';
$debug_info = [];

// Form gönderildiğinde
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    $debug_info[] = "Form gönderildi";
    $debug_info[] = "Username: " . $username;
    $debug_info[] = "Password length: " . strlen($password);

    if (empty($username) || empty($password)) {
        $error_message = 'Kullanıcı adı ve şifre gereklidir.';
        $debug_info[] = "Boş alan hatası";
    } else {
        try {
            $debug_info[] = "Veritabanı sorgusu başlatılıyor...";
            
            // Veritabanından admin kullanıcıyı getir
            $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE username = ? AND status = 'active'");
            $stmt->execute([$username]);
            $admin = $stmt->fetch();
            
            $debug_info[] = "Sorgu tamamlandı";
            
            if ($admin) {
                $debug_info[] = "Kullanıcı bulundu: " . $admin['username'];
                $debug_info[] = "Kullanıcı ID: " . $admin['id'];
                $debug_info[] = "Kullanıcı durumu: " . $admin['status'];
                
                // Şifre kontrolü
                $password_check = password_verify($password, $admin['password']);
                $debug_info[] = "Şifre kontrolü: " . ($password_check ? 'BAŞARILI' : 'BAŞARISIZ');
                
                if ($password_check) {
                    $debug_info[] = "Giriş başarılı - Session ayarlanıyor";
                    
                    // Giriş başarılı
                    $_SESSION['admin_logged_in'] = true;
                    $_SESSION['admin_user_id'] = $admin['id'];
                    $_SESSION['admin_id'] = $admin['id'];
                    $_SESSION['admin_username'] = $admin['username'];
                    $_SESSION['admin_name'] = $admin['full_name'];
                    $_SESSION['admin_role'] = $admin['role'];

                    $debug_info[] = "Session ayarlandı";
                    
                    // Son giriş zamanını güncelle
                    $update_stmt = $pdo->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
                    $update_stmt->execute([$admin['id']]);
                    
                    $debug_info[] = "Son giriş zamanı güncellendi";
                    $debug_info[] = "Admin paneline yönlendiriliyor...";
                    
                    echo "<h2>✅ GİRİŞ BAŞARILI!</h2>";
                    echo "<p>Admin paneline yönlendiriliyorsunuz...</p>";
                    echo "<p><a href='admin/index.php'>Admin Paneline Git</a></p>";
                    
                    echo "<h3>Debug Bilgileri:</h3>";
                    echo "<ul>";
                    foreach ($debug_info as $info) {
                        echo "<li>" . htmlspecialchars($info) . "</li>";
                    }
                    echo "</ul>";
                    
                    echo "<script>setTimeout(function(){ window.location.href = 'admin/index.php'; }, 3000);</script>";
                    exit;
                } else {
                    $error_message = 'Şifre yanlış!';
                    $debug_info[] = "Stored hash: " . substr($admin['password'], 0, 20) . "...";
                }
            } else {
                $error_message = 'Kullanıcı bulunamadı!';
                $debug_info[] = "Kullanıcı bulunamadı";
                
                // Tüm kullanıcıları listele
                $all_stmt = $pdo->prepare("SELECT username, status FROM admin_users");
                $all_stmt->execute();
                $all_users = $all_stmt->fetchAll();
                $debug_info[] = "Veritabanındaki kullanıcılar: " . print_r($all_users, true);
            }
        } catch (PDOException $e) {
            $error_message = 'Veritabanı hatası: ' . $e->getMessage();
            $debug_info[] = "PDO Hatası: " . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basit Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
        button { width: 100%; padding: 12px; background: #F9B233; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }
        button:hover { background: #e6a429; }
        .error { background: #fee; color: #c53030; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
        .debug { background: #f0f0f0; padding: 15px; border-radius: 5px; margin-top: 20px; }
        .debug h3 { margin-top: 0; }
        .debug ul { margin: 10px 0; }
        .debug li { margin: 5px 0; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h2>🔐 Basit Login Test</h2>
        <p>Bu sayfa login işlemini test etmek için oluşturuldu.</p>
        
        <?php if ($error_message): ?>
            <div class="error">
                ❌ <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <div class="form-group">
                <label for="username">Kullanıcı Adı:</label>
                <input type="text" id="username" name="username" value="gngadmin" required>
            </div>

            <div class="form-group">
                <label for="password">Şifre:</label>
                <input type="password" id="password" name="password" value="GNG2024!" required>
            </div>

            <button type="submit">Giriş Yap</button>
        </form>
        
        <p style="margin-top: 20px; text-align: center;">
            <a href="admin/login.php">Normal Login Sayfasına Dön</a> | 
            <a href="debug_login.php">Debug Sayfası</a>
        </p>

        <?php if (!empty($debug_info)): ?>
            <div class="debug">
                <h3>🔍 Debug Bilgileri:</h3>
                <ul>
                    <?php foreach ($debug_info as $info): ?>
                        <li><?php echo htmlspecialchars($info); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
