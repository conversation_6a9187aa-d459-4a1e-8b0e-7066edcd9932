<?php
require_once 'config/config.php';

echo "<h2>Veritabanı Bağlantı Testi</h2>";

try {
    // Veritabanı bağlantısını test et
    echo "<p>✅ Veritabanı bağlantısı başarılı!</p>";
    
    // Admin kullanıcılarını listele
    $stmt = $pdo->prepare("SELECT id, username, email, full_name, role, status, created_at FROM admin_users");
    $stmt->execute();
    $admins = $stmt->fetchAll();
    
    echo "<h3>Admin Kullanıcıları:</h3>";
    if (empty($admins)) {
        echo "<p>❌ Hiç admin kullanıcısı bulunamadı!</p>";
        echo "<p><a href='database/create_admin.php'>Admin kullanıcısı oluştur</a></p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Kullanıcı Adı</th><th>E-posta</th><th>Ad Soyad</th><th>Rol</th><th>Durum</th><th>Oluşturulma</th></tr>";
        foreach ($admins as $admin) {
            echo "<tr>";
            echo "<td>" . $admin['id'] . "</td>";
            echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
            echo "<td>" . htmlspecialchars($admin['email']) . "</td>";
            echo "<td>" . htmlspecialchars($admin['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($admin['role']) . "</td>";
            echo "<td>" . htmlspecialchars($admin['status']) . "</td>";
            echo "<td>" . $admin['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Tabloları kontrol et
    echo "<h3>Veritabanı Tabloları:</h3>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p>❌ Hiç tablo bulunamadı! Veritabanını import etmeniz gerekiyor.</p>";
        echo "<p>1. phpMyAdmin'e gidin</p>";
        echo "<p>2. 'gng_makine' veritabanını seçin</p>";
        echo "<p>3. 'database/gng_makine.sql' dosyasını import edin</p>";
    } else {
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>✅ " . $table . "</li>";
        }
        echo "</ul>";
    }
    
} catch (PDOException $e) {
    echo "<p>❌ Veritabanı hatası: " . $e->getMessage() . "</p>";
    echo "<p>Lütfen şunları kontrol edin:</p>";
    echo "<ul>";
    echo "<li>XAMPP MySQL servisi çalışıyor mu?</li>";
    echo "<li>'gng_makine' veritabanı oluşturuldu mu?</li>";
    echo "<li>Veritabanı bağlantı bilgileri doğru mu?</li>";
    echo "</ul>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
