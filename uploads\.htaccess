# Prevent execution of PHP files in uploads directory
<FilesMatch "\.php$">
    Order Allow,<PERSON>y
    <PERSON> from all
</FilesMatch>

# Only allow specific file types
<FilesMatch "\.(jpg|jpeg|png|gif|webp|pdf|doc|docx|xls|xlsx|txt|zip|rar)$">
    Order Allow,<PERSON><PERSON>
    Allow from all
</FilesMatch>

# Prevent access to other file types
<FilesMatch "\.">
    Order Allow,<PERSON>y
    Deny from all
</FilesMatch>

# Allow the specific file types (override the deny all)
<FilesMatch "\.(jpg|jpeg|png|gif|webp|pdf|doc|docx|xls|xlsx|txt|zip|rar)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Set proper MIME types
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/webp .webp
    AddType application/pdf .pdf
    AddType application/msword .doc
    AddType application/vnd.openxmlformats-officedocument.wordprocessingml.document .docx
    AddType application/vnd.ms-excel .xls
    AddType application/vnd.openxmlformats-officedocument.spreadsheetml.sheet .xlsx
    AddType text/plain .txt
    AddType application/zip .zip
    AddType application/x-rar-compressed .rar
</IfModule>

# Prevent hotlinking
RewriteEngine On
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?gngmakine\.com [NC]
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?localhost [NC]
RewriteRule \.(jpg|jpeg|png|gif|webp)$ - [NC,F,L]
